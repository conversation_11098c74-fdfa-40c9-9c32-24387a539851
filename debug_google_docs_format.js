// Debug Google Docs format field processing
console.log("=== DEBUG: Google Docs Format Field Issue ===");

// The actual schema from saved_config.json for the format field
const googleDocsFormatSchema = {
    "anyOf": [
        {
            "enum": ["plain", "html", "markdown"],
            "type": "string"
        },
        {
            "type": "null"
        }
    ],
    "default": "plain",
    "title": "Format"
};

console.log("Google Docs format schema:", JSON.stringify(googleDocsFormatSchema, null, 2));

// Simulate the MCPMarketplaceInputGenerator processing
console.log("\n=== Testing MCPMarketplaceInputGenerator Logic ===");

// The MCPMarketplaceInputGenerator does NOT use DynamicInputTypeMapper directly
// Instead, it processes the inputs from node.data.definition.inputs which come from the server

console.log("Key insight: MCPMarketplaceInputGenerator processes node.data.definition.inputs");
console.log("These inputs are likely already converted from the schema by the backend");

// However, the issue might be that the backend is not properly converting the anyOf enum schema
// Let's simulate what the backend might be sending

const possibleBackendInput1 = {
    name: "format",
    display_name: "Format",
    input_type: "string", // Wrong - should be dropdown
    info: "Format for the document content",
    value: "plain",
    required: false,
    // Missing options array
};

const possibleBackendInput2 = {
    name: "format", 
    display_name: "Format",
    input_type: "dropdown", // Correct
    info: "Format for the document content", 
    value: "plain",
    required: false,
    options: ["plain", "html", "markdown"] // Correct
};

console.log("\nPossible backend input (WRONG):", JSON.stringify(possibleBackendInput1, null, 2));
console.log("\nPossible backend input (CORRECT):", JSON.stringify(possibleBackendInput2, null, 2));

// The issue is likely one of these:
console.log("\n=== Possible Issues ===");
console.log("1. Backend not converting anyOf enum schema to dropdown input_type");
console.log("2. Backend not setting options array for dropdown");
console.log("3. Frontend not properly handling anyOf structures when they exist");
console.log("4. Google Docs tool not being recognized as MCP Marketplace component");

// To debug, we need to check:
console.log("\n=== Debugging Steps Needed ===");
console.log("1. Check if Google Docs node has data.type === 'mcp'");
console.log("2. Check node.data.definition.inputs for format field structure");
console.log("3. Check if format field has input_type === 'dropdown' and options array");
console.log("4. Check browser console for any conversion errors");
console.log("5. Check if DynamicInputRenderer is receiving correct mapping");

console.log("\n=== END DEBUG ===");