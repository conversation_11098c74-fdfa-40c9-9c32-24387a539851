#!/usr/bin/env python3
"""
Test script for the Regex Extractor Component implementation.
This script tests various scenarios from the PRD examples.
"""

import asyncio
import sys
import os

# Add the project paths to Python path
sys.path.append('/Users/<USER>/Desktop/ruh_ai/backend/workflow-service')
sys.path.append('/Users/<USER>/Desktop/ruh_ai/backend/node-executor-service')

try:
    # Test workflow-service component
    from app.components.processing.regex_extractor import RegexExtractorComponent as WorkflowRegexComponent
    print("✅ Successfully imported workflow-service RegexExtractorComponent")
except ImportError as e:
    print(f"❌ Failed to import workflow-service component: {e}")
    sys.exit(1)

try:
    # Test node-executor-service component
    from app.components.regex_extractor_component import RegexExtractorComponent as NodeExecutorRegexComponent
    print("✅ Successfully imported node-executor-service RegexExtractorComponent")
except ImportError as e:
    print(f"❌ Failed to import node-executor-service component: {e}")
    sys.exit(1)


async def test_node_executor_component():
    """Test the node-executor-service component with various scenarios."""
    print("\n🧪 Testing Node Executor Service Component...")
    
    component = NodeExecutorRegexComponent()
    
    # Test cases from the PRD
    test_cases = [
        {
            "name": "Extract Email",
            "payload": {
                "tool_parameters": {
                    "input_text": "Contact <NAME_EMAIL>",
                    "regex_pattern": r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
                    "extraction_mode": "first_match",
                    "output_format": "full_match"
                },
                "request_id": "test_1"
            },
            "expected_matches": ["<EMAIL>"]
        },
        {
            "name": "Extract All Hashtags",
            "payload": {
                "tool_parameters": {
                    "input_text": "#automation #nocode is the future",
                    "regex_pattern": r"#(\w+)",
                    "extraction_mode": "all_matches",
                    "output_format": "first_capture_group"
                },
                "request_id": "test_2"
            },
            "expected_matches": ["automation", "nocode"]
        },
        {
            "name": "Extract Invoice Number",
            "payload": {
                "tool_parameters": {
                    "input_text": "Your invoice is INV-2023-9451.",
                    "regex_pattern": r"INV-\d{4}-\d{4}",
                    "extraction_mode": "first_match",
                    "output_format": "full_match"
                },
                "request_id": "test_3"
            },
            "expected_matches": ["INV-2023-9451"]
        },
        {
            "name": "Extract Price with Capture Group",
            "payload": {
                "tool_parameters": {
                    "input_text": "Total: $49.99",
                    "regex_pattern": r"\$(\d+\.\d{2})",
                    "extraction_mode": "first_match",
                    "output_format": "first_capture_group"
                },
                "request_id": "test_4"
            },
            "expected_matches": ["49.99"]
        },
        {
            "name": "Parse URL Parts with Named Groups",
            "payload": {
                "tool_parameters": {
                    "input_text": "https://app.myservice.io/dashboard",
                    "regex_pattern": r"https?://(?P<domain>[^/]+)/(?P<path>.*)",
                    "extraction_mode": "first_match",
                    "output_format": "named_groups"
                },
                "request_id": "test_5"
            },
            "expected_matches": [{"domain": "app.myservice.io", "path": "dashboard"}]
        },
        {
            "name": "Case Insensitive Match",
            "payload": {
                "tool_parameters": {
                    "input_text": "The API uses REST.",
                    "regex_pattern": r"\b([A-Z]{2,})\b",
                    "extraction_mode": "all_matches",
                    "output_format": "first_capture_group",
                    "case_insensitive": True
                },
                "request_id": "test_6"
            },
            "expected_matches": ["API", "REST"]
        },
        {
            "name": "No Match - Continue Empty",
            "payload": {
                "tool_parameters": {
                    "input_text": "This text has no numbers",
                    "regex_pattern": r"\d+",
                    "extraction_mode": "first_match",
                    "output_format": "full_match",
                    "on_no_match": "continue_empty"
                },
                "request_id": "test_7"
            },
            "expected_matches": []
        }
    ]
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n  Test {i}: {test_case['name']}")
        
        try:
            # Validate payload
            validation_result = await component.validate(test_case["payload"])
            if not validation_result.is_valid:
                print(f"    ❌ Validation failed: {validation_result.error_message}")
                failed += 1
                continue
            
            # Process payload
            result = await component.process(test_case["payload"])
            
            if result["status"] == "error":
                print(f"    ❌ Processing failed: {result['error']}")
                failed += 1
                continue
            
            # Check results
            actual_matches = result["matches"]
            expected_matches = test_case["expected_matches"]
            
            if actual_matches == expected_matches:
                print(f"    ✅ Passed - Matches: {actual_matches}")
                passed += 1
            else:
                print(f"    ❌ Failed - Expected: {expected_matches}, Got: {actual_matches}")
                failed += 1
                
        except Exception as e:
            print(f"    ❌ Exception: {str(e)}")
            failed += 1
    
    print(f"\n📊 Node Executor Tests: {passed} passed, {failed} failed")
    return failed == 0


def test_workflow_component():
    """Test the workflow-service component schema."""
    print("\n🧪 Testing Workflow Service Component...")
    
    try:
        component = WorkflowRegexComponent()
        definition = component.get_definition()
        
        print(f"  ✅ Component Name: {definition['name']}")
        print(f"  ✅ Display Name: {definition['display_name']}")
        print(f"  ✅ Category: {definition['category']}")
        print(f"  ✅ Inputs: {len(definition['inputs'])}")
        print(f"  ✅ Outputs: {len(definition['outputs'])}")
        
        # Check required inputs
        input_names = [inp['name'] for inp in definition['inputs']]
        required_inputs = ['input_text', 'regex_pattern']
        
        for req_input in required_inputs:
            if req_input in input_names:
                print(f"  ✅ Required input '{req_input}' found")
            else:
                print(f"  ❌ Required input '{req_input}' missing")
                return False
        
        # Check outputs
        output_names = [out['name'] for out in definition['outputs']]
        required_outputs = ['matches', 'first_match', 'match_count', 'is_match_found']
        
        for req_output in required_outputs:
            if req_output in output_names:
                print(f"  ✅ Required output '{req_output}' found")
            else:
                print(f"  ❌ Required output '{req_output}' missing")
                return False
        
        print("📊 Workflow Component Tests: All passed")
        return True
        
    except Exception as e:
        print(f"❌ Workflow component test failed: {str(e)}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Testing Regex Extractor Component Implementation")
    print("=" * 60)
    
    # Test workflow component
    workflow_success = test_workflow_component()
    
    # Test node executor component
    node_executor_success = await test_node_executor_component()
    
    print("\n" + "=" * 60)
    if workflow_success and node_executor_success:
        print("🎉 All tests passed! The Regex Extractor implementation is working correctly.")
        return 0
    else:
        print("💥 Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)