ith workflow ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************
2025-07-22 17:29:13 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************
2025-07-22 17:29:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'workflow_id': '41001c48-11a8-4d16-be64-0e016e6a9021', 'status': 'Initialized', 'message': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-22 17:29:13 - State<PERSON>anager - INFO - Workflow initialized with multiple initial transitions: ['transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************']
2025-07-22 17:29:13 - StateManager - DEBUG - State: pending={'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}, waiting=set(), completed=set()
2025-07-22 17:29:13 - EnhancedWorkflowEngine - INFO - Initializing workflow with multiple initial transitions: ['transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************']
2025-07-22 17:29:13 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}
2025-07-22 17:29:14 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:e7b14cdb-eb75-44f1-8c45-400e868670c9-****************'
2025-07-22 17:29:14 - RedisManager - DEBUG - Set key 'workflow_state:e7b14cdb-eb75-44f1-8c45-400e868670c9-****************' with TTL of 600 seconds
2025-07-22 17:29:14 - StateManager - INFO - Workflow state saved to Redis for workflow ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************. Will be archived to PostgreSQL when Redis key expires.
2025-07-22 17:29:14 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-22 17:29:14 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-22 17:29:14 - StateManager - INFO - Cleared 2 pending transitions: {'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}
2025-07-22 17:29:14 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-22 17:29:14 - StateManager - INFO - Terminated: False
2025-07-22 17:29:14 - StateManager - INFO - Pending transitions (0): []
2025-07-22 17:29:14 - StateManager - INFO - Waiting transitions (0): []
2025-07-22 17:29:14 - StateManager - INFO - Completed transitions (0): []
2025-07-22 17:29:14 - StateManager - INFO - Results stored for 0 transitions
2025-07-22 17:29:14 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-22 17:29:14 - StateManager - INFO - Workflow status: inactive
2025-07-22 17:29:14 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-22 17:29:14 - StateManager - INFO - Workflow status: inactive
2025-07-22 17:29:14 - StateManager - INFO - Workflow paused: False
2025-07-22 17:29:14 - StateManager - INFO - ==============================
2025-07-22 17:29:14 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-07-22 17:29:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id e7b14cdb-eb75-44f1-8c45-400e868670c9-****************):
2025-07-22 17:29:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-22 17:29:14 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=initial, execution_type=Components)
2025-07-22 17:29:14 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-22 17:29:14 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-07-22 17:29:14 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-22 17:29:14 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-22 17:29:14 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-22 17:29:14 - TransitionHandler - DEBUG - 🔧 PARALLEL DEBUG: input_data_configs for transition-MergeDataComponent-*************: [{'from_transition_id': 'transition-DelayComponent-*************', 'source_node_id': 'Wait / Delay', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-DelayComponent-*************', 'source_handle_id': 'output', 'target_handle_id': 'main_input', 'edge_id': 'reactflow__edge-DelayComponent-*************output-MergeDataComponent-*************main_input'}]}]
2025-07-22 17:29:16 - StateManager - DEBUG - No result found in Redis for transition transition-DelayComponent-*************. Trying PostgreSQL next.
2025-07-22 17:29:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-22 17:29:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-07-22 17:29:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-07-22 17:29:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-22 17:29:18 - PostgresManager - DEBUG - No result found for transition transition-DelayComponent-************* in correlation e7b14cdb-eb75-44f1-8c45-400e868670c9-****************
2025-07-22 17:29:18 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-DelayComponent-*************. Trying in-memory next.
2025-07-22 17:29:18 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-DelayComponent-*************
2025-07-22 17:29:18 - TransitionHandler - WARNING - 🔧 PARALLEL DEBUG: No result found for from_transition_id transition-DelayComponent-************* in state manager
2025-07-22 17:29:18 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-07-22 17:29:18 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 4 fields (20 null/empty fields removed)
2025-07-22 17:29:18 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'branch1', 'num_additional_inputs': 0, 'merge_strategy': 'Structured Compose', 'output_key_1': 'hello'}
2025-07-22 17:29:18 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': 'branch1', 'num_additional_inputs': 0, 'merge_strategy': 'Structured Compose', 'output_key_1': 'hello'}
2025-07-22 17:29:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id e7b14cdb-eb75-44f1-8c45-400e868670c9-****************):
2025-07-22 17:29:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server Merge Data', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-22 17:29:18 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: ed8ee73c-7f3f-4f2b-b1ee-50779817f331) using provided producer.
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Added correlation_id e7b14cdb-eb75-44f1-8c45-400e868670c9-**************** to payload
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Added node_label Merge Data to payload
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': 'branch1', 'num_additional_inputs': 0, 'merge_strategy': 'Structured Compose', 'output_key_1': 'hello'}, 'request_id': 'ed8ee73c-7f3f-4f2b-b1ee-50779817f331', 'correlation_id': 'e7b14cdb-eb75-44f1-8c45-400e868670c9-****************', 'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data'}
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Request ed8ee73c-7f3f-4f2b-b1ee-50779817f331 sent successfully using provided producer.
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Waiting indefinitely for result for request ed8ee73c-7f3f-4f2b-b1ee-50779817f331...
2025-07-22 17:29:18 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-22 17:29:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id e7b14cdb-eb75-44f1-8c45-400e868670c9-****************):
2025-07-22 17:29:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-07-22 17:29:18 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=initial, execution_type=Components)
2025-07-22 17:29:18 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-22 17:29:18 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-22 17:29:18 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-22 17:29:18 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-22 17:29:18 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-22 17:29:18 - TransitionHandler - DEBUG - 🔧 PARALLEL DEBUG: input_data_configs for transition-CombineTextComponent-*************: []
2025-07-22 17:29:18 - TransitionHandler - WARNING - 🔧 PARALLEL DEBUG: Empty input_data_configs for transition-CombineTextComponent-*************
2025-07-22 17:29:18 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-22 17:29:18 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-07-22 17:29:18 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'branch1', 'num_additional_inputs': 1, 'input_1': 'hello1'}
2025-07-22 17:29:18 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'branch1', 'num_additional_inputs': 1, 'input_1': 'hello1'}
2025-07-22 17:29:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id e7b14cdb-eb75-44f1-8c45-400e868670c9-****************):
2025-07-22 17:29:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_label': 'branch 1', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server branch 1', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-07-22 17:29:18 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 6ff82027-140a-49f7-81b4-47cc3def7377) using provided producer.
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Added correlation_id e7b14cdb-eb75-44f1-8c45-400e868670c9-**************** to payload
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Added node_label branch 1 to payload
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'branch1', 'num_additional_inputs': 1, 'input_1': 'hello1'}, 'request_id': '6ff82027-140a-49f7-81b4-47cc3def7377', 'correlation_id': 'e7b14cdb-eb75-44f1-8c45-400e868670c9-****************', 'transition_id': 'transition-CombineTextComponent-*************', 'node_label': 'branch 1'}
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Request 6ff82027-140a-49f7-81b4-47cc3def7377 sent successfully using provided producer.
2025-07-22 17:29:18 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 6ff82027-140a-49f7-81b4-47cc3def7377...
2025-07-22 17:29:18 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1440, corr_id: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************
2025-07-22 17:29:19 - NodeExecutor - DEBUG - Result consumer received message: Offset=1776
2025-07-22 17:29:19 - NodeExecutor - DEBUG - Received valid result for request_id ed8ee73c-7f3f-4f2b-b1ee-50779817f331
2025-07-22 17:29:19 - NodeExecutor - DEBUG - Result consumer received message: Offset=1777
2025-07-22 17:29:19 - NodeExecutor - DEBUG - Received valid result for request_id 6ff82027-140a-49f7-81b4-47cc3def7377
2025-07-22 17:29:19 - NodeExecutor - INFO - Result received for request ed8ee73c-7f3f-4f2b-b1ee-50779817f331.
2025-07-22 17:29:19 - TransitionHandler - INFO - Execution result from Components executor: {
  "hello": "branch1"
}
2025-07-22 17:29:19 - TransitionHandler - INFO - Checking execution result for errors: {
  "hello": "branch1"
}
2025-07-22 17:29:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id e7b14cdb-eb75-44f1-8c45-400e868670c9-****************):
2025-07-22 17:29:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': {'hello': 'branch1'}, 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'raw_result': {'hello': 'branch1'}, 'approval_required': False}
2025-07-22 17:29:19 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'result': {'result': {'hello': 'branch1'}}, 'status': 'completed', 'timestamp': 1753185559.067182}}
2025-07-22 17:29:19 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-07-22 17:29:19 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-07-22 17:29:19 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-22 17:29:19 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-22 17:29:19 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************'}
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'dict'>
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔀 Execution result keys: ['hello']
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-07-22 17:29:19 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 5.00 seconds
2025-07-22 17:29:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id e7b14cdb-eb75-44f1-8c45-400e868670c9-****************):
2025-07-22 17:29:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'result': 'Completed transition in 5.00 seconds', 'message': 'Transition completed in 5.00 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: []
2025-07-22 17:29:19 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-07-22 17:29:19 - NodeExecutor - INFO - Result received for request 6ff82027-140a-49f7-81b4-47cc3def7377.
2025-07-22 17:29:19 - TransitionHandler - INFO - Execution result from Components executor: "branch1 hello1"
2025-07-22 17:29:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id e7b14cdb-eb75-44f1-8c45-400e868670c9-****************):
2025-07-22 17:29:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_label': 'branch 1', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'branch1 hello1', 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'raw_result': 'branch1 hello1', 'approval_required': False}
2025-07-22 17:29:19 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_label': 'branch 1', 'tool_name': 'CombineTextComponent', 'result': {'result': 'branch1 hello1'}, 'status': 'completed', 'timestamp': 1753185559.884352}}
2025-07-22 17:29:20 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-07-22 17:29:20 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-07-22 17:29:20 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-22 17:29:20 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-22 17:29:20 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-DelayComponent-*************']
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-DelayComponent-*************
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-DelayComponent-*************']
2025-07-22 17:29:20 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.41 seconds
2025-07-22 17:29:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id e7b14cdb-eb75-44f1-8c45-400e868670c9-****************):
2025-07-22 17:29:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'result': 'Completed transition in 2.41 seconds', 'message': 'Transition completed in 2.41 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-DelayComponent-*************']
2025-07-22 17:29:20 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-22 17:29:20 - EnhancedWorkflowEngine - DEBUG - Results: [[], ['transition-DelayComponent-*************']]
2025-07-22 17:29:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-*************: []
2025-07-22 17:29:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-22 17:29:20 - StateManager - INFO - End transition completed: transition-MergeDataComponent-************* (1/1)
2025-07-22 17:29:20 - StateManager - INFO - All end transitions completed - workflow can terminate
2025-07-22 17:29:20 - EnhancedWorkflowEngine - INFO - All end transitions completed - terminating workflow
2025-07-22 17:29:20 - StateManager - INFO - Workflow terminated flag set to: True
2025-07-22 17:29:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-CombineTextComponent-*************: ['transition-DelayComponent-*************']
2025-07-22 17:29:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-22 17:29:20 - EnhancedWorkflowEngine - INFO - Transition transition-CombineTextComponent-************* completed successfully: 1 next transitions
2025-07-22 17:29:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-DelayComponent-*************']
2025-07-22 17:29:20 - EnhancedWorkflowEngine - INFO - Workflow is marked as terminated, ending workflow execution.
2025-07-22 17:29:20 - StateManager - DEBUG - Workflow active: False (terminated=True, pending=empty, waiting=empty)
2025-07-22 17:29:20 - EnhancedWorkflowEngine - INFO - Workflow execution completed.
2025-07-22 17:29:20 - KafkaWorkflowConsumer - WARNING - No running workflow found for workflow_id: 41001c48-11a8-4d16-be64-0e016e6a9021
2025-07-22 17:29:20 - KafkaWorkflowConsumer - INFO - Workflow '41001c48-11a8-4d16-be64-0e016e6a9021' final status: completed, result: Workflow '41001c48-11a8-4d16-be64-0e016e6a9021' executed successfully.
2025-07-22 17:29:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e7b14cdb-eb75-44f1-8c45-400e868670c9-****************, response: {'status': 'complete', 'result': "Workflow '41001c48-11a8-4d16-be64-0e016e6a9021' executed successfully.", 'workflow_status': 'completed'}
2025-07-22 17:29:20 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: e7b14cdb-eb75-44f1-8c45-400e868670c9-**************** 
2025-07-22 17:29:59 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1441
2025-07-22 17:29:59 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1753185599, 'task_type': 'workflow', 'data': {'workflow_id': '41001c48-11a8-4d16-be64-0e016e6a9021', 'payload': {'user_dependent_fields': ['main_input', 'output_key_1'], 'user_payload_template': {'main_input': {'value': 'branch1', 'transition_id': 'CombineTextComponent-*************'}, 'output_key_1': {'value': 'hello', 'transition_id': 'MergeDataComponent-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-07-22 17:29:59 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: 41001c48-11a8-4d16-be64-0e016e6a9021
2025-07-22 17:29:59 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/41001c48-11a8-4d16-be64-0e016e6a9021
2025-07-22 17:30:00 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-22 17:30:00 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow timeout_testing retrieved successfully",
  "workflow": {
    "id": "41001c48-11a8-4d16-be64-0e016e6a9021",
    "name": "timeout_testing",
    "description": "timeout_testing",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/786e81fc-494c-43f3-9077-d26ac8ae37b8.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/b4ad2ccc-67f9-4479-b30d-338f21874f9e.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "string",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "field": "output_key_1",
        "type": "string",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-07-22T10:45:18.080670",
    "updated_at": "2025-07-22T11:59:50.016185",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************",
        "label": "branch 1"
      },
      {
        "name": "DelayComponent",
        "display_name": "Wait / Delay",
        "type": "component",
        "transition_id": "transition-DelayComponent-*************",
        "label": "Wait / Delay"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************",
        "label": "Merge Data"
      },
      {
        "type": "auth_summary",
        "auth_summary": {
          "requires_authentication": false,
          "env_keys_required": [],
          "oauth_providers_required": [],
          "total_auth_items": 0,
          "components_with_auth": [],
          "auth_complexity": "none",
          "analyzed_at": "2025-01-22T00:00:00Z"
        }
      }
    ],
    "is_updated": true,
    "source_version_id": null
  }
}
2025-07-22 17:30:00 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 41001c48-11a8-4d16-be64-0e016e6a9021 - server_script_path is optional
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Skipping field 'main_input' for transition 'transition-CombineTextComponent-*************' (intended for 'CombineTextComponent-*************')
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Processing user-dependent field 'main_input' for transition 'transition-CombineTextComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Target transition for field 'main_input': 'CombineTextComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Target transition 'CombineTextComponent-*************' doesn't exist, using as fallback for 'transition-CombineTextComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-CombineTextComponent-*************
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Checking transition transition-CombineTextComponent-*************: node_type='', is_conditional=False
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-CombineTextComponent-*************
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-DelayComponent-*************
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Checking transition transition-DelayComponent-*************: node_type='', is_conditional=False
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-DelayComponent-*************
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Skipping field 'main_input' for transition 'transition-MergeDataComponent-*************' (intended for 'CombineTextComponent-*************')
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Processing user-dependent field 'main_input' for transition 'transition-MergeDataComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Target transition for field 'main_input': 'CombineTextComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Target transition 'CombineTextComponent-*************' doesn't exist, using as fallback for 'transition-MergeDataComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Skipping field 'output_key_1' for transition 'transition-MergeDataComponent-*************' (intended for 'MergeDataComponent-*************')
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Processing user-dependent field 'output_key_1' for transition 'transition-MergeDataComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Target transition for field 'output_key_1': 'MergeDataComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Target transition 'MergeDataComponent-*************' doesn't exist, using as fallback for 'transition-MergeDataComponent-*************'
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-MergeDataComponent-*************
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Checking transition transition-MergeDataComponent-*************: node_type='', is_conditional=False
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-MergeDataComponent-*************
2025-07-22 17:30:00 - app.services.initialize_workflow - DEBUG - Preserved payload structure in workflow: {'user_dependent_fields': ['main_input', 'output_key_1'], 'user_payload_template': {'main_input': {'value': 'branch1', 'transition_id': 'CombineTextComponent-*************'}, 'output_key_1': {'value': 'hello', 'transition_id': 'MergeDataComponent-*************'}}}
2025-07-22 17:30:00 - EnhancedWorkflowEngine - DEBUG - Stored user_payload_template: {'main_input': {'value': 'branch1', 'transition_id': 'CombineTextComponent-*************'}, 'output_key_1': {'value': 'hello', 'transition_id': 'MergeDataComponent-*************'}}
2025-07-22 17:30:00 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-22 17:30:00 - StateManager - DEBUG - Using global database connections from initializer
2025-07-22 17:30:00 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-22 17:30:00 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-22 17:30:00 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-22 17:30:01 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-22 17:30:01 - StateManager - INFO - WorkflowStateManager initialized
2025-07-22 17:30:01 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-22 17:30:01 - StateManager - DEBUG - Using provided database connections
2025-07-22 17:30:01 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-22 17:30:01 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-22 17:30:01 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-22 17:30:02 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-22 17:30:02 - StateManager - INFO - WorkflowStateManager initialized
2025-07-22 17:30:02 - StateManager - DEBUG - Extracted dependencies for transition transition-DelayComponent-*************: ['transition-CombineTextComponent-*************']
2025-07-22 17:30:02 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-DelayComponent-*************']
2025-07-22 17:30:02 - StateManager - INFO - Built dependency map for 3 transitions
2025-07-22 17:30:02 - StateManager - DEBUG - Transition transition-DelayComponent-************* depends on: ['transition-CombineTextComponent-*************']
2025-07-22 17:30:02 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-DelayComponent-*************']
2025-07-22 17:30:02 - EnhancedWorkflowEngine - DEBUG - Found end transition: transition-MergeDataComponent-*************
2025-07-22 17:30:02 - EnhancedWorkflowEngine - INFO - Found 1 end transitions: {'transition-MergeDataComponent-*************'}
2025-07-22 17:30:02 - StateManager - INFO - Set end transitions: {'transition-MergeDataComponent-*************'}
2025-07-22 17:30:02 - MCPToolExecutor - DEBUG - Set correlation ID to: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************
2025-07-22 17:30:02 - EnhancedWorkflowEngine - DEBUG - Set correlation_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-**************** in tool_executor
2025-07-22 17:30:02 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-22 17:30:02 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-07-22 17:30:02 - NodeExecutor - DEBUG - Set correlation ID to: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************
2025-07-22 17:30:02 - EnhancedWorkflowEngine - DEBUG - Set correlation_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-**************** in node_executor
2025-07-22 17:30:02 - AgentExecutor - DEBUG - Set correlation ID to: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************
2025-07-22 17:30:02 - EnhancedWorkflowEngine - DEBUG - Set correlation_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-**************** in agent_executor
2025-07-22 17:30:02 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-22 17:30:02 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-07-22 17:30:02 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-22 17:30:02 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************
2025-07-22 17:30:02 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************
2025-07-22 17:30:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'workflow_id': '41001c48-11a8-4d16-be64-0e016e6a9021', 'status': 'Initialized', 'message': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-22 17:30:02 - StateManager - INFO - Workflow initialized with multiple initial transitions: ['transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************']
2025-07-22 17:30:02 - StateManager - DEBUG - State: pending={'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}, waiting=set(), completed=set()
2025-07-22 17:30:02 - EnhancedWorkflowEngine - INFO - Initializing workflow with multiple initial transitions: ['transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************']
2025-07-22 17:30:02 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}
2025-07-22 17:30:03 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************'
2025-07-22 17:30:03 - RedisManager - DEBUG - Set key 'workflow_state:e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************' with TTL of 600 seconds
2025-07-22 17:30:03 - StateManager - INFO - Workflow state saved to Redis for workflow ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************. Will be archived to PostgreSQL when Redis key expires.
2025-07-22 17:30:03 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-22 17:30:03 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-22 17:30:03 - StateManager - INFO - Cleared 2 pending transitions: {'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}
2025-07-22 17:30:03 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-22 17:30:03 - StateManager - INFO - Terminated: False
2025-07-22 17:30:03 - StateManager - INFO - Pending transitions (0): []
2025-07-22 17:30:03 - StateManager - INFO - Waiting transitions (0): []
2025-07-22 17:30:03 - StateManager - INFO - Completed transitions (0): []
2025-07-22 17:30:03 - StateManager - INFO - Results stored for 0 transitions
2025-07-22 17:30:03 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-22 17:30:03 - StateManager - INFO - Workflow status: inactive
2025-07-22 17:30:03 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-22 17:30:03 - StateManager - INFO - Workflow status: inactive
2025-07-22 17:30:03 - StateManager - INFO - Workflow paused: False
2025-07-22 17:30:03 - StateManager - INFO - ==============================
2025-07-22 17:30:03 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-07-22 17:30:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************):
2025-07-22 17:30:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-22 17:30:03 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=initial, execution_type=Components)
2025-07-22 17:30:03 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-22 17:30:03 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-07-22 17:30:03 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-22 17:30:03 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-22 17:30:03 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-22 17:30:03 - TransitionHandler - DEBUG - 🔧 PARALLEL DEBUG: input_data_configs for transition-MergeDataComponent-*************: [{'from_transition_id': 'transition-DelayComponent-*************', 'source_node_id': 'Wait / Delay', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-DelayComponent-*************', 'source_handle_id': 'output', 'target_handle_id': 'main_input', 'edge_id': 'reactflow__edge-DelayComponent-*************output-MergeDataComponent-*************main_input'}]}]
2025-07-22 17:30:04 - StateManager - DEBUG - No result found in Redis for transition transition-DelayComponent-*************. Trying PostgreSQL next.
2025-07-22 17:30:06 - PostgresManager - DEBUG - No result found for transition transition-DelayComponent-************* in correlation e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************
2025-07-22 17:30:06 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-DelayComponent-*************. Trying in-memory next.
2025-07-22 17:30:06 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-DelayComponent-*************
2025-07-22 17:30:06 - TransitionHandler - WARNING - 🔧 PARALLEL DEBUG: No result found for from_transition_id transition-DelayComponent-************* in state manager
2025-07-22 17:30:06 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-07-22 17:30:06 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 4 fields (20 null/empty fields removed)
2025-07-22 17:30:06 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'branch1', 'num_additional_inputs': 0, 'merge_strategy': 'Structured Compose', 'output_key_1': 'hello'}
2025-07-22 17:30:06 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': 'branch1', 'num_additional_inputs': 0, 'merge_strategy': 'Structured Compose', 'output_key_1': 'hello'}
2025-07-22 17:30:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************):
2025-07-22 17:30:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server Merge Data', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-22 17:30:06 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: f275e4c6-eb85-4535-8ca1-264ef6bc2d82) using provided producer.
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Added correlation_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-**************** to payload
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Added node_label Merge Data to payload
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': 'branch1', 'num_additional_inputs': 0, 'merge_strategy': 'Structured Compose', 'output_key_1': 'hello'}, 'request_id': 'f275e4c6-eb85-4535-8ca1-264ef6bc2d82', 'correlation_id': 'e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************', 'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data'}
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Request f275e4c6-eb85-4535-8ca1-264ef6bc2d82 sent successfully using provided producer.
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Waiting indefinitely for result for request f275e4c6-eb85-4535-8ca1-264ef6bc2d82...
2025-07-22 17:30:06 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-22 17:30:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************):
2025-07-22 17:30:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-07-22 17:30:06 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=initial, execution_type=Components)
2025-07-22 17:30:06 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-22 17:30:06 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-22 17:30:06 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-22 17:30:06 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-22 17:30:06 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-22 17:30:06 - TransitionHandler - DEBUG - 🔧 PARALLEL DEBUG: input_data_configs for transition-CombineTextComponent-*************: []
2025-07-22 17:30:06 - TransitionHandler - WARNING - 🔧 PARALLEL DEBUG: Empty input_data_configs for transition-CombineTextComponent-*************
2025-07-22 17:30:06 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-22 17:30:06 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-07-22 17:30:06 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'branch1', 'num_additional_inputs': 1, 'input_1': 'hello1'}
2025-07-22 17:30:06 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'branch1', 'num_additional_inputs': 1, 'input_1': 'hello1'}
2025-07-22 17:30:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************):
2025-07-22 17:30:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_label': 'branch 1', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server branch 1', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-07-22 17:30:06 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 03e98571-02f9-4373-9075-3bc7bc8266b6) using provided producer.
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Added correlation_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-**************** to payload
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Added node_label branch 1 to payload
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'branch1', 'num_additional_inputs': 1, 'input_1': 'hello1'}, 'request_id': '03e98571-02f9-4373-9075-3bc7bc8266b6', 'correlation_id': 'e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************', 'transition_id': 'transition-CombineTextComponent-*************', 'node_label': 'branch 1'}
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Request 03e98571-02f9-4373-9075-3bc7bc8266b6 sent successfully using provided producer.
2025-07-22 17:30:06 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 03e98571-02f9-4373-9075-3bc7bc8266b6...
2025-07-22 17:30:06 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1441, corr_id: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************
2025-07-22 17:30:07 - NodeExecutor - DEBUG - Result consumer received message: Offset=1778
2025-07-22 17:30:07 - NodeExecutor - DEBUG - Received valid result for request_id f275e4c6-eb85-4535-8ca1-264ef6bc2d82
2025-07-22 17:30:07 - NodeExecutor - DEBUG - Result consumer received message: Offset=1779
2025-07-22 17:30:07 - NodeExecutor - DEBUG - Received valid result for request_id 03e98571-02f9-4373-9075-3bc7bc8266b6
2025-07-22 17:30:07 - NodeExecutor - INFO - Result received for request f275e4c6-eb85-4535-8ca1-264ef6bc2d82.
2025-07-22 17:30:07 - TransitionHandler - INFO - Execution result from Components executor: {
  "hello": "branch1"
}
2025-07-22 17:30:07 - TransitionHandler - INFO - Checking execution result for errors: {
  "hello": "branch1"
}
2025-07-22 17:30:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************):
2025-07-22 17:30:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': {'hello': 'branch1'}, 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'raw_result': {'hello': 'branch1'}, 'approval_required': False}
2025-07-22 17:30:07 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'result': {'result': {'hello': 'branch1'}}, 'status': 'completed', 'timestamp': 1753185607.2428641}}
2025-07-22 17:30:07 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-07-22 17:30:08 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-07-22 17:30:08 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-22 17:30:08 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-22 17:30:08 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************'}
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'dict'>
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 Execution result keys: ['hello']
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-07-22 17:30:08 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 4.73 seconds
2025-07-22 17:30:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************):
2025-07-22 17:30:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'result': 'Completed transition in 4.73 seconds', 'message': 'Transition completed in 4.73 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: []
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-07-22 17:30:08 - NodeExecutor - INFO - Result received for request 03e98571-02f9-4373-9075-3bc7bc8266b6.
2025-07-22 17:30:08 - TransitionHandler - INFO - Execution result from Components executor: "branch1 hello1"
2025-07-22 17:30:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************):
2025-07-22 17:30:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_label': 'branch 1', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'branch1 hello1', 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'raw_result': 'branch1 hello1', 'approval_required': False}
2025-07-22 17:30:08 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_label': 'branch 1', 'tool_name': 'CombineTextComponent', 'result': {'result': 'branch1 hello1'}, 'status': 'completed', 'timestamp': 1753185608.061814}}
2025-07-22 17:30:08 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-07-22 17:30:08 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-07-22 17:30:08 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-22 17:30:08 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-22 17:30:08 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-DelayComponent-*************']
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-DelayComponent-*************
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-DelayComponent-*************']
2025-07-22 17:30:08 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.24 seconds
2025-07-22 17:30:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************):
2025-07-22 17:30:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'result': 'Completed transition in 2.24 seconds', 'message': 'Transition completed in 2.24 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-DelayComponent-*************']
2025-07-22 17:30:08 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-22 17:30:08 - EnhancedWorkflowEngine - DEBUG - Results: [[], ['transition-DelayComponent-*************']]
2025-07-22 17:30:08 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-*************: []
2025-07-22 17:30:08 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-22 17:30:08 - StateManager - INFO - End transition completed: transition-MergeDataComponent-************* (1/1)
2025-07-22 17:30:08 - StateManager - INFO - All end transitions completed - workflow can terminate
2025-07-22 17:30:08 - EnhancedWorkflowEngine - INFO - All end transitions completed - terminating workflow
2025-07-22 17:30:08 - StateManager - INFO - Workflow terminated flag set to: True
2025-07-22 17:30:08 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-CombineTextComponent-*************: ['transition-DelayComponent-*************']
2025-07-22 17:30:08 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-22 17:30:08 - EnhancedWorkflowEngine - INFO - Transition transition-CombineTextComponent-************* completed successfully: 1 next transitions
2025-07-22 17:30:08 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-DelayComponent-*************']
2025-07-22 17:30:08 - EnhancedWorkflowEngine - INFO - Workflow is marked as terminated, ending workflow execution.
2025-07-22 17:30:08 - StateManager - DEBUG - Workflow active: False (terminated=True, pending=empty, waiting=empty)
2025-07-22 17:30:08 - EnhancedWorkflowEngine - INFO - Workflow execution completed.
2025-07-22 17:30:08 - KafkaWorkflowConsumer - WARNING - No running workflow found for workflow_id: 41001c48-11a8-4d16-be64-0e016e6a9021
2025-07-22 17:30:08 - KafkaWorkflowConsumer - INFO - Workflow '41001c48-11a8-4d16-be64-0e016e6a9021' final status: completed, result: Workflow '41001c48-11a8-4d16-be64-0e016e6a9021' executed successfully.
2025-07-22 17:30:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: e4190666-91d7-4412-9d00-aaeeb45dcbc2-****************, response: {'status': 'complete', 'result': "Workflow '41001c48-11a8-4d16-be64-0e016e6a9021' executed successfully.", 'workflow_status': 'completed'}
2025-07-22 17:30:08 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: e4190666-91d7-4412-9d00-aaeeb45dcbc2-**************** 
