# Credentials & Global Variables - Quick Reference

## 🏗️ Architecture Overview

```
Frontend (Next.js) → API Gateway (FastAPI) → User Service (gRPC) → Google Secret Manager
                                          ↓
                                   Workflow Service (Template Processing)
```

## 🔐 Security Model

- **Per-user encryption keys** stored in Google Cloud Secret Manager
- **Fernet encryption** for credential values in database
- **JWT authentication** with role-based access control
- **User-scoped access** - users only see their own credentials

## 📊 Current Implementation Status

### ✅ Working Features
- ✅ Secure credential CRUD operations
- ✅ Encrypted storage with per-user keys
- ✅ Frontend credential management UI
- ✅ Template variable preprocessing
- ✅ JWT authentication & authorization
- ✅ gRPC service communication

### 🚧 Partially Implemented
- 🚧 Global variables UI (basic structure exists)
- 🚧 Template variable validation in workflow builder
- 🚧 Credential usage in workflow execution

### 📋 Missing/Planned
- ❌ Global variables API endpoints
- ❌ Credential sharing between users
- ❌ Credential expiration & rotation
- ❌ Usage analytics & audit logs

## 🛠️ Key Files & Components

### Backend Services

**API Gateway** (`api-gateway/`):
- `app/api/routers/credential_routes.py` - HTTP endpoints
- `app/schemas/user.py` - Request/response schemas

**User Service** (`user-service/`):
- `app/models/credential.py` - Database model
- `app/services/credential_manager_service.py` - gRPC service
- `app/utils/secret_manager/secret_manager.py` - Encryption manager

**Workflow Service** (`workflow-service/`):
- `app/services/workflow_builder/workflow_schema_converter.py` - Template processing

### Frontend Components

**Workflow Builder App** (`workflow-builder-app/`):
- `src/components/credentials/CredentialManager.tsx` - Main credential UI
- `src/components/inspector/inputs/CredentialInput.tsx` - Workflow node input
- `src/app/(features)/settings/global-variable/` - Global variables UI
- `src/services/credentialService.ts` - API service layer

## 🔌 API Endpoints

### Credential Management
```http
POST   /api/v1/credentials          # Create credential
GET    /api/v1/credentials          # List user credentials  
GET    /api/v1/credentials/{id}     # Get specific credential
PUT    /api/v1/credentials/{id}     # Update credential
DELETE /api/v1/credentials/{id}     # Delete credential
```

### Authentication Required
All endpoints require `Authorization: Bearer <jwt_token>` header.

## 🔄 Template Variable Formats

The system supports multiple formats:

```javascript
// Frontend format (kept as-is)
${{variable_name}}

// Backend/orchestration format (normalized to)
${variable_name}

// Legacy format (auto-converted)
{variable_name} → ${variable_name}
```

## 💾 Database Schema

### Credentials Table
```sql
CREATE TABLE credentials (
    id VARCHAR PRIMARY KEY,
    key_name VARCHAR NOT NULL,           -- User-friendly name
    description TEXT,
    value TEXT NOT NULL,                 -- Encrypted value
    owner_id VARCHAR NOT NULL,           -- User ID
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_used_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 Configuration

### Environment Variables

**API Gateway**:
```bash
JWT_SECRET_KEY=your-secret-key-32-chars-min
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=50052
```

**User Service**:
```bash
GOOGLE_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=base64-service-account-key
DATABASE_URL=******************************
```

**Frontend**:
```bash
NEXT_PUBLIC_API_URL=https://api.example.com/api/v1
```

## 🐛 Common Issues & Solutions

### Credential Creation Fails
```bash
# Check Google Secret Manager permissions
gcloud secrets list --project=your-project-id

# Check user service logs
docker logs user-service
```

### Template Variables Not Working
```bash
# Check workflow service preprocessing
# Verify template variable syntax: ${variable_name}
# Check workflow save logs
```

### Authentication Errors
```bash
# Verify JWT token validity
# Check user roles and permissions
# Ensure Authorization header is present
```

## 🚀 Quick Setup

### 1. Start Services
```bash
# Start user service
cd user-service && python -m app.main

# Start API gateway
cd api-gateway && uvicorn app.main:app --reload

# Start workflow service  
cd workflow-service && python -m app.main

# Start frontend
cd workflow-builder-app && npm run dev
```

### 2. Test Credential Creation
```bash
curl -X POST http://localhost:8000/api/v1/credentials \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "key_name": "test_key",
    "value": "test_value",
    "description": "Test credential"
  }'
```

## 📈 Monitoring

### Key Metrics to Track
- Credential creation/update/deletion rates
- Authentication success/failure rates
- Template variable usage patterns
- API response times
- Encryption/decryption performance

### Health Checks
```bash
# API Gateway health
curl http://localhost:8000/api/v1/health

# Check service connectivity
curl http://localhost:8000/api/v1/credentials \
  -H "Authorization: Bearer <jwt_token>"
```

## 🔮 Next Steps

### Immediate Priorities
1. **Complete Global Variables API** - Add CRUD endpoints
2. **Enhance Template Variable Validation** - Real-time validation in UI
3. **Add Credential Usage Tracking** - Track when/where credentials are used
4. **Implement Audit Logging** - Log all credential operations

### Architecture Improvements
1. **Add Caching Layer** - Redis for frequently accessed credentials
2. **Implement Rate Limiting** - Protect against abuse
3. **Add Circuit Breakers** - Handle service failures gracefully
4. **Enhance Error Handling** - Consistent error responses across services

---

*For detailed implementation details, see the full [Architecture Documentation](./credentials-global-variables-architecture.md)*
