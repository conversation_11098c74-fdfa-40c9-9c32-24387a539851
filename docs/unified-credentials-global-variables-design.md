# Unified Credentials & Global Variables API Design

## Overview

This document outlines the design for unifying credentials and global variables into a single endpoint system with different behaviors based on a `type` parameter.

## Key Requirements

### Credential Behavior (`type: "credential"`)
- **Storage**: Values are encrypted using per-user encryption keys
- **Listing**: Values are **hidden** (not returned in API responses)
- **Getting**: Values are **hidden** (not returned in API responses)
- **Updating**: Replaces previous value without showing it to user
- **Security**: High security with encryption at rest

### Global Variable Behavior (`type: "global-variable"`)
- **Storage**: Values are stored as plain text (no encryption)
- **Listing**: Values are **visible** (returned in API responses)
- **Getting**: Values are **visible** (returned in API responses)
- **Updating**: Shows current value to user before update
- **Security**: Standard security (no encryption needed)

## API Design

### Unified Endpoint Structure

All endpoints will use the same base path but with a `type` query parameter:

```
POST   /api/v1/variables?type={credential|global-variable}
GET    /api/v1/variables?type={credential|global-variable}
GET    /api/v1/variables/{id}?type={credential|global-variable}
PUT    /api/v1/variables/{id}?type={credential|global-variable}
DELETE /api/v1/variables/{id}?type={credential|global-variable}
```

### Request/Response Schemas

#### Create Variable Request
```json
{
  "key_name": "api_key",
  "value": "secret_value_or_plain_text",
  "description": "Description of the variable"
}
```

#### List Variables Response

**For Credentials** (`type=credential`):
```json
{
  "success": true,
  "message": "Variables retrieved successfully",
  "variables": [
    {
      "id": "uuid",
      "key_name": "api_key",
      "description": "API key for service",
      "type": "credential",
      "value": null,  // Always null for credentials
      "has_value": true,  // Indicates if value exists
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "last_used_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

**For Global Variables** (`type=global-variable`):
```json
{
  "success": true,
  "message": "Variables retrieved successfully",
  "variables": [
    {
      "id": "uuid",
      "key_name": "company_name",
      "description": "Company name variable",
      "type": "global-variable",
      "value": "RUH AI",  // Actual value shown
      "has_value": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "last_used_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

## Database Schema Changes

### Updated Variable Model

```python
# user-service/app/models/variable.py (renamed from credential.py)
from datetime import datetime
from sqlalchemy import Column, ForeignKey, String, DateTime, Enum
from sqlalchemy.orm import relationship
from app.models.user import Base
import enum

class VariableType(enum.Enum):
    CREDENTIAL = "credential"
    GLOBAL_VARIABLE = "global-variable"

class Variable(Base):
    __tablename__ = "variables"  # Renamed from "credentials"

    id = Column(String, primary_key=True)
    key_name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    value = Column(String, nullable=False)  # Encrypted for credentials, plain for global vars
    type = Column(Enum(VariableType), nullable=False)  # NEW: Type field
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    owner_id = Column(String, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_used_at = Column(DateTime, default=datetime.utcnow)

    # Relationship with User
    owner = relationship("User", backref="variables")

    def __repr__(self):
        return f"<Variable {self.key_name} ({self.type.value})>"
```

### Database Migration

```sql
-- Migration script to update existing credentials table
ALTER TABLE credentials RENAME TO variables;
ALTER TABLE variables ADD COLUMN type VARCHAR(20) NOT NULL DEFAULT 'credential';
UPDATE variables SET type = 'credential' WHERE type IS NULL;

-- Add index for better performance
CREATE INDEX idx_variables_owner_type ON variables(owner_id, type);
CREATE UNIQUE INDEX idx_variables_owner_key_type ON variables(owner_id, key_name, type);
```

## Backend Implementation

### API Gateway Changes

#### Updated Router
```python
# api-gateway/app/api/routers/variable_routes.py (renamed from credential_routes.py)
from fastapi import APIRouter, Depends, HTTPException, status, Query
from enum import Enum

class VariableType(str, Enum):
    CREDENTIAL = "credential"
    GLOBAL_VARIABLE = "global-variable"

variable_router = APIRouter(prefix="/variables", tags=["variables"])

@variable_router.post("", response_model=VariableResponse)
async def create_variable(
    variable_data: VariableCreate,
    type: VariableType = Query(..., description="Type of variable to create"),
    current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.create_variable(
            owner_id=current_user["user_id"],
            key_name=variable_data.key_name,
            value=variable_data.value,
            description=variable_data.description,
            type=type.value
        )
        # ... rest of implementation
    except Exception as e:
        # ... error handling

@variable_router.get("", response_model=VariableListResponse)
async def list_variables(
    type: VariableType = Query(..., description="Type of variables to list"),
    current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.list_variables(
            owner_id=current_user["user_id"],
            type=type.value
        )
        # ... rest of implementation
```

#### Updated Schemas
```python
# api-gateway/app/schemas/user.py
from enum import Enum

class VariableType(str, Enum):
    CREDENTIAL = "credential"
    GLOBAL_VARIABLE = "global-variable"

class VariableCreate(BaseModel):
    key_name: str = Field(..., min_length=1, max_length=50)
    value: str = Field(..., min_length=1)
    description: Optional[str] = None

class VariableInfo(BaseModel):
    id: str
    key_name: str
    description: Optional[str]
    type: VariableType
    value: Optional[str]  # Null for credentials, actual value for global variables
    has_value: bool
    created_at: str
    updated_at: str
    last_used_at: str

class VariableListResponse(BaseModel):
    success: bool
    message: str
    variables: List[VariableInfo]
```

### User Service Changes

#### Updated gRPC Service
```python
# user-service/app/services/variable_manager_service.py (renamed)
class VariableService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.encryption_manager = EncryptionManager()
    
    def create_variable(
        self, request: user_pb2.CreateVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.CreateVariableResponse:
        db = self.get_db()
        try:
            # Determine if encryption is needed based on type
            if request.type == "credential":
                # Encrypt the value for credentials
                processed_value = self.encryption_manager.encrypt(request.value, request.owner_id)
            else:
                # Store as plain text for global variables
                processed_value = request.value

            variable = Variable(
                id=str(uuid.uuid4()),
                key_name=request.key_name,
                description=request.description,
                value=processed_value,
                type=VariableType(request.type),
                owner_id=request.owner_id
            )

            db.add(variable)
            db.commit()
            db.refresh(variable)

            return user_pb2.CreateVariableResponse(
                success=True,
                message="Variable created successfully",
                id=variable.id,
                key_name=variable.key_name
            )
        except Exception as e:
            # ... error handling

    def list_variables(
        self, request: user_pb2.ListVariablesRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListVariablesResponse:
        db = self.get_db()
        try:
            variables = db.query(Variable).filter(
                Variable.owner_id == request.owner_id,
                Variable.type == VariableType(request.type)
            ).all()

            variable_list = []
            for var in variables:
                if var.type == VariableType.CREDENTIAL:
                    # Don't return actual value for credentials
                    variable_info = user_pb2.VariableInfo(
                        id=var.id,
                        key_name=var.key_name,
                        description=var.description or "",
                        type=var.type.value,
                        value="",  # Empty for credentials
                        has_value=bool(var.value),
                        created_at=var.created_at.isoformat(),
                        updated_at=var.updated_at.isoformat(),
                        last_used_at=var.last_used_at.isoformat()
                    )
                else:
                    # Return actual value for global variables
                    variable_info = user_pb2.VariableInfo(
                        id=var.id,
                        key_name=var.key_name,
                        description=var.description or "",
                        type=var.type.value,
                        value=var.value,  # Actual value for global variables
                        has_value=bool(var.value),
                        created_at=var.created_at.isoformat(),
                        updated_at=var.updated_at.isoformat(),
                        last_used_at=var.last_used_at.isoformat()
                    )
                
                variable_list.append(variable_info)

            return user_pb2.ListVariablesResponse(
                success=True,
                message="Variables retrieved successfully",
                variables=variable_list
            )
        except Exception as e:
            # ... error handling
```

## Frontend Implementation

### Updated API Service
```typescript
// workflow-builder-app/src/services/variableService.ts
export enum VariableType {
  CREDENTIAL = 'credential',
  GLOBAL_VARIABLE = 'global-variable'
}

export interface Variable {
  id: string;
  key_name: string;
  description?: string;
  type: VariableType;
  value?: string;  // null for credentials, actual value for global variables
  has_value: boolean;
  created_at: string;
  updated_at: string;
  last_used_at: string;
}

export class VariableService {
  async createVariable(variable: VariableCreate, type: VariableType): Promise<Variable> {
    const response = await api.post(`/variables?type=${type}`, variable);
    return response.data;
  }

  async listVariables(type: VariableType): Promise<Variable[]> {
    const response = await api.get(`/variables?type=${type}`);
    return response.data.variables;
  }

  async getVariable(id: string, type: VariableType): Promise<Variable> {
    const response = await api.get(`/variables/${id}?type=${type}`);
    return response.data.variable;
  }

  async updateVariable(id: string, variable: VariableUpdate, type: VariableType): Promise<Variable> {
    const response = await api.put(`/variables/${id}?type=${type}`, variable);
    return response.data.variable;
  }

  async deleteVariable(id: string, type: VariableType): Promise<void> {
    await api.delete(`/variables/${id}?type=${type}`);
  }
}
```

### Updated UI Components
```typescript
// workflow-builder-app/src/components/variables/VariableManager.tsx
interface VariableManagerProps {
  type: VariableType;
}

export function VariableManager({ type }: VariableManagerProps) {
  const [variables, setVariables] = useState<Variable[]>([]);
  
  const isCredentialMode = type === VariableType.CREDENTIAL;
  
  return (
    <div>
      <h2>{isCredentialMode ? 'Credentials' : 'Global Variables'}</h2>
      
      <table>
        <thead>
          <tr>
            <th>Key</th>
            <th>Value</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {variables.map(variable => (
            <tr key={variable.id}>
              <td>{variable.key_name}</td>
              <td>
                {isCredentialMode ? (
                  variable.has_value ? '••••••••' : 'No value'
                ) : (
                  variable.value || 'No value'
                )}
              </td>
              <td>{variable.description}</td>
              <td>
                <button onClick={() => handleEdit(variable)}>
                  {isCredentialMode ? 'Replace' : 'Edit'}
                </button>
                <button onClick={() => handleDelete(variable.id)}>Delete</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
```

## Migration Strategy

### Phase 1: Database Migration
1. Rename `credentials` table to `variables`
2. Add `type` column with default value `'credential'`
3. Update existing records to have `type = 'credential'`
4. Add new indexes for performance

### Phase 2: Backend Updates
1. Update user-service models and gRPC methods
2. Update API gateway routes and schemas
3. Add type-based logic for encryption/decryption
4. Update validation rules

### Phase 3: Frontend Updates
1. Update API service to use new endpoints
2. Update UI components to handle both types
3. Add type selection in creation forms
4. Update existing credential screens

### Phase 4: Testing & Deployment
1. Comprehensive testing of both credential and global variable flows
2. Migration testing with existing data
3. Performance testing with new indexes
4. Gradual rollout with feature flags

## Benefits of This Design

1. **Unified Interface**: Single set of endpoints for both types
2. **Type Safety**: Clear separation of behavior based on type parameter
3. **Security**: Maintains encryption for credentials while allowing visibility for global variables
4. **Backward Compatibility**: Existing credentials continue to work
5. **Scalability**: Easy to add new variable types in the future
6. **Consistency**: Same API patterns for both types of variables

## Next Steps

1. Review and approve this design
2. Create detailed implementation tasks
3. Begin with database migration
4. Implement backend changes
5. Update frontend components
6. Comprehensive testing
7. Deployment and monitoring
