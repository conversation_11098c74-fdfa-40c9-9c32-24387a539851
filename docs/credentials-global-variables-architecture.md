# Credentials and Global Variables Architecture Documentation

## Overview

This document provides a comprehensive overview of how credentials and global variables are architected across the RUH AI backend services. The system implements secure credential storage with encryption, template variable processing, and multi-service integration.

## Architecture Components

### 1. Service Architecture

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│  workflow-builder-  │    │    api-gateway      │    │   user-service      │
│       app           │◄──►│                     │◄──►│                     │
│  (Frontend/Next.js) │    │  (FastAPI Gateway)  │    │ (gRPC/SQLAlchemy)   │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
                                      │
                                      ▼
                           ┌─────────────────────┐
                           │  workflow-service   │
                           │ (gRPC/SQLAlchemy)   │
                           └─────────────────────┘
```

### 2. Data Flow

```
Frontend → API Gateway → User Service → Google Secret Manager
   ↓              ↓            ↓              ↓
Template      Validation   Encryption    Secure Storage
Variables    & Auth Guard  & Database    & Key Management
```

## Service-by-Service Implementation

### A. User Service (`user-service/`)

**Purpose**: Core credential storage, encryption, and user management

#### Database Models

**File**: `app/models/credential.py`
```python
class Credential(Base):
    __tablename__ = "credentials"
    
    id = Column(String, primary_key=True)
    key_name = Column(String, nullable=False)  # User-friendly name
    description = Column(String, nullable=True)
    value = Column(String, nullable=False)     # Encrypted value
    updated_at = Column(DateTime, default=datetime.utcnow)
    owner_id = Column(String, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_used_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationship with User
    owner = relationship("User", backref="credentials")
```

#### Encryption Manager

**File**: `app/utils/secret_manager/secret_manager.py`

**Key Features**:
- Uses Google Cloud Secret Manager for encryption key storage
- Fernet encryption for credential values
- Per-user encryption keys
- Automatic key generation and rotation support

**Core Methods**:
```python
class EncryptionManager:
    def create_and_store_user_key(self, secret_id: str) -> str
    def get_user_encryption_key(self, user_id: str) -> str
    def encrypt(self, plain_text: str, user_id: str) -> str
    def decrypt(self, cipher_text: str, user_id: str) -> str
```

#### gRPC Service

**File**: `app/services/credential_manager_service.py`

**Available Operations**:
- `CreateCredential` - Create new encrypted credential
- `ListCredentials` - Get user's credentials (decrypted)
- `GetCredentialDetails` - Get specific credential details
- `UpdateCredential` - Update existing credential
- `DeleteCredential` - Remove credential

### B. API Gateway (`api-gateway/`)

**Purpose**: HTTP API layer, authentication, and request routing

#### Credential Routes

**File**: `app/api/routers/credential_routes.py`

**Endpoints**:
```python
POST   /api/v1/credentials          # Create credential
GET    /api/v1/credentials          # List user credentials  
GET    /api/v1/credentials/{id}     # Get specific credential
PUT    /api/v1/credentials/{id}     # Update credential
DELETE /api/v1/credentials/{id}     # Delete credential
```

#### Authentication & Authorization

**Features**:
- JWT-based authentication via `role_required(["user"])` decorator
- User-scoped credential access (owner_id filtering)
- Request validation using Pydantic schemas

#### Schema Definitions

**File**: `app/schemas/user.py`
```python
class CredentialCreate(BaseModel):
    key_name: str = Field(..., min_length=1, max_length=20)
    value: str = Field(..., min_length=1)
    description: Optional[str] = None

class CredentialInfo(BaseModel):
    id: str
    key_name: str
    description: Optional[str]
    value: str  # Decrypted for API responses
    created_at: str
    updated_at: str
    last_used_at: str
```

### C. Workflow Service (`workflow-service/`)

**Purpose**: Template variable processing and workflow schema conversion

#### Template Variable Processing

**File**: `app/services/workflow_builder/workflow_schema_converter.py`

**Key Functions**:
```python
def preprocess_template_variables(field_value: Any, context: str = "") -> Any
def extract_template_variables_from_value(value: Any) -> Dict[str, str]
def validate_workflow_template_variables(workflow_data: Dict[str, Any]) -> Dict[str, Any]
```

**Template Variable Formats Supported**:
- `{variable_name}` → Converted to `${variable_name}`
- `${variable_name}` → Normalized format
- `${{variable_name}}` → Frontend double-brace format (kept as-is)

#### Workflow Context

**File**: `app/models/workflow_builder/context.py`
```python
class WorkflowContext:
    def __init__(self, workflow_id, execution_id, global_context):
        self.workflow_id = workflow_id
        self.execution_id = execution_id
        self.node_outputs: Dict[str, Dict[str, Any]] = {}
        self.global_context = global_context or {}  # Global variables
```

### D. Workflow Builder App (`workflow-builder-app/`)

**Purpose**: Frontend interface for credential and global variable management

#### Credential Management Components

**File**: `src/components/credentials/CredentialManager.tsx`
- Full CRUD interface for credentials
- Real-time validation and error handling
- Secure credential input components

**File**: `src/components/inspector/inputs/CredentialInput.tsx`
- Dual-mode credential input (direct value vs credential ID)
- Integration with workflow node configuration
- Toggle between secure storage and direct input

#### Global Variables Interface

**File**: `src/app/(features)/settings/global-variable/page.tsx`
- Table view of global variables
- Search and sort functionality
- Usage syntax display (`{{ $json.variableName }}`)

**File**: `src/app/(features)/settings/global-variable/AddVariableDialog.tsx`
- Modal for adding new global variables
- Secure credential toggle
- Form validation

#### API Integration

**File**: `src/services/credentialService.ts`
- Centralized credential API service
- Error handling and retry logic
- Data transformation between frontend and backend formats

**File**: `src/lib/api.ts`
```typescript
// Credential Management Functions
export async function fetchCredentials(): Promise<CredentialListResponse>
export async function createCredential(credential: CredentialCreate): Promise<Credential>
export async function deleteCredential(credentialId: string): Promise<CredentialDeleteResponse>
export async function getCredentialValueForExecution(credentialId: string): Promise<string>
```

## Security Implementation

### 1. Encryption Strategy

- **Per-user encryption keys** stored in Google Cloud Secret Manager
- **Fernet symmetric encryption** for credential values
- **Key rotation support** through versioned secrets
- **Automatic key generation** on first credential creation

### 2. Access Control

- **JWT-based authentication** for all API endpoints
- **User-scoped access** - users can only access their own credentials
- **Role-based authorization** using `role_required` decorators
- **Audit logging** for credential access and modifications

### 3. Data Protection

- **Encrypted storage** of credential values in database
- **Secure transmission** via HTTPS/TLS
- **Memory protection** - credentials decrypted only when needed
- **No credential logging** in application logs

## Template Variable System

### 1. Variable Formats

The system supports multiple template variable formats for flexibility:

```javascript
// Frontend format (double braces)
${{variable_name}}

// Backend/orchestration format (single braces)  
${variable_name}

// Legacy format (converted automatically)
{variable_name}
```

### 2. Processing Pipeline

1. **Frontend Input** → Template variables entered in `${{}}` format
2. **Schema Conversion** → Preprocessed during workflow save
3. **Validation** → Template variables validated against available variables
4. **Execution** → Variables resolved by orchestration engine at runtime

### 3. Usage in Workflows

Template variables can be used in:
- AI component system messages and queries
- API request parameters and headers
- Conditional logic expressions
- Loop iteration parameters
- Data transformation operations

## API Endpoints Summary

### Credential Endpoints
```
POST   /api/v1/credentials          - Create new credential
GET    /api/v1/credentials          - List user credentials
GET    /api/v1/credentials/{id}     - Get credential details
PUT    /api/v1/credentials/{id}     - Update credential
DELETE /api/v1/credentials/{id}     - Delete credential
```

### Authentication Required
All credential endpoints require:
- Valid JWT token in Authorization header
- User role authorization
- Request body validation (for POST/PUT)

## Configuration

### Environment Variables

**API Gateway** (`.env`):
```bash
# JWT Configuration
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Service Discovery
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=50052
```

**User Service** (`.env`):
```bash
# Google Cloud Configuration
GOOGLE_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=base64-encoded-service-account-key

# Database Configuration
DATABASE_URL=postgresql://user:pass@localhost/dbname
```

**Frontend** (`.env.local`):
```bash
NEXT_PUBLIC_API_URL=https://api.example.com/api/v1
```

## Current Implementation Status

### ✅ Completed Features
- Secure credential storage with encryption
- Full CRUD API for credentials
- Frontend credential management interface
- Template variable preprocessing
- User authentication and authorization
- Per-user encryption key management

### 🚧 In Progress
- Global variable UI integration with credential system
- Template variable validation in workflow builder
- Credential usage tracking and analytics

### 📋 Planned Features
- Credential sharing between users/teams
- Credential expiration and rotation
- Advanced template variable functions
- Credential usage audit logs
- Bulk credential import/export

## Best Practices

### 1. Security
- Never log credential values
- Use secure credential inputs in UI
- Implement proper key rotation
- Regular security audits

### 2. Development
- Use centralized API configuration
- Implement proper error handling
- Follow consistent naming conventions
- Write comprehensive tests

### 3. Operations
- Monitor credential usage
- Set up proper backup procedures
- Implement disaster recovery
- Regular security updates

## Integration Patterns

### 1. Workflow Node Integration

**Credential Input Component Pattern**:
```typescript
// File: src/components/inspector/inputs/CredentialInput.tsx
interface CredentialInputValue {
  use_credential_id: boolean;
  credential_id?: string;
  value?: string;
}

// Usage in workflow nodes
const handleCredentialInput = (inputDef, value, onChange) => {
  if (value.use_credential_id) {
    // Use stored credential
    return <CredentialSelector credentialId={value.credential_id} />
  } else {
    // Direct input
    return <SecureInput value={value.value} />
  }
}
```

### 2. Template Variable Integration

**Frontend Template Variable Display**:
```typescript
// File: src/components/ui/template-variable-display.tsx
export function TemplateVariableDisplay({
  value,
  availableVariables,
  showHighlighting = true,
  showPreview = false
}) {
  // Highlights template variables in text
  // Shows validation status
  // Provides variable suggestions
}
```

**Backend Template Processing**:
```python
# File: workflow-service/app/services/workflow_builder/workflow_schema_converter.py
def preprocess_field_value(data):
    """Convert template variable formats during workflow save"""
    if isinstance(data, str):
        # Convert {var} → ${var}
        # Keep ${{var}} as-is for frontend
        return process_template_variables(data)
    return data
```

### 3. Execution Context Integration

**Orchestration Engine Integration**:
```python
# Template variables passed to orchestration engine
execution_context = {
    "template_variables": {
        "api_key": "${credential_id_123}",
        "user_name": "${global_var_user}",
        "company": "RUH AI"
    }
}

# Orchestration engine resolves at runtime:
# ${credential_id_123} → actual API key value
# ${global_var_user} → user's name from global variables
```

## Data Models and Schemas

### 1. Database Schema

**Credentials Table** (`user-service`):
```sql
CREATE TABLE credentials (
    id VARCHAR PRIMARY KEY,
    key_name VARCHAR NOT NULL,
    description TEXT,
    value TEXT NOT NULL,  -- Encrypted
    owner_id VARCHAR NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_used_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_credentials_owner_id ON credentials(owner_id);
CREATE UNIQUE INDEX idx_credentials_owner_key ON credentials(owner_id, key_name);
```

**Workflow Template Variables** (`workflow-service`):
```sql
-- Template variables are stored as JSON in workflow data
-- No separate table needed as they're part of workflow schema
```

### 2. API Schema Transformations

**Frontend ↔ Backend Mapping**:
```typescript
// Frontend format
interface Credential {
  id: string;
  name: string;        // Maps to key_name
  value: string;
  description?: string;
}

// Backend format
interface BackendCredential {
  id: string;
  key_name: string;    // Maps from name
  value: string;
  description?: string;
}
```

### 3. gRPC Protocol Definitions

**User Service Proto** (implied structure):
```protobuf
service UserService {
  rpc CreateCredential(CreateCredentialRequest) returns (CreateCredentialResponse);
  rpc ListCredentials(ListCredentialsRequest) returns (ListCredentialsResponse);
  rpc GetCredentialDetails(GetCredentialDetailsRequest) returns (GetCredentialDetailsResponse);
  rpc UpdateCredential(UpdateCredentialRequest) returns (UpdateCredentialResponse);
  rpc DeleteCredential(DeleteCredentialRequest) returns (DeleteCredentialResponse);
}

message CredentialInfo {
  string id = 1;
  string key_name = 2;
  string description = 3;
  string value = 4;
  string created_at = 5;
  string updated_at = 6;
  string last_used_at = 7;
}
```

## Error Handling and Validation

### 1. Frontend Validation

**Credential Input Validation**:
```typescript
// File: src/utils/credentialTransforms.ts
export function validateCredentialCreate(credential: CredentialCreate): ValidationResult {
  const errors: string[] = [];

  if (!credential.name || credential.name.length < 1) {
    errors.push("Credential name is required");
  }

  if (credential.name && credential.name.length > 20) {
    errors.push("Credential name must be 20 characters or less");
  }

  if (!credential.value || credential.value.length < 1) {
    errors.push("Credential value is required");
  }

  return { isValid: errors.length === 0, errors };
}
```

### 2. Backend Validation

**API Gateway Validation**:
```python
# File: api-gateway/app/schemas/user.py
class CredentialCreate(BaseModel):
    key_name: str = Field(..., min_length=1, max_length=20)
    value: str = Field(..., min_length=1)
    description: Optional[str] = None

    @validator('key_name')
    def validate_key_name(cls, v):
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Key name can only contain letters, numbers, underscores, and hyphens')
        return v
```

### 3. Error Response Format

**Standardized Error Responses**:
```json
{
  "success": false,
  "message": "Credential with this key_name already exists",
  "code": "CREDENTIAL_ALREADY_EXISTS",
  "details": {
    "field": "key_name",
    "value": "duplicate_key"
  }
}
```

## Testing Strategy

### 1. Unit Tests

**Credential Service Tests**:
```python
# File: user-service/tests/test_credential_service.py
class TestCredentialService:
    def test_create_credential_success(self):
        # Test successful credential creation

    def test_create_credential_duplicate_key(self):
        # Test duplicate key_name handling

    def test_encrypt_decrypt_roundtrip(self):
        # Test encryption/decryption consistency
```

### 2. Integration Tests

**API Integration Tests**:
```python
# File: api-gateway/tests/test_credential_routes.py
class TestCredentialRoutes:
    def test_create_credential_endpoint(self):
        # Test full API endpoint with authentication

    def test_list_credentials_user_scoped(self):
        # Test user isolation in credential listing
```

### 3. Frontend Tests

**Component Tests**:
```typescript
// File: src/components/credentials/__tests__/CredentialManager.test.tsx
describe('CredentialManager', () => {
  it('should create credential successfully', async () => {
    // Test credential creation flow
  });

  it('should handle validation errors', async () => {
    // Test error handling
  });
});
```

## Monitoring and Observability

### 1. Logging Strategy

**Structured Logging**:
```python
# User Service
logger.info("Credential created", extra={
    "user_id": user_id,
    "credential_id": credential.id,
    "key_name": credential.key_name,  # Safe to log
    # Never log credential.value
})

# API Gateway
logger.info("Credential API request", extra={
    "endpoint": "/credentials",
    "method": "POST",
    "user_id": current_user["user_id"],
    "status_code": 201
})
```

### 2. Metrics Collection

**Key Metrics to Track**:
- Credential creation/update/deletion rates
- Authentication success/failure rates
- Template variable usage patterns
- API response times
- Encryption/decryption performance

### 3. Health Checks

**Service Health Endpoints**:
```python
# API Gateway health check
@health_router.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "services": {
            "user_service": await check_user_service_health(),
            "workflow_service": await check_workflow_service_health()
        }
    }
```

## Deployment Considerations

### 1. Environment Configuration

**Production Settings**:
```bash
# Strong encryption keys
JWT_SECRET_KEY=<strong-random-key-32-chars-minimum>

# Secure database connections
DATABASE_URL=***************************************/db?sslmode=require

# Google Cloud configuration
GOOGLE_PROJECT_ID=production-project-id
GOOGLE_APPLICATION_CREDENTIALS=<base64-service-account-key>
```

### 2. Security Hardening

**Production Security Checklist**:
- [ ] Enable HTTPS/TLS for all endpoints
- [ ] Configure proper CORS policies
- [ ] Set up rate limiting
- [ ] Enable request/response logging (without sensitive data)
- [ ] Configure proper database connection pooling
- [ ] Set up monitoring and alerting
- [ ] Regular security audits and updates

### 3. Scaling Considerations

**Horizontal Scaling**:
- API Gateway: Stateless, can scale horizontally
- User Service: Database-dependent, consider connection pooling
- Workflow Service: Stateless for template processing
- Frontend: CDN deployment for static assets

## Troubleshooting Guide

### 1. Common Issues

**Credential Creation Fails**:
```bash
# Check user service logs
docker logs user-service

# Common causes:
# - Google Secret Manager permissions
# - Database connection issues
# - Duplicate key_name for user
# - Invalid encryption key
```

**Template Variables Not Resolving**:
```bash
# Check workflow service logs
docker logs workflow-service

# Common causes:
# - Invalid template variable syntax
# - Missing variable definitions
# - Preprocessing errors during workflow save
```

**Authentication Errors**:
```bash
# Check API gateway logs
docker logs api-gateway

# Common causes:
# - Expired JWT tokens
# - Invalid user roles
# - Missing authorization headers
```

### 2. Debugging Steps

**Enable Debug Logging**:
```python
# User Service
logging.basicConfig(level=logging.DEBUG)

# API Gateway
import logging
logging.getLogger("uvicorn").setLevel(logging.DEBUG)
```

**Database Query Debugging**:
```python
# Check credential existence
SELECT * FROM credentials WHERE owner_id = 'user_id' AND key_name = 'key_name';

# Check user permissions
SELECT * FROM users WHERE id = 'user_id' AND is_active = true;
```

**Google Secret Manager Debugging**:
```bash
# Test GSM access
gcloud secrets list --project=your-project-id

# Check secret versions
gcloud secrets versions list user-encryption-key-{user_id} --project=your-project-id
```

### 3. Performance Issues

**Slow Credential Operations**:
- Check database connection pool settings
- Monitor Google Secret Manager API quotas
- Review encryption/decryption performance
- Consider credential caching strategies

**High Memory Usage**:
- Ensure credentials are not cached in memory
- Check for memory leaks in encryption operations
- Monitor gRPC connection pooling

## Migration Guide

### 1. From Legacy System

**Data Migration Steps**:
```python
# Example migration script
def migrate_legacy_credentials():
    legacy_credentials = get_legacy_credentials()

    for cred in legacy_credentials:
        # Create user encryption key if not exists
        encryption_manager.create_and_store_user_key(cred.user_id)

        # Encrypt legacy value
        encrypted_value = encryption_manager.encrypt(cred.value, cred.user_id)

        # Create new credential record
        new_credential = Credential(
            id=str(uuid.uuid4()),
            key_name=cred.name,
            value=encrypted_value,
            owner_id=cred.user_id
        )
        db.add(new_credential)

    db.commit()
```

### 2. Template Variable Migration

**Convert Legacy Template Formats**:
```python
def migrate_template_variables(workflow_data):
    """Convert old template formats to new format"""

    # Convert {var} → ${var}
    # Keep ${{var}} as-is
    # Update workflow schema

    return preprocess_template_variables(workflow_data)
```

## API Reference

### 1. Complete Endpoint Documentation

**Credential Management API**:

```http
POST /api/v1/credentials
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "key_name": "api_key",
  "value": "secret_value",
  "description": "API key for external service"
}

Response:
{
  "success": true,
  "message": "Credential created successfully",
  "id": "credential_uuid",
  "key_name": "api_key"
}
```

```http
GET /api/v1/credentials
Authorization: Bearer <jwt_token>

Response:
{
  "success": true,
  "message": "Credentials retrieved successfully",
  "credentials": [
    {
      "id": "credential_uuid",
      "key_name": "api_key",
      "description": "API key for external service",
      "value": "decrypted_value",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "last_used_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. Error Codes Reference

**HTTP Status Codes**:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (credential doesn't exist)
- `409` - Conflict (duplicate key_name)
- `500` - Internal Server Error

**Custom Error Codes**:
- `CREDENTIAL_ALREADY_EXISTS` - Duplicate key_name for user
- `CREDENTIAL_NOT_FOUND` - Credential ID not found
- `ENCRYPTION_ERROR` - Encryption/decryption failed
- `INVALID_TEMPLATE_VARIABLE` - Template variable syntax error

## Future Enhancements

### 1. Planned Features

**Short Term (Next Sprint)**:
- [ ] Credential usage analytics dashboard
- [ ] Bulk credential import/export
- [ ] Credential expiration notifications
- [ ] Enhanced template variable validation

**Medium Term (Next Quarter)**:
- [ ] Team-based credential sharing
- [ ] Credential rotation automation
- [ ] Advanced template variable functions
- [ ] Credential access audit logs

**Long Term (Next 6 Months)**:
- [ ] Multi-cloud secret manager support
- [ ] Credential versioning and rollback
- [ ] Advanced encryption options
- [ ] Integration with external secret managers

### 2. Technical Debt

**Known Issues to Address**:
- [ ] Improve error handling consistency across services
- [ ] Add comprehensive integration tests
- [ ] Optimize database queries for large credential sets
- [ ] Implement proper credential caching strategy
- [ ] Add request rate limiting for credential operations

### 3. Architecture Improvements

**Proposed Enhancements**:
- [ ] Implement event-driven architecture for credential changes
- [ ] Add credential usage metrics collection
- [ ] Implement circuit breaker pattern for external services
- [ ] Add distributed tracing for credential operations
- [ ] Implement proper backup and disaster recovery

---

## Appendix

### A. File Structure Reference

```
backend/
├── api-gateway/
│   ├── app/api/routers/credential_routes.py
│   ├── app/schemas/user.py
│   └── app/services/user_service.py
├── user-service/
│   ├── app/models/credential.py
│   ├── app/services/credential_manager_service.py
│   └── app/utils/secret_manager/secret_manager.py
├── workflow-service/
│   ├── app/services/workflow_builder/workflow_schema_converter.py
│   └── app/models/workflow_builder/context.py
└── workflow-builder-app/
    ├── src/components/credentials/CredentialManager.tsx
    ├── src/components/inspector/inputs/CredentialInput.tsx
    ├── src/services/credentialService.ts
    ├── src/lib/api.ts
    └── src/app/(features)/settings/global-variable/
```

### B. Environment Variables Reference

**Complete Environment Configuration**:

```bash
# API Gateway
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=50052

# User Service
GOOGLE_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=base64-encoded-service-account-key
DATABASE_URL=postgresql://user:pass@localhost/dbname

# Workflow Service
DATABASE_URL=postgresql://user:pass@localhost/workflow_db

# Frontend
NEXT_PUBLIC_API_URL=https://api.example.com/api/v1
```

---

*This comprehensive documentation covers the complete architecture, implementation, and operational aspects of the credentials and global variables system across all RUH AI backend services. Last updated: 2024-07-16*
