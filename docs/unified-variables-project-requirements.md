# Unified Credentials & Global Variables - Project Requirements Document

## 1. Project Overview

### 1.1 Objective
Unify the credentials and global variables management into a single endpoint system that provides different behaviors based on a `type` parameter, enabling secure credential storage with hidden values and transparent global variable storage with visible values.

### 1.2 Business Requirements
- **Credentials**: Secure storage with encryption, values hidden from users, replace-only updates
- **Global Variables**: Plain text storage, values visible to users, editable updates
- **Unified Interface**: Single API endpoint system for both types
- **Type Safety**: Clear separation of behavior based on type parameter
- **Backward Compatibility**: Existing credential functionality must continue to work

### 1.3 Success Criteria
- [ ] Single API endpoint handles both credentials and global variables
- [ ] Credential values remain encrypted and hidden from users
- [ ] Global variable values are stored as plain text and visible to users
- [ ] Frontend provides appropriate UI for each type
- [ ] Migration from existing credential system is seamless
- [ ] Performance is maintained or improved

## 2. Current System Analysis

### 2.1 Existing Architecture

```
Frontend (Next.js) → API Gateway (FastAPI) → User Service (gRPC) → Database + Google Secret Manager
```

**Current Components:**
- **Database Table**: `credentials` (id, key_name, description, value, owner_id, timestamps)
- **Encryption**: Per-user keys in Google Secret Manager, Fernet encryption
- **API Endpoints**: `/api/v1/credentials` (POST, GET, PUT, DELETE)
- **Frontend**: Credential management UI with hidden values

### 2.2 Current Data Flow

#### Credential Creation Flow
```
1. Frontend → POST /api/v1/credentials {key_name, value, description}
2. API Gateway → Validates request, extracts user_id from JWT
3. User Service → Encrypts value with user's key from Google Secret Manager
4. Database → Stores encrypted value in credentials table
5. Response → Returns success with credential ID and key_name (no value)
```

#### Credential Listing Flow
```
1. Frontend → GET /api/v1/credentials
2. API Gateway → Validates JWT, extracts user_id
3. User Service → Queries credentials by owner_id, decrypts values
4. Response → Returns credentials with decrypted values (security issue!)
5. Frontend → Should hide values but currently shows them
```

### 2.3 Current Issues
- **Security Gap**: List endpoint returns decrypted values
- **No Global Variables**: No support for non-encrypted variables
- **Inconsistent UI**: Credential values sometimes visible
- **Single Purpose**: Only handles encrypted credentials

## 3. Proposed Solution Architecture

### 3.1 Unified Data Model

```sql
CREATE TABLE variables (
    id VARCHAR PRIMARY KEY,
    key_name VARCHAR NOT NULL,
    description VARCHAR,
    value VARCHAR NOT NULL,  -- Encrypted for credentials, plain for global vars
    type VARCHAR(20) NOT NULL,  -- 'credential' or 'global-variable'
    owner_id VARCHAR NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_used_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_variables_owner_type ON variables(owner_id, type);
CREATE UNIQUE INDEX idx_variables_owner_key_type ON variables(owner_id, key_name, type);
```

### 3.2 Unified API Design

**Base Endpoint**: `/api/v1/variables`

**Type Parameter**: `?type={credential|global-variable}`

**Endpoints**:
```
POST   /api/v1/variables?type={type}          # Create variable
GET    /api/v1/variables?type={type}          # List variables by type
GET    /api/v1/variables/{id}?type={type}     # Get specific variable
PUT    /api/v1/variables/{id}?type={type}     # Update variable
DELETE /api/v1/variables/{id}?type={type}     # Delete variable
```

### 3.3 Type-Based Behavior

| Operation | Credential Behavior | Global Variable Behavior |
|-----------|-------------------|-------------------------|
| **Create** | Encrypt value before storage | Store value as plain text |
| **List** | Return `value: null, has_value: true` | Return `value: "actual_value"` |
| **Get** | Return `value: null, has_value: true` | Return `value: "actual_value"` |
| **Update** | Replace encrypted value (no preview) | Show current value, allow edit |
| **Delete** | Standard deletion | Standard deletion |

## 4. Detailed Implementation Plan

### 4.1 Database Migration Strategy

#### Phase 1: Table Creation
```sql
-- Create new variables table
CREATE TABLE variables (
    id VARCHAR PRIMARY KEY,
    key_name VARCHAR NOT NULL,
    description VARCHAR,
    value VARCHAR NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'credential',
    owner_id VARCHAR NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_used_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_variables_owner_type ON variables(owner_id, type);
CREATE UNIQUE INDEX idx_variables_owner_key_type ON variables(owner_id, key_name, type);
```

#### Phase 2: Data Migration
```sql
-- Migrate existing credentials
INSERT INTO variables (id, key_name, description, value, type, owner_id, created_at, updated_at, last_used_at)
SELECT id, key_name, description, value, 'credential', owner_id, created_at, updated_at, last_used_at
FROM credentials;
```

#### Phase 3: Verification & Cleanup
- Verify data integrity
- Update application code
- Drop old credentials table (after confirmation)

### 4.2 Backend Implementation

#### User Service Changes
```python
# New Variable model
class Variable(Base):
    __tablename__ = "variables"
    id = Column(String, primary_key=True)
    key_name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    value = Column(String, nullable=False)
    type = Column(Enum(VariableType), nullable=False)
    owner_id = Column(String, ForeignKey('users.id'), nullable=False)
    # ... timestamps

# Updated service methods
class VariableService:
    def create_variable(self, request):
        if request.type == "credential":
            processed_value = self.encryption_manager.encrypt(request.value, request.owner_id)
        else:
            processed_value = request.value
        # ... create variable
    
    def list_variables(self, request):
        variables = query_by_owner_and_type(request.owner_id, request.type)
        for var in variables:
            if var.type == "credential":
                var.value = None  # Hide credential values
                var.has_value = bool(var.value)
            else:
                var.value = var.value  # Show global variable values
        return variables
```

#### API Gateway Changes
```python
# New unified router
@variable_router.post("")
async def create_variable(
    variable_data: VariableCreate,
    type: VariableType = Query(...),
    current_user: dict = Depends(role_required(["user"]))
):
    response = await user_service.create_variable(
        owner_id=current_user["user_id"],
        key_name=variable_data.key_name,
        value=variable_data.value,
        description=variable_data.description,
        type=type.value
    )
    return response

@variable_router.get("")
async def list_variables(
    type: VariableType = Query(...),
    current_user: dict = Depends(role_required(["user"]))
):
    response = await user_service.list_variables(
        owner_id=current_user["user_id"],
        type=type.value
    )
    return response
```

### 4.3 Frontend Implementation

#### API Service Layer
```typescript
export enum VariableType {
  CREDENTIAL = 'credential',
  GLOBAL_VARIABLE = 'global-variable'
}

export class VariableService {
  async createVariable(variable: VariableCreate, type: VariableType): Promise<Variable> {
    return await api.post(`/variables?type=${type}`, variable);
  }

  async listVariables(type: VariableType): Promise<Variable[]> {
    const response = await api.get(`/variables?type=${type}`);
    return response.data.variables;
  }

  async updateVariable(id: string, variable: VariableUpdate, type: VariableType): Promise<Variable> {
    return await api.put(`/variables/${id}?type=${type}`, variable);
  }
}
```

#### UI Components
```typescript
// Unified Variable Manager
export function VariableManager({ type }: { type: VariableType }) {
  const isCredentialMode = type === VariableType.CREDENTIAL;
  
  return (
    <div>
      <h2>{isCredentialMode ? 'Credentials' : 'Global Variables'}</h2>
      <VariableTable 
        variables={variables}
        type={type}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    </div>
  );
}

// Variable Table with type-specific rendering
export function VariableTable({ variables, type, onEdit, onDelete }) {
  const isCredentialMode = type === VariableType.CREDENTIAL;
  
  return (
    <table>
      <tbody>
        {variables.map(variable => (
          <tr key={variable.id}>
            <td>{variable.key_name}</td>
            <td>
              {isCredentialMode ? (
                variable.has_value ? '••••••••' : 'No value'
              ) : (
                variable.value || 'No value'
              )}
            </td>
            <td>
              <button onClick={() => onEdit(variable)}>
                {isCredentialMode ? 'Replace' : 'Edit'}
              </button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
```

## 5. Expected Data Formats

### 5.1 API Request Formats

#### Create Variable Request
```json
POST /api/v1/variables?type=credential
{
  "key_name": "openai_api_key",
  "value": "sk-1234567890abcdef",
  "description": "OpenAI API key for GPT models"
}
```

```json
POST /api/v1/variables?type=global-variable
{
  "key_name": "company_name",
  "value": "RUH AI",
  "description": "Company name for templates"
}
```

#### Update Variable Request
```json
PUT /api/v1/variables/{id}?type=credential
{
  "value": "sk-new-api-key-value"
}
```

```json
PUT /api/v1/variables/{id}?type=global-variable
{
  "key_name": "updated_company_name",
  "value": "RUH AI Technologies",
  "description": "Updated company name"
}
```

### 5.2 API Response Formats

#### List Credentials Response
```json
GET /api/v1/variables?type=credential
{
  "success": true,
  "message": "Variables retrieved successfully",
  "variables": [
    {
      "id": "uuid-1234",
      "key_name": "openai_api_key",
      "description": "OpenAI API key for GPT models",
      "type": "credential",
      "value": null,
      "has_value": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "last_used_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### List Global Variables Response
```json
GET /api/v1/variables?type=global-variable
{
  "success": true,
  "message": "Variables retrieved successfully",
  "variables": [
    {
      "id": "uuid-5678",
      "key_name": "company_name",
      "description": "Company name for templates",
      "type": "global-variable",
      "value": "RUH AI",
      "has_value": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "last_used_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 5.3 Error Response Format
```json
{
  "success": false,
  "message": "Variable with this key_name already exists",
  "code": "VARIABLE_ALREADY_EXISTS",
  "details": {
    "field": "key_name",
    "value": "duplicate_key",
    "type": "credential"
  }
}
```

## 6. CRUD Operations Specification

### 6.1 Create Operation

#### Credential Creation
```
Endpoint: POST /api/v1/variables?type=credential
Flow:
1. Validate request body (key_name, value, description)
2. Check for duplicate key_name for user + type combination
3. Generate/retrieve user encryption key from Google Secret Manager
4. Encrypt value using user's key
5. Store encrypted value in database with type='credential'
6. Return success response (no value in response)

Security: Value is encrypted before storage, never logged
```

#### Global Variable Creation
```
Endpoint: POST /api/v1/variables?type=global-variable
Flow:
1. Validate request body (key_name, value, description)
2. Check for duplicate key_name for user + type combination
3. Store plain text value in database with type='global-variable'
4. Return success response

Security: Value stored as plain text, suitable for non-sensitive data
```

### 6.2 Read Operations

#### List Credentials
```
Endpoint: GET /api/v1/variables?type=credential
Flow:
1. Query variables table filtered by owner_id and type='credential'
2. For each credential: set value=null, has_value=true
3. Return list with hidden values

Response: Values are always null for security
```

#### List Global Variables
```
Endpoint: GET /api/v1/variables?type=global-variable
Flow:
1. Query variables table filtered by owner_id and type='global-variable'
2. Return actual values for each global variable
3. Return list with visible values

Response: Actual values are returned
```

#### Get Single Variable
```
Endpoint: GET /api/v1/variables/{id}?type={type}
Flow:
1. Query specific variable by id and owner_id
2. Verify type matches query parameter
3. Apply type-specific value handling (hide for credentials, show for global vars)
4. Return variable details

Security: Type verification prevents access to wrong variable type
```

### 6.3 Update Operation

#### Credential Update
```
Endpoint: PUT /api/v1/variables/{id}?type=credential
Flow:
1. Validate request body
2. Find existing credential by id and owner_id
3. Encrypt new value using user's key
4. Replace encrypted value in database (no preview of old value)
5. Update timestamps
6. Return success response (no value in response)

UX: User cannot see current value, must provide new value to replace
```

#### Global Variable Update
```
Endpoint: PUT /api/v1/variables/{id}?type=global-variable
Flow:
1. Validate request body
2. Find existing global variable by id and owner_id
3. Update fields (key_name, value, description) as provided
4. Store plain text value in database
5. Update timestamps
6. Return success response with updated values

UX: User can see current value and edit it
```

### 6.4 Delete Operation

```
Endpoint: DELETE /api/v1/variables/{id}?type={type}
Flow:
1. Find variable by id and owner_id
2. Verify type matches query parameter
3. Delete record from database
4. Return success response

Behavior: Same for both types, permanent deletion
```

## 7. Frontend Configuration

### 7.1 Route Structure

```
/settings/credentials          → VariableManager with type='credential'
/settings/global-variables     → VariableManager with type='global-variable'
```

### 7.2 Component Architecture

```typescript
// Main container component
<VariableManager type={VariableType.CREDENTIAL} />
<VariableManager type={VariableType.GLOBAL_VARIABLE} />

// Shared components with type-aware behavior
<VariableTable variables={variables} type={type} />
<VariableForm variable={variable} type={type} mode="create|edit" />
<VariableDeleteDialog variable={variable} type={type} />
```

### 7.3 Type-Specific UI Behavior

#### Credential Mode UI
```typescript
// Table display
<td>
  {variable.has_value ? (
    <span className="credential-hidden">••••••••</span>
  ) : (
    <span className="no-value">No value set</span>
  )}
</td>

// Edit form
<CredentialInput
  label="New Credential Value"
  type="password"
  placeholder="Enter new credential value"
  showCurrentValue={false}  // Never show current value
/>

// Action buttons
<Button>Replace Value</Button>  // Not "Edit"
```

#### Global Variable Mode UI
```typescript
// Table display
<td>
  <span className="variable-value">
    {variable.value || 'No value set'}
  </span>
</td>

// Edit form
<TextInput
  label="Variable Value"
  value={variable.value}  // Show current value
  placeholder="Enter variable value"
/>

// Action buttons
<Button>Edit</Button>  // Standard edit button
```

### 7.4 Form Validation

```typescript
// Shared validation rules
const baseValidation = {
  key_name: {
    required: true,
    minLength: 1,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9_-]+$/,
    message: "Key name can only contain letters, numbers, underscores, and hyphens"
  },
  description: {
    maxLength: 255
  }
};

// Type-specific validation
const credentialValidation = {
  ...baseValidation,
  value: {
    required: true,
    minLength: 1,
    message: "Credential value is required"
  }
};

const globalVariableValidation = {
  ...baseValidation,
  value: {
    required: true,
    minLength: 1,
    message: "Variable value is required"
  }
};
```

## 8. Filtering and Search Mechanisms

### 8.1 Backend Filtering

#### Type-Based Filtering
```python
# Automatic filtering by type parameter
@variable_router.get("")
async def list_variables(
    type: VariableType = Query(...),  # Required parameter
    search: Optional[str] = Query(None),
    sort_by: Optional[str] = Query("created_at"),
    sort_order: Optional[str] = Query("desc"),
    current_user: dict = Depends(role_required(["user"]))
):
    filters = {
        "owner_id": current_user["user_id"],
        "type": type.value
    }

    if search:
        filters["search"] = search  # Search in key_name and description

    variables = await user_service.list_variables(**filters)
    return variables
```

#### Search Implementation
```python
# In user service
def list_variables(self, owner_id: str, type: str, search: str = None):
    query = db.query(Variable).filter(
        Variable.owner_id == owner_id,
        Variable.type == VariableType(type)
    )

    if search:
        query = query.filter(
            or_(
                Variable.key_name.ilike(f"%{search}%"),
                Variable.description.ilike(f"%{search}%")
            )
        )

    return query.all()
```

### 8.2 Frontend Filtering

#### Search Component
```typescript
export function VariableSearch({
  type,
  onSearch,
  onSort
}: {
  type: VariableType;
  onSearch: (query: string) => void;
  onSort: (field: string, order: 'asc' | 'desc') => void;
}) {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="variable-search">
      <SearchInput
        placeholder={`Search ${type === VariableType.CREDENTIAL ? 'credentials' : 'global variables'}...`}
        value={searchQuery}
        onChange={(value) => {
          setSearchQuery(value);
          onSearch(value);
        }}
      />

      <SortDropdown
        options={[
          { value: 'key_name', label: 'Name' },
          { value: 'created_at', label: 'Created Date' },
          { value: 'updated_at', label: 'Updated Date' }
        ]}
        onSort={onSort}
      />
    </div>
  );
}
```

#### Filter State Management
```typescript
export function VariableManager({ type }: { type: VariableType }) {
  const [variables, setVariables] = useState<Variable[]>([]);
  const [filteredVariables, setFilteredVariables] = useState<Variable[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortConfig, setSortConfig] = useState({ field: 'created_at', order: 'desc' });

  // Load variables based on type
  useEffect(() => {
    loadVariables();
  }, [type]);

  // Apply search and sort
  useEffect(() => {
    let filtered = variables;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(variable =>
        variable.key_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        variable.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply sort
    filtered = filtered.sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];

      if (sortConfig.order === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredVariables(filtered);
  }, [variables, searchQuery, sortConfig]);

  const loadVariables = async () => {
    try {
      const data = await variableService.listVariables(type);
      setVariables(data);
    } catch (error) {
      console.error('Failed to load variables:', error);
    }
  };

  return (
    <div>
      <VariableSearch
        type={type}
        onSearch={setSearchQuery}
        onSort={(field, order) => setSortConfig({ field, order })}
      />

      <VariableTable
        variables={filteredVariables}
        type={type}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    </div>
  );
}
```

### 8.3 URL-Based Filtering

```typescript
// Route configuration
const routes = [
  {
    path: '/settings/credentials',
    component: () => <VariableManager type={VariableType.CREDENTIAL} />
  },
  {
    path: '/settings/global-variables',
    component: () => <VariableManager type={VariableType.GLOBAL_VARIABLE} />
  }
];

// Navigation component
export function VariableNavigation() {
  const location = useLocation();

  return (
    <nav className="variable-nav">
      <NavLink
        to="/settings/credentials"
        className={location.pathname === '/settings/credentials' ? 'active' : ''}
      >
        Credentials
      </NavLink>

      <NavLink
        to="/settings/global-variables"
        className={location.pathname === '/settings/global-variables' ? 'active' : ''}
      >
        Global Variables
      </NavLink>
    </nav>
  );
}
```

## 9. Security Considerations

### 9.1 Data Protection

#### Credential Security
```
- Encryption: Per-user Fernet keys stored in Google Secret Manager
- Storage: Encrypted values in database, never plain text
- Transmission: HTTPS/TLS for all API calls
- Logging: Never log credential values, only metadata
- Memory: Decrypt only when needed, clear from memory immediately
- Access: User-scoped access only, no cross-user visibility
```

#### Global Variable Security
```
- Storage: Plain text in database (by design for visibility)
- Access: User-scoped access only
- Validation: Input sanitization and validation
- Logging: Safe to log (non-sensitive data)
- Transmission: HTTPS/TLS for all API calls
```

### 9.2 Authentication & Authorization

```python
# All endpoints require authentication
@variable_router.post("")
async def create_variable(
    current_user: dict = Depends(role_required(["user"]))  # JWT validation
):
    # User can only create variables for themselves
    owner_id = current_user["user_id"]

# Database queries always filter by owner_id
def list_variables(self, owner_id: str, type: str):
    return db.query(Variable).filter(
        Variable.owner_id == owner_id,  # User isolation
        Variable.type == VariableType(type)
    ).all()
```

### 9.3 Input Validation

```python
# API Gateway validation
class VariableCreate(BaseModel):
    key_name: str = Field(
        ...,
        min_length=1,
        max_length=50,
        regex=r'^[a-zA-Z0-9_-]+$'  # Prevent injection
    )
    value: str = Field(..., min_length=1, max_length=10000)
    description: Optional[str] = Field(None, max_length=255)

# Additional validation in service layer
def validate_variable_create(self, request):
    # Check for SQL injection patterns
    # Validate against business rules
    # Sanitize input data
```

## 10. Performance Considerations

### 10.1 Database Optimization

```sql
-- Indexes for efficient queries
CREATE INDEX idx_variables_owner_type ON variables(owner_id, type);
CREATE INDEX idx_variables_search ON variables(owner_id, key_name, description);
CREATE UNIQUE INDEX idx_variables_owner_key_type ON variables(owner_id, key_name, type);

-- Query optimization
SELECT * FROM variables
WHERE owner_id = ? AND type = ?
ORDER BY created_at DESC
LIMIT 100;  -- Pagination for large datasets
```

### 10.2 Caching Strategy

```python
# Redis caching for frequently accessed variables
@cache(ttl=300)  # 5 minute cache
def list_variables(self, owner_id: str, type: str):
    # Cache non-sensitive metadata only
    # Never cache decrypted credential values
    pass

# Cache invalidation on updates
def update_variable(self, variable_id: str, data: dict):
    result = self._update_variable(variable_id, data)
    cache.delete(f"variables:{owner_id}:{type}")
    return result
```

### 10.3 API Rate Limiting

```python
# Rate limiting per user
@limiter.limit("100/minute")
@variable_router.post("")
async def create_variable():
    pass

@limiter.limit("500/minute")  # Higher limit for reads
@variable_router.get("")
async def list_variables():
    pass
```

## 11. Testing Strategy

### 11.1 Unit Tests

```python
# Test credential encryption/decryption
def test_credential_creation_encrypts_value():
    service = VariableService()
    request = CreateVariableRequest(
        owner_id="user123",
        key_name="test_key",
        value="secret_value",
        type="credential"
    )

    result = service.create_variable(request)

    # Verify value is encrypted in database
    stored_variable = db.query(Variable).filter(Variable.id == result.id).first()
    assert stored_variable.value != "secret_value"
    assert stored_variable.type == VariableType.CREDENTIAL

# Test global variable plain text storage
def test_global_variable_stores_plain_text():
    service = VariableService()
    request = CreateVariableRequest(
        owner_id="user123",
        key_name="company_name",
        value="RUH AI",
        type="global-variable"
    )

    result = service.create_variable(request)

    # Verify value is stored as plain text
    stored_variable = db.query(Variable).filter(Variable.id == result.id).first()
    assert stored_variable.value == "RUH AI"
    assert stored_variable.type == VariableType.GLOBAL_VARIABLE
```

### 11.2 Integration Tests

```python
# Test API endpoints
def test_create_credential_endpoint():
    response = client.post(
        "/api/v1/variables?type=credential",
        json={
            "key_name": "test_credential",
            "value": "secret_value",
            "description": "Test credential"
        },
        headers={"Authorization": f"Bearer {jwt_token}"}
    )

    assert response.status_code == 201
    assert response.json()["success"] == True
    assert "value" not in response.json()  # Value should not be returned

def test_list_credentials_hides_values():
    response = client.get(
        "/api/v1/variables?type=credential",
        headers={"Authorization": f"Bearer {jwt_token}"}
    )

    assert response.status_code == 200
    variables = response.json()["variables"]
    for variable in variables:
        assert variable["value"] is None
        assert variable["has_value"] == True
```

### 11.3 Frontend Tests

```typescript
// Test component behavior
describe('VariableManager', () => {
  it('should hide credential values in table', () => {
    render(<VariableManager type={VariableType.CREDENTIAL} />);

    // Should show masked values
    expect(screen.getByText('••••••••')).toBeInTheDocument();
    expect(screen.queryByText('actual_secret_value')).not.toBeInTheDocument();
  });

  it('should show global variable values in table', () => {
    render(<VariableManager type={VariableType.GLOBAL_VARIABLE} />);

    // Should show actual values
    expect(screen.getByText('RUH AI')).toBeInTheDocument();
  });
});
```

## 12. Migration Plan

### 12.1 Pre-Migration Checklist

- [ ] Backup existing credentials table
- [ ] Test migration script on staging environment
- [ ] Verify all existing credentials can be decrypted
- [ ] Ensure Google Secret Manager access is working
- [ ] Update monitoring and alerting for new table structure

### 12.2 Migration Steps

#### Step 1: Database Migration
```bash
# Run migration script
python user-service/migrations/migrate_credentials_to_variables.py migrate

# Verify migration
python user-service/migrations/migrate_credentials_to_variables.py verify
```

#### Step 2: Deploy Backend Changes
```bash
# Deploy user-service with new Variable model
# Deploy api-gateway with new variable endpoints
# Keep old credential endpoints for backward compatibility
```

#### Step 3: Deploy Frontend Changes
```bash
# Deploy new variable management UI
# Update existing credential screens to use new endpoints
# Test both credential and global variable functionality
```

#### Step 4: Cleanup
```bash
# After confirming everything works
python user-service/migrations/migrate_credentials_to_variables.py cleanup

# Remove old credential endpoints (after grace period)
```

### 12.3 Rollback Plan

```bash
# If issues occur, rollback database changes
python user-service/migrations/migrate_credentials_to_variables.py rollback

# Revert application deployments
# Restore from backup if necessary
```

## 13. Monitoring and Observability

### 13.1 Key Metrics

```python
# Track variable operations
metrics = {
    "variable_create_total": Counter("variable_create_total", ["type", "status"]),
    "variable_list_total": Counter("variable_list_total", ["type"]),
    "variable_update_total": Counter("variable_update_total", ["type", "status"]),
    "variable_delete_total": Counter("variable_delete_total", ["type", "status"]),
    "encryption_operation_duration": Histogram("encryption_operation_duration"),
    "api_request_duration": Histogram("api_request_duration", ["endpoint", "method"])
}
```

### 13.2 Logging Strategy

```python
# Structured logging
logger.info("Variable created", extra={
    "user_id": user_id,
    "variable_id": variable.id,
    "variable_type": variable.type.value,
    "key_name": variable.key_name,
    # Never log variable.value for credentials
})

logger.info("Variable accessed", extra={
    "user_id": user_id,
    "variable_id": variable_id,
    "variable_type": variable_type,
    "operation": "list|get|update|delete"
})
```

### 13.3 Health Checks

```python
@health_router.get("/variables/health")
async def variables_health_check():
    checks = {
        "database": await check_database_connection(),
        "encryption": await check_encryption_service(),
        "google_secret_manager": await check_gsm_access()
    }

    all_healthy = all(checks.values())

    return {
        "status": "healthy" if all_healthy else "unhealthy",
        "checks": checks,
        "timestamp": datetime.utcnow().isoformat()
    }
```

## 14. Success Metrics

### 14.1 Technical Metrics
- [ ] API response time < 200ms for list operations
- [ ] API response time < 100ms for create/update operations
- [ ] 99.9% uptime for variable endpoints
- [ ] Zero credential value leaks in logs or responses
- [ ] Successful migration of 100% of existing credentials

### 14.2 User Experience Metrics
- [ ] Users can create both credentials and global variables
- [ ] Credential values are never visible to users
- [ ] Global variable values are always visible to users
- [ ] Search and filtering work for both types
- [ ] No user-reported security issues

### 14.3 Business Metrics
- [ ] Increased usage of global variables for template systems
- [ ] Reduced support tickets related to credential management
- [ ] Improved developer productivity with unified interface
- [ ] Enhanced security posture with proper credential handling

---

## 15. Conclusion

This unified credentials and global variables system provides a secure, scalable, and user-friendly solution that maintains the security of sensitive credentials while enabling transparent management of global variables. The implementation plan ensures backward compatibility, proper security measures, and a smooth migration path from the existing system.

The key differentiator is the type-based behavior that automatically handles encryption for credentials while providing transparency for global variables, all through a single, consistent API interface.
```
