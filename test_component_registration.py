#!/usr/bin/env python3
"""
Test component registration and discovery.
"""

import sys
import os

def test_workflow_service_discovery():
    """Test if workflow service can discover the regex extractor component."""
    print("🧪 Testing Workflow Service Component Discovery...")
    
    # Add workflow-service to path
    sys.path.insert(0, '/Users/<USER>/Desktop/ruh_ai/backend/workflow-service')
    
    try:
        # Try to import the component discovery system
        from app.utils.component_loader import load_all_components
        from app.components.processing.regex_extractor import RegexExtractorComponent
        
        print("  ✅ Successfully imported component loader and regex extractor")
        
        # Test component definition
        component = RegexExtractorComponent()
        definition = component.get_definition()
        
        if definition and definition.get('name') == 'RegexExtractorComponent':
            print(f"  ✅ Component definition generated: {definition['name']}")
            print(f"    Category: {definition['category']}")
            print(f"    Inputs: {len(definition['inputs'])}")
            print(f"    Outputs: {len(definition['outputs'])}")
            return True
        else:
            print("  ❌ Component definition invalid")
            return False
            
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
        return False

def test_node_executor_registration():
    """Test if node-executor service can register the regex extractor component."""
    print("\n🧪 Testing Node Executor Service Component Registration...")
    
    # Add node-executor-service to path
    sys.path.insert(0, '/Users/<USER>/Desktop/ruh_ai/backend/node-executor-service')
    
    try:
        # Import the component system first to get the registry
        from app.core_.component_system import COMPONENT_REGISTRY, discover_component_modules
        
        print(f"  📋 Registry before discovery: {list(COMPONENT_REGISTRY.keys())}")
        
        # Import our component to trigger registration
        from app.components.regex_extractor_component import RegexExtractorComponent
        
        print(f"  📋 Registry after import: {list(COMPONENT_REGISTRY.keys())}")
        
        # Check if our component is registered
        if 'RegexExtractorComponent' in COMPONENT_REGISTRY:
            print("  ✅ RegexExtractorComponent is registered")
            
            # Test instantiation
            component_class = COMPONENT_REGISTRY['RegexExtractorComponent']
            component_instance = component_class()
            print(f"  ✅ Component instantiated: {component_instance.__class__.__name__}")
            
            return True
        else:
            print("  ❌ RegexExtractorComponent not found in registry")
            return False
            
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
        import traceback
        print(f"  🔍 Traceback: {traceback.format_exc()}")
        return False

def test_component_discovery():
    """Test the automatic component discovery."""
    print("\n🧪 Testing Automatic Component Discovery...")
    
    try:
        # This should discover all components including our new one
        from app.core_.component_system import discover_component_modules, COMPONENT_REGISTRY
        
        print(f"  📋 Registry before discovery: {list(COMPONENT_REGISTRY.keys())}")
        
        # Run discovery
        imported_modules = discover_component_modules()
        
        print(f"  📋 Registry after discovery: {list(COMPONENT_REGISTRY.keys())}")
        print(f"  📦 Imported modules: {len(imported_modules)}")
        
        if 'RegexExtractorComponent' in COMPONENT_REGISTRY:
            print("  ✅ RegexExtractorComponent discovered successfully")
            return True
        else:
            print("  ❌ RegexExtractorComponent not discovered")
            return False
            
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
        import traceback
        print(f"  🔍 Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all registration tests."""
    print("🚀 Testing Regex Extractor Component Registration")
    print("=" * 60)
    
    results = []
    
    # Test workflow service discovery
    results.append(test_workflow_service_discovery())
    
    # Test node executor registration  
    results.append(test_node_executor_registration())
    
    # Test automatic discovery
    results.append(test_component_discovery())
    
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All registration tests passed ({passed}/{total})!")
        print("\n📝 Next Steps:")
        print("  1. Both components are properly structured for registration")
        print("  2. The workflow-service component defines the UI schema correctly")
        print("  3. The node-executor-service component registers with the processing system")
        print("  4. Start/restart both services to activate the new component")
        print("  5. The component should appear in the workflow builder UI")
        return 0
    else:
        print(f"💥 Some registration tests failed ({passed}/{total})")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)