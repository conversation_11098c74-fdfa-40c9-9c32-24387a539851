'use client';
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  variableName: string;
}

export default function DeleteConfirmationDialog({ 
  open, 
  onOpenChange, 
  onConfirm, 
  variableName 
}: DeleteConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[500px] w-full rounded-[20px] bg-white dark:bg-[#232326] border border-gray-200 dark:border-none shadow-[0_8px_32px_0_rgba(0,0,0,0.40)] p-6 font-[Satoshi]">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <DialogTitle className="text-gray-900 dark:text-white text-[20px] font-bold">
              Delete Variable
            </DialogTitle>
          </div>
        </DialogHeader>
        
        <div className="mb-6">
          <p className="text-gray-600 dark:text-gray-300 text-[16px] leading-relaxed">
            Are you sure you want to delete the variable <span className="font-semibold text-gray-900 dark:text-white">"{variableName}"</span>? 
            This action cannot be undone.
          </p>
        </div>

        <div className="flex justify-end gap-3">
          <button
            type="button"
            className="bg-white text-gray-900 dark:text-black font-[Satoshi] font-medium rounded-md px-6 py-2.5 text-sm border border-gray-300 dark:border-[#393939] hover:bg-gray-50 dark:hover:bg-gray-100 transition-colors"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </button>
          <button
            type="button"
            className="bg-red-600 hover:bg-red-700 text-white font-[Satoshi] font-medium rounded-md px-6 py-2.5 text-sm transition-colors"
            onClick={onConfirm}
          >
            Delete
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 