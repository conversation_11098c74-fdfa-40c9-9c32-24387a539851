Product Requirements Document: Regex Extractor Component
Document Version: 1.0
Date: October 27, 2023
Author: [Your Name/Team]
Status: Draft
1. Introduction
1.1. Problem Statement
Workflow users frequently encounter data in unstructured or semi-structured text formats, such as email bodies, log files, raw API responses, and scraped web content. Currently, there is no simple, built-in way for users to parse this text and extract specific pieces of information (e.g., an order ID, a username, an error code). This limitation forces users to write custom code or use complex workarounds, which undermines the value proposition of a low-code/no-code workflow builder.
1.2. Proposed Solution
We will develop a new workflow component called the "Regex Extractor." This node will empower users to apply powerful regular expression (regex) patterns to an input text, extract the desired data in a structured format, and pass it to subsequent steps in the workflow. A key feature will be an interactive testing environment to help users build and validate their patterns in real-time.
1.3. Goals & Objectives
Empower Users: Enable non-developers to perform complex text parsing and data extraction.
Increase Use Cases: Unlock new automation possibilities involving logs, emails, and unstructured text from any source.
Improve User Experience: Provide an intuitive and powerful interface for a typically complex task, reducing user frustration and the need for support.
Reduce Reliance on Code: Decrease the number of workflows that require a custom "code" step for simple text manipulation.
2. User Personas
DevOps Engineer (<PERSON>): Needs to parse alerts from monitoring systems to extract hostnames, IP addresses, and error codes to trigger automated remediation workflows.
Marketing Operations Specialist (Mark): Needs to extract lead information (name, email, company) from the body of inbound contact form emails to add them to a CRM.
Data Analyst (Dana): Needs to pull specific metrics or identifiers from raw log files or semi-structured reports before loading them into a database.
3. Functional Requirements
3.1. Component on Workflow Canvas
Name: Regex Extractor
Icon: A magnifying glass over text (</>) or a text symbol with brackets [a-z].
Ports: Standard input and output ports on the left and right.
3.2. Input Ports
Port Name	Data Type	Required	Description
Input Text	String	Yes	The source text content to be searched.
Regex Pattern	String	No	Dynamically provides a regex pattern from a previous step. Overrides the pattern set in the configuration panel.
3.3. Configuration Panel
When the component is selected, a settings panel will open with the following fields:
Regex Pattern (Required): A multi-line text area for the user to input their regular expression.
Live Test Area (Interactive):
Sample Input Text: A text area where users can paste sample data to test their pattern.
Live Matches: A read-only view that automatically updates to show the results of the pattern applied to the sample text. It should highlight the matched strings and capture groups. This is a critical usability feature.
Extraction Mode (Radio Buttons):
First Match Only (Default): The component finds the first occurrence that matches the pattern and stops.
All Matches: The component finds all non-overlapping occurrences that match the pattern.
Output Format (Dropdown):
List of Strings (Full Match) (Default): Outputs the entire matched text for each match.
List of Strings (First Capture Group): Outputs only the content of the first capturing group (...) for each match.
Structured Object (Named Groups): If the pattern uses named capture groups (?<name>...), the output will be an object (for First Match) or a list of objects (for All Matches).
Regex Flags (Checkboxes):
[ ] Case Insensitive (i)
[ ] Multiline (m) (^ and $ match start/end of lines)
[ ] Dot All (s) (. matches newlines)
On No Match (Dropdown): Defines component behavior when no match is found.
Continue with empty value (Default): The workflow proceeds. Output ports will contain null or empty values ([], 0, false).
Fail the workflow: The workflow execution halts and is marked as failed.
3.4. Output Ports
Port Name	Data Type	Description
Matches	String / Object / List	The primary output. Its structure is determined by the Extraction Mode and Output Format settings.
First Match	String / Object	A convenience output that always contains only the first result. null if no match.
Match Count	Integer	The total number of matches found (e.g., 0, 1, 5).
Is Match Found	Boolean	A simple true or false value.
4. User Stories
As Mark (Marketing Ops), I want to extract an order ID from an email body so that I can look up the order in my database.
As Devin (DevOps), I want to extract all IP addresses that failed to authenticate from a log file so that I can iterate through them and add them to a firewall blocklist.
As Dana (Data Analyst), I want to parse a line of text containing user=alice, role=admin into a structured object so that I can easily reference user and role in subsequent steps.
As any user, I want to test my regex pattern with sample data directly in the component so that I can be confident it works correctly before running my entire workflow.
5. Non-Functional Requirements
Performance: The regex engine must be efficient. For a 100 KB input text, a typical pattern match should complete in under 100ms. The engine must include a timeout mechanism (e.g., 1-2 seconds) to prevent ReDoS (Regular expression Denial of Service) attacks caused by catastrophic backtracking.
Usability: The Live Test Area is mandatory. The UI should provide tooltips explaining each option and a link to an external regex guide (e.g., regex101.com).
Reliability: The component must handle null or empty string inputs gracefully without crashing the workflow.
6. Out of Scope (Future Enhancements)
A built-in library of common regex patterns (e.g., for Email, URL, IP Address).
A visual, block-based regex builder for users unfamiliar with regex syntax.
This component is not intended to be a full-fledged HTML/XML parser. For complex documents, users should be directed to dedicated parser components.
7. Success Metrics
Adoption: Number and percentage of active workflows that use the Regex Extractor component.
Task Success Rate: High percentage of workflows using this component that run successfully.
Qualitative Feedback: Positive user feedback regarding the component's ease of use, especially the Live Test Area.
Reduction in Support Tickets: A decrease in "How do I parse text?" support requests.
Appendix A: Use Case Examples
This appendix provides concrete examples to guide development and quality assurance.
#	Use Case	Input Text	Regex Pattern	Configuration	Output (Matches)
1	Extract Email	Contact <NAME_EMAIL>	\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b	First Match, Full Match	["<EMAIL>"]
2	Extract All Hashtags	#automation #nocode is the future	#(\w+)	All Matches, 1st Capture Group	["automation", "nocode"]
3	Extract Invoice #	Your invoice is INV-2023-9451.	INV-\d{4}-\d{4}	First Match, Full Match	["INV-2023-9451"]
4	Extract Price	Total: $49.99	\$(\d+\.\d{2})	First Match, 1st Capture Group	["49.99"]
5	Parse URL Parts	https://app.myservice.io/dashboard	https?:\/\/(?<domain>[^/]+)\/(?<path>.*)	First Match, Named Groups	[{"domain": "app.myservice.io", "path": "dashboard"}]
6	Parse Key-Value	user=jdoe;role=admin;	(?<key>\w+)=(?<value>[^;]+);	All Matches, Named Groups	[{"key": "user", "value": "jdoe"}, {"key": "role", "value": "admin"}]
7	Extract H1 Content	<h1>Welcome</h1>	<h1>(.*?)<\/h1>	First Match, 1st Capture Group	["Welcome"]
8	Extract Error Code	Process failed with exit code: 128	exit code:\s*(\d+)	First Match, 1st Capture Group	["128"]
9	Get all href	<a href="/home"><a href="/about">	href="([^"]+)"	All Matches, 1st Capture Group	["/home", "/about"]
10	Extract Acronyms	The API uses REST.	\b([A-Z]{2,})\b	All Matches, 1st Capture Group