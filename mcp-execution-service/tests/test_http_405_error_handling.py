#!/usr/bin/env python3
"""
Test HTTP 405 Method Not Allowed error handling in MCP client.

This test verifies that HTTP 405 errors are properly caught and converted
to MCPHTTPMethodError instead of being treated as retryable connection errors.
"""

import asyncio
import pytest
from unittest.mock import Mock, patch, AsyncMock
import httpx

from app.core_.client import MC<PERSON>lient
from app.core_.exceptions import MCPHTTPMethodError, ErrorCode, ErrorCategory


class TestHTTP405ErrorHandling:
    """Test cases for HTTP 405 error handling."""

    @pytest.mark.asyncio
    async def test_http_405_error_raises_mcp_http_method_error(self):
        """Test that HTTP 405 errors are converted to MCPHTTPMethodError."""
        
        # Create a mock HTTP 405 response
        mock_response = Mock()
        mock_response.status_code = 405
        mock_response.text = "Method Not Allowed"
        
        # Create a mock request
        mock_request = Mock()
        mock_request.method = "POST"
        
        # Create the HTTPStatusError
        http_error = httpx.HTTPStatusError(
            message="405 Method Not Allowed",
            request=mock_request,
            response=mock_response
        )
        
        # Create MCP client
        client = MCPClient(server_url="https://test-server.com/mcp")
        
        # Mock the streamablehttp_client to raise HTTP 405 error
        with patch('app.core_.client.streamablehttp_client') as mock_streamable:
            mock_context = AsyncMock()
            mock_context.__aenter__.side_effect = http_error
            mock_streamable.return_value = mock_context
            
            # Test that the error is properly converted
            with pytest.raises(MCPHTTPMethodError) as exc_info:
                async with client:
                    pass  # This should trigger the connection and raise the error
            
            # Verify the exception details
            error = exc_info.value
            assert error.status_code == 405
            assert error.method == "POST"
            assert error.server_url == "https://test-server.com/mcp"
            assert "Method Not Allowed" in error.reason
            assert error.error_code == ErrorCode.MCP_HTTP_METHOD_ERROR
            assert error.error_category == ErrorCategory.MCP_ERROR
            assert error.retryable is False

    @pytest.mark.asyncio
    async def test_http_401_error_raises_authentication_error(self):
        """Test that HTTP 401 errors are converted to AuthenticationError."""
        
        # Create a mock HTTP 401 response
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = "Unauthorized"
        
        # Create a mock request
        mock_request = Mock()
        mock_request.method = "POST"
        
        # Create the HTTPStatusError
        http_error = httpx.HTTPStatusError(
            message="401 Unauthorized",
            request=mock_request,
            response=mock_response
        )
        
        # Create MCP client
        client = MCPClient(server_url="https://test-server.com/mcp")
        
        # Mock the streamablehttp_client to raise HTTP 401 error
        with patch('app.core_.client.streamablehttp_client') as mock_streamable:
            mock_context = AsyncMock()
            mock_context.__aenter__.side_effect = http_error
            mock_streamable.return_value = mock_context
            
            # Test that the error is properly converted
            from app.schemas.client import AuthenticationError
            with pytest.raises(AuthenticationError) as exc_info:
                async with client:
                    pass  # This should trigger the connection and raise the error
            
            # Verify the exception details
            error = exc_info.value
            assert "HTTP 401 Unauthorized" in str(error)

    @pytest.mark.asyncio
    async def test_http_500_error_raises_connection_error(self):
        """Test that HTTP 500 errors are converted to ConnectionError."""
        
        # Create a mock HTTP 500 response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        
        # Create a mock request
        mock_request = Mock()
        mock_request.method = "POST"
        
        # Create the HTTPStatusError
        http_error = httpx.HTTPStatusError(
            message="500 Internal Server Error",
            request=mock_request,
            response=mock_response
        )
        
        # Create MCP client
        client = MCPClient(server_url="https://test-server.com/mcp")
        
        # Mock the streamablehttp_client to raise HTTP 500 error
        with patch('app.core_.client.streamablehttp_client') as mock_streamable:
            mock_context = AsyncMock()
            mock_context.__aenter__.side_effect = http_error
            mock_streamable.return_value = mock_context
            
            # Test that the error is properly converted
            with pytest.raises(ConnectionError) as exc_info:
                async with client:
                    pass  # This should trigger the connection and raise the error
            
            # Verify the exception details
            error = exc_info.value
            assert "HTTP 500 Server Error" in str(error)

    @pytest.mark.asyncio
    async def test_network_error_raises_connection_error(self):
        """Test that network errors are converted to ConnectionError."""

        # Create a network error
        network_error = httpx.RequestError("Connection failed")

        # Create MCP client
        client = MCPClient(server_url="https://test-server.com/mcp")

        # Mock the streamablehttp_client to raise network error
        with patch('app.core_.client.streamablehttp_client') as mock_streamable:
            mock_context = AsyncMock()
            mock_context.__aenter__.side_effect = network_error
            mock_streamable.return_value = mock_context

            # Test that the error is properly converted
            with pytest.raises(ConnectionError) as exc_info:
                async with client:
                    pass  # This should trigger the connection and raise the error

            # Verify the exception details
            error = exc_info.value
            assert "Network error" in str(error)

    @pytest.mark.asyncio
    async def test_http_405_error_from_message_raises_mcp_http_method_error(self):
        """Test that HTTP 405 errors detected from error messages are converted to MCPHTTPMethodError."""

        # Create a generic exception with HTTP 405 in the message (simulating MCP client behavior)
        generic_error = Exception("HTTP/1.1 405 Method Not Allowed")

        # Create MCP client
        client = MCPClient(server_url="https://test-server.com/mcp")

        # Mock the streamablehttp_client to raise generic error with 405 message
        with patch('app.core_.client.streamablehttp_client') as mock_streamable:
            mock_context = AsyncMock()
            mock_context.__aenter__.side_effect = generic_error
            mock_streamable.return_value = mock_context

            # Test that the error is properly converted
            with pytest.raises(MCPHTTPMethodError) as exc_info:
                async with client:
                    pass  # This should trigger the connection and raise the error

            # Verify the exception details
            error = exc_info.value
            assert error.status_code == 405
            assert error.method == "POST"  # Default method for MCP
            assert error.server_url == "https://test-server.com/mcp"
            assert "405" in error.reason
            assert error.error_code == ErrorCode.MCP_HTTP_METHOD_ERROR
            assert error.error_category == ErrorCategory.MCP_ERROR
            assert error.retryable is False


if __name__ == "__main__":
    # Run a simple test
    async def main():
        test_instance = TestHTTP405ErrorHandling()
        try:
            await test_instance.test_http_405_error_raises_mcp_http_method_error()
            print("✅ HTTP 405 error handling test passed!")
        except Exception as e:
            print(f"❌ HTTP 405 error handling test failed: {e}")
            raise

    asyncio.run(main())
