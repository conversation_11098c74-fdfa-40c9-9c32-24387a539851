#!/usr/bin/env python3
"""
Test script to verify SSH termination handling fix.
This script tests the improved error handling for SSH process termination.
"""

import asyncio
import logging
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from app.core_.direct_ssh_streams import DirectSSHReadStream, DirectMCPSession
from app.core_.client import MCPClient

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MockTerminatedProcess:
    """Mock process that simulates a terminated SSH process."""

    def __init__(self):
        self.returncode = 1  # Simulate terminated process
        self.stdout = None
        self.stderr = None


async def test_direct_ssh_read_stream_termination():
    """Test that DirectSSHReadStream handles termination correctly."""
    logger.info("=== Testing DirectSSHReadStream termination handling ===")

    # Create a mock terminated process
    mock_process = MockTerminatedProcess()

    # Create read stream with terminated process
    read_stream = DirectSSHReadStream(mock_process, logger)

    # Test that read() returns None and logs termination only once
    logger.info("Testing first read() call...")
    result1 = await read_stream.read()
    assert result1 is None, "Expected None from terminated process"

    logger.info("Testing second read() call (should not log again)...")
    result2 = await read_stream.read()
    assert result2 is None, "Expected None from terminated process"

    # Test is_terminated() method
    assert read_stream.is_terminated(), "Expected is_terminated() to return True"

    logger.info("✅ DirectSSHReadStream termination handling test passed")


async def test_async_iterator_termination():
    """Test that async iterator stops correctly on termination."""
    logger.info("=== Testing async iterator termination handling ===")

    # Create a mock terminated process
    mock_process = MockTerminatedProcess()

    # Create read stream with terminated process
    read_stream = DirectSSHReadStream(mock_process, logger)

    # Test async iterator
    message_count = 0
    try:
        iterator = read_stream.__aiter__()
        while True:
            try:
                message = await iterator.__anext__()
                message_count += 1
                # Should not reach here since process is terminated
                break
            except StopAsyncIteration:
                break  # Expected
    except Exception as e:
        logger.info(f"Expected exception during iteration: {e}")

    assert message_count == 0, "Expected no messages from terminated process"
    logger.info("✅ Async iterator termination handling test passed")


class MockLiveProcess:
    """Mock process that simulates a live SSH process."""

    def __init__(self):
        self.returncode = None  # Simulate live process
        self.stdout = MockStdout()
        self.stderr = None


class MockStdout:
    """Mock stdout that provides test data."""

    def __init__(self):
        self.messages = [
            b'{"jsonrpc": "2.0", "id": 1, "result": {"test": "data"}}\n',
            b"",  # EOF
        ]
        self.index = 0

    async def readline(self):
        if self.index < len(self.messages):
            message = self.messages[self.index]
            self.index += 1
            return message
        return b""


async def test_normal_operation():
    """Test that normal operation still works."""
    logger.info("=== Testing normal operation ===")

    # Create a mock live process
    mock_process = MockLiveProcess()

    # Create read stream with live process
    read_stream = DirectSSHReadStream(mock_process, logger)

    # Test that read() works normally
    result = await read_stream.read()
    assert result is not None, "Expected valid message from live process"
    assert result.get("result", {}).get("test") == "data", "Expected test data"

    # Test is_terminated() method
    assert not read_stream.is_terminated(), "Expected is_terminated() to return False"

    logger.info("✅ Normal operation test passed")


async def main():
    """Run all tests."""
    logger.info("Starting SSH termination fix tests...")

    try:
        await test_direct_ssh_read_stream_termination()
        await test_async_iterator_termination()
        await test_normal_operation()

        logger.info("🎉 All tests passed! SSH termination fix is working correctly.")

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
