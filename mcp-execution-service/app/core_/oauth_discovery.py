"""
OAuth Discovery Handler for MCP Client Authentication.

This module handles the OAuth discovery flow where:
1. Make initial tool call without authentication
2. Parse authentication requirements from error response
3. Fetch OAuth credentials based on requirements
4. Construct proper headers and retry the call
"""

import json
import logging
from typing import Dict, Any, List, Optional
import mcp.types as types
from app.services.credential_service import CredentialService
from app.schemas.oauth_discovery import (
    AuthenticationError,
    AuthenticationRequirement,
    OAuthCredentials,
    CachedHeaderRequirements,
)


class OAuthDiscoveryHandler:
    """Handles OAuth discovery flow for MCP tool calls."""

    # Class-level cache for header requirements (shared across all instances)
    _header_requirements_cache: Dict[str, CachedHeaderRequirements] = {}

    def __init__(
        self,
        user_id: Optional[str] = None,
        mcp_config: Optional[Dict[str, Any]] = None,
        credential_service: Optional[CredentialService] = None,
        mcp_client: Optional[Any] = None,
    ):
        self.user_id = user_id
        self.mcp_config = mcp_config
        self.credential_service = credential_service or CredentialService()
        self._mcp_client = mcp_client  # Reference to the MCP client for reconnection
        self.logger = logging.getLogger(f"{__name__}.OAuthDiscoveryHandler")

        # Cache for fetched credentials to avoid repeated API calls (user-specific)
        self._credentials_cache: Dict[str, OAuthCredentials] = {}

    def _get_server_url(self) -> str:
        """Get the server URL from the MCP client."""
        if self._mcp_client and hasattr(self._mcp_client, "server_url"):
            return self._mcp_client.server_url
        return "unknown_server"

    def _get_cached_header_requirements(
        self, tool_name: str
    ) -> Optional[List[AuthenticationRequirement]]:
        """Get cached header requirements for a tool if available and not expired."""
        server_url = self._get_server_url()
        cache_key = CachedHeaderRequirements.create_cache_key(server_url, tool_name)

        cached = self._header_requirements_cache.get(cache_key)
        if cached and not cached.is_expired:
            self.logger.info(
                f"Using cached header requirements for {server_url}/{tool_name}"
            )
            return cached.requirements
        elif cached and cached.is_expired:
            # Clean up expired cache entry
            del self._header_requirements_cache[cache_key]
            self.logger.debug(
                f"Removed expired header requirements cache for {cache_key}"
            )

        return None

    def _cache_header_requirements(
        self, tool_name: str, requirements: List[AuthenticationRequirement]
    ) -> None:
        """Cache header requirements for a tool."""
        server_url = self._get_server_url()
        cache_key = CachedHeaderRequirements.create_cache_key(server_url, tool_name)

        cached_requirements = CachedHeaderRequirements(
            server_url=server_url,
            tool_name=tool_name,
            requirements=requirements,
        )

        self._header_requirements_cache[cache_key] = cached_requirements
        self.logger.info(
            f"Cached header requirements for {server_url}/{tool_name} ({len(requirements)} requirements)"
        )

    @classmethod
    def clear_header_requirements_cache(cls) -> None:
        """Clear the global header requirements cache."""
        cls._header_requirements_cache.clear()

    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """Get statistics about the header requirements cache."""
        total_entries = len(cls._header_requirements_cache)
        expired_entries = sum(
            1 for cached in cls._header_requirements_cache.values() if cached.is_expired
        )

        return {
            "total_entries": total_entries,
            "expired_entries": expired_entries,
            "active_entries": total_entries - expired_entries,
            "cache_keys": list(cls._header_requirements_cache.keys()),
        }

    async def call_tool_with_discovery(
        self, session: Any, tool_name: str, arguments: Dict[str, Any]
    ) -> types.CallToolResult:
        """
        Call tool with OAuth discovery flow.

        Args:
            session: MCP session object
            tool_name: Name of the tool to call
            arguments: Tool arguments

        Returns:
            Tool call result

        Raises:
            Exception: If authentication fails or tool call fails
        """
        self.logger.debug(f"Starting OAuth discovery flow for tool: {tool_name}")

        # Step 1: Check if we have cached header requirements for this tool
        cached_requirements = self._get_cached_header_requirements(tool_name)
        if cached_requirements:
            self.logger.info(
                f"Using cached authentication requirements for {tool_name}"
            )
            auth_requirements = cached_requirements
        else:
            # Step 2: Make initial call without OAuth headers to discover requirements
            try:
                result = await session.call_tool(tool_name, arguments)

                # Step 3: Check if authentication is required
                if not self._is_authentication_error(result):
                    self.logger.debug("Tool call succeeded without authentication")
                    return result

            except Exception as e:
                self.logger.debug(f"Initial tool call failed: {e}")
                # Continue with discovery flow in case the exception contains auth requirements
                result = None

            # Step 4: Parse authentication requirements from the response
            auth_requirements = self._parse_authentication_requirements(result)
            if not auth_requirements:
                self.logger.error(
                    "Authentication required but no requirements found in response"
                )
                self.logger.debug(f"Result type: {type(result)}")
                if hasattr(result, "content"):
                    self.logger.debug(f"Result content: {result.content}")
                if result:
                    return result
                else:
                    raise Exception(
                        "Authentication required but no requirements provided"
                    )
            else:
                # Cache the discovered requirements for future use
                self._cache_header_requirements(tool_name, auth_requirements)

        self.logger.info(f"Found {len(auth_requirements)} authentication requirements")

        # Step 5: Build authentication headers from requirements using fresh credentials
        headers_to_add = await self._build_headers_from_requirements(auth_requirements)
        self.logger.debug(f"Headers to add: {list(headers_to_add.keys())}")

        # Step 6: Add headers to the client's authentication manager and reconnect
        await self._add_oauth_headers_and_reconnect(headers_to_add)

        # Step 7: Retry the tool call with authentication using the new session
        self.logger.info(
            f"Retrying tool call with {len(headers_to_add)} authentication headers"
        )
        try:
            # Use the client's current session after reconnection, not the old session
            new_session = self._mcp_client._session
            if not new_session:
                raise Exception("No valid session available after reconnection")

            result = await new_session.call_tool(tool_name, arguments)
            self.logger.info("Authenticated tool call succeeded")
            return result
        except Exception as e:
            self.logger.error(f"Authenticated tool call failed: {e}")
            raise

    def _is_authentication_error(self, result: Optional[types.CallToolResult]) -> bool:
        """Check if the result indicates an authentication error."""
        if not result:
            return False

        try:
            # Check if result has isError flag
            if hasattr(result, "isError") and result.isError:
                return True

            # Check if result content indicates authentication error
            if hasattr(result, "content") and result.content:
                for content_item in result.content:
                    if hasattr(content_item, "text"):
                        try:
                            # Handle stringified JSON with single quotes
                            text = content_item.text.strip()

                            # Try to parse as Python literal first (handles single quotes)
                            import ast

                            try:
                                error_data = ast.literal_eval(text)
                            except (ValueError, SyntaxError):
                                # Fallback to json.loads
                                error_data = json.loads(text)

                            if (
                                isinstance(error_data, dict)
                                and error_data.get("error", {}).get("code")
                                == "AUTHENTICATION_REQUIRED"
                            ):
                                return True
                        except (
                            json.JSONDecodeError,
                            ValueError,
                            SyntaxError,
                            AttributeError,
                        ):
                            # If not parseable as JSON/dict, check text content
                            if "AUTHENTICATION_REQUIRED" in content_item.text:
                                return True

            return False
        except Exception as e:
            self.logger.debug(f"Error checking authentication status: {e}")
            return False

    def _parse_authentication_requirements(
        self, result: Optional[types.CallToolResult]
    ) -> List[AuthenticationRequirement]:
        """Parse authentication requirements from error response."""
        requirements = []

        if not result or not hasattr(result, "content") or not result.content:
            return requirements

        try:
            for content_item in result.content:
                if hasattr(content_item, "text"):
                    self.logger.debug(
                        f"Parsing content text: {content_item.text[:200]}..."
                    )
                    try:
                        # Handle stringified JSON with single quotes
                        text = content_item.text.strip()

                        # First, try to parse as Python literal (handles single quotes)
                        import ast

                        try:
                            error_data = ast.literal_eval(text)
                            self.logger.debug(
                                "Successfully parsed using ast.literal_eval"
                            )
                        except (ValueError, SyntaxError) as e:
                            self.logger.debug(
                                f"ast.literal_eval failed: {e}, trying json.loads"
                            )
                            # Fallback to json.loads (for proper JSON with double quotes)
                            error_data = json.loads(text)

                        self.logger.debug(f"Parsed error data: {error_data}")

                        if isinstance(error_data, dict) and "error" in error_data:
                            error_details = error_data["error"].get("details", {})
                            auth_reqs = error_details.get(
                                "authentication_requirements", []
                            )
                            self.logger.info(
                                f"Found {len(auth_reqs)} authentication requirements"
                            )

                            for req_data in auth_reqs:
                                requirement = AuthenticationRequirement(
                                    provider=req_data.get("provider"),
                                    auth_type=req_data.get("auth_type"),
                                    header_name=req_data.get("header_name"),
                                    header_format=req_data.get("header_format"),
                                    token_source=req_data.get("token_source"),
                                    required_scopes=req_data.get("required_scopes", []),
                                )
                                requirements.append(requirement)

                    except (json.JSONDecodeError, KeyError, TypeError) as e:
                        self.logger.debug(
                            f"Could not parse authentication requirements: {e}"
                        )
                        continue

        except Exception as e:
            self.logger.error(f"Error parsing authentication requirements: {e}")

        return requirements

    def _get_oauth_tool_name(self) -> str:
        """Get the tool name from oauth_details in MCP config."""
        if self.mcp_config and "oauth_details" in self.mcp_config:
            oauth_details = self.mcp_config["oauth_details"]
            if oauth_details and "tool_name" in oauth_details:
                return oauth_details["tool_name"]

        # Fallback to empty string if not found
        self.logger.warning("Could not find tool_name in oauth_details")
        return ""

    async def _process_auth_requirement(
        self, requirement: AuthenticationRequirement
    ) -> Dict[str, str]:
        """Process a single authentication requirement and return headers."""
        self.logger.debug(
            f"Processing auth requirement for provider: {requirement.provider}"
        )

        # Step 1: Fetch OAuth credentials using the tool_name from oauth_details, not the MCP tool name
        oauth_tool_name = self._get_oauth_tool_name()
        self.logger.info(
            f"Using OAuth tool name '{oauth_tool_name}' from oauth_details for credential fetching"
        )
        credentials = await self._fetch_oauth_credentials(
            requirement.provider, oauth_tool_name
        )

        # Step 2: Extract token based on token_source
        token_value = self._extract_token_from_credentials(
            credentials, requirement.token_source
        )
        if not token_value:
            raise ValueError(
                f"Token source '{requirement.token_source}' not found in credentials"
            )

        # Step 3: Format header value
        header_value = requirement.header_format.format(
            **{requirement.token_source: token_value}
        )

        return {requirement.header_name: header_value}

    async def _fetch_oauth_credentials(
        self, provider: str, tool_name: str
    ) -> OAuthCredentials:
        """Fetch OAuth credentials for the given provider and tool."""
        if not self.user_id:
            raise ValueError("user_id is required for OAuth credential fetching")

        cache_key = f"{self.user_id}_{tool_name}_{provider}"

        # Check cache first
        if cache_key in self._credentials_cache:
            self.logger.debug(f"Using cached credentials for {cache_key}")
            return self._credentials_cache[cache_key]

        # Fetch from credential service
        self.logger.info(
            f"Fetching OAuth credentials for user: {self.user_id}, tool: {tool_name}, provider: {provider}"
        )

        try:
            cred_response = await self.credential_service.get_oauth_credentials(
                user_id=self.user_id, tool_name=tool_name, provider=provider
            )

            credentials = OAuthCredentials.from_api_response(cred_response)

            # Cache the credentials
            self._credentials_cache[cache_key] = credentials

            return credentials

        except Exception as e:
            self.logger.error(f"Failed to fetch OAuth credentials: {e}")
            raise

    def _extract_token_from_credentials(
        self, credentials: OAuthCredentials, token_source: str
    ) -> Optional[str]:
        """Extract specific token from credentials based on token_source dynamically."""
        # Use getattr to dynamically access any field on the credentials object
        # This allows us to support any token_source that the MCP server requests
        token_value = getattr(credentials, token_source, None)

        self.logger.debug(
            f"Extracting token_source '{token_source}': {'found' if token_value else 'not found'}"
        )

        return token_value

    async def _build_headers_from_requirements(
        self, requirements: List[AuthenticationRequirement]
    ) -> Dict[str, str]:
        """
        Build authentication headers from requirements using fresh credentials.

        This method separates the header construction logic from discovery,
        allowing us to use cached requirements with fresh credential values.
        """
        headers_to_add = {}

        for i, requirement in enumerate(requirements):
            try:
                self.logger.debug(
                    f"Building header {i+1}/{len(requirements)}: {requirement.header_name} = {requirement.header_format} (token_source: {requirement.token_source})"
                )
                headers = await self._process_auth_requirement(requirement)
                headers_to_add.update(headers)
                self.logger.info(
                    f"Successfully built header {i+1}: {requirement.header_name}"
                )
            except Exception as e:
                self.logger.error(
                    f"Failed to build header {i+1} for {requirement.provider} (token_source: {requirement.token_source}): {e}"
                )
                continue

        if not headers_to_add:
            raise Exception(
                "Could not build any authentication headers from requirements"
            )

        self.logger.info(
            f"Successfully built {len(headers_to_add)} out of {len(requirements)} authentication headers"
        )

        return headers_to_add

    async def _add_oauth_headers_and_reconnect(self, headers: Dict[str, str]) -> None:
        """Add OAuth headers to the client's authentication manager and reconnect."""
        try:
            # We need access to the MCP client to add headers and reconnect
            # This will be passed from the client when the OAuth discovery handler is created
            if not hasattr(self, "_mcp_client") or not self._mcp_client:
                self.logger.error("MCP client not available for header update")
                raise Exception("MCP client not available for OAuth header update")

            # Add OAuth headers to the authentication manager with user-specific name
            # This prevents cross-user header contamination
            auth_config_name = (
                f"oauth_discovery_headers_{self.user_id}"
                if self.user_id
                else "oauth_discovery_headers"
            )
            self.logger.info(
                f"Adding {len(headers)} OAuth headers to authentication manager with name: {auth_config_name}"
            )
            self._mcp_client.auth_manager.add_custom_auth(
                headers=headers, name=auth_config_name
            )

            # Reconnect to apply the new headers
            self.logger.info("Reconnecting MCP client to apply OAuth headers")
            await self._mcp_client.close()
            await self._mcp_client._connect_with_retry()
            self.logger.info("Successfully reconnected with OAuth headers")

        except Exception as e:
            self.logger.error(f"Failed to add OAuth headers and reconnect: {e}")
            raise
