"""
OAuth-aware MCP Client Factory.

This module provides factory functions to create MCP clients with OAuth discovery capabilities
based on MCP configuration and user context.
"""

import logging
from typing import Dict, Any, Optional
from app.core_.client import MC<PERSON>lient
from app.core_.oauth_discovery import OAuthDiscoveryHandler
from app.services.credential_service import CredentialService
from app.services.authentication_manager import AuthenticationManager
from app.schemas.client import ConnectionConfig


class OAuthAwareMCPClient(MCPClient):
    """
    Extended MCP Client with OAuth discovery capabilities.
    
    This client automatically handles OAuth discovery flow when oauth_details
    are present in the MCP configuration.
    """
    
    def __init__(
        self,
        server_url: Optional[str] = None,
        user_id: Optional[str] = None,
        mcp_config: Optional[Dict[str, Any]] = None,
        credential_service: Optional[CredentialService] = None,
        **kwargs
    ):
        """
        Initialize OAuth-aware MCP client.
        
        Args:
            server_url: MCP server URL
            user_id: User identifier for OAuth credential fetching
            mcp_config: Complete MCP configuration including oauth_details
            credential_service: Service for fetching OAuth credentials
            **kwargs: Additional arguments passed to parent MCPClient
        """
        # Filter out parameters that MCPClient doesn't accept
        filtered_kwargs = kwargs.copy()
        filtered_kwargs.pop('max_retries', None)
        filtered_kwargs.pop('enable_health_check', None)
        filtered_kwargs.pop('timeout', None)

        super().__init__(server_url=server_url, mcp_config=mcp_config, **filtered_kwargs)
        
        self.user_id = user_id
        self.mcp_config = mcp_config or {}
        self.credential_service = credential_service or CredentialService()
        
        # Initialize OAuth discovery handler if oauth_details are present
        self._oauth_discovery_handler = None
        if self._should_use_oauth_discovery():
            self._oauth_discovery_handler = OAuthDiscoveryHandler(
                user_id=self.user_id,
                mcp_config=self.mcp_config,
                credential_service=self.credential_service,
                mcp_client=self  # Pass reference to self for reconnection
            )
            self.logger.info("OAuth discovery handler initialized")
        else:
            self.logger.debug("No oauth_details found, using standard authentication")
    
    def _should_use_oauth_discovery(self) -> bool:
        """Check if OAuth discovery should be used based on MCP config."""
        oauth_details = self.mcp_config.get('oauth_details')
        return (
            oauth_details is not None and
            isinstance(oauth_details, dict) and
            oauth_details.get('provider') and
            oauth_details.get('tool_name') and
            self.user_id is not None
        )
    
    def get_oauth_info(self) -> Optional[Dict[str, str]]:
        """Get OAuth information from MCP config."""
        oauth_details = self.mcp_config.get('oauth_details')
        if oauth_details:
            return {
                'provider': oauth_details.get('provider'),
                'tool_name': oauth_details.get('tool_name'),
                'user_id': self.user_id
            }
        return None


def create_oauth_aware_client(
    server_url: str,
    user_id: str,
    mcp_config: Dict[str, Any],
    credential_service: Optional[CredentialService] = None,
    connection_config: Optional[ConnectionConfig] = None,
    **kwargs
) -> OAuthAwareMCPClient:
    """
    Factory function to create an OAuth-aware MCP client.
    
    This function implements the flow you described:
    1. Check if oauth_details are present in mcp_config
    2. If present, create client with OAuth discovery capabilities
    3. If not present, create standard client
    
    Args:
        server_url: MCP server URL
        user_id: User identifier for OAuth operations
        mcp_config: Complete MCP configuration
        credential_service: Optional credential service instance
        connection_config: Optional connection configuration
        **kwargs: Additional arguments for client creation
        
    Returns:
        Configured OAuthAwareMCPClient instance
    """
    logger = logging.getLogger(__name__)
    
    # Extract oauth_details from config
    oauth_details = mcp_config.get('oauth_details')
    
    if oauth_details:
        logger.info(
            f"Creating OAuth-aware client for provider: {oauth_details.get('provider')}, "
            f"tool: {oauth_details.get('tool_name')}, user: {user_id}"
        )
    else:
        logger.info("Creating standard MCP client (no oauth_details found)")
    
    # Create authentication manager
    auth_manager = AuthenticationManager()
    
    # Set up connection config
    if connection_config is None:
        connection_config = ConnectionConfig(
            max_retries=kwargs.pop('max_retries', 3),
            enable_health_check=kwargs.pop('enable_health_check', True),
        )

    # Remove parameters that MCPClient doesn't accept directly
    kwargs.pop('timeout', None)  # Remove timeout if present

    # Create the client
    client = OAuthAwareMCPClient(
        server_url=server_url,
        user_id=user_id,
        mcp_config=mcp_config,
        credential_service=credential_service,
        auth_manager=auth_manager,
        connection_config=connection_config,
        **kwargs
    )
    
    return client


def create_client_from_execution_context(
    server_url: str,
    user_id: str,
    mcp_id: str,
    tool_name: str,
    mcp_configs: Dict[str, Any],
    credential_service: Optional[CredentialService] = None,
    **kwargs
) -> OAuthAwareMCPClient:
    """
    Create OAuth-aware client from execution context.
    
    This is a convenience function for the MCP executor service that
    extracts the relevant MCP config and creates an appropriate client.
    
    Args:
        server_url: MCP server URL
        user_id: User identifier
        mcp_id: MCP configuration identifier
        tool_name: Tool being executed
        mcp_configs: Dictionary of all MCP configurations
        credential_service: Optional credential service instance
        **kwargs: Additional client configuration
        
    Returns:
        Configured OAuthAwareMCPClient instance
        
    Raises:
        ValueError: If MCP config is not found
    """
    logger = logging.getLogger(__name__)
    
    # Find the specific MCP config
    mcp_config = mcp_configs.get(mcp_id)
    if not mcp_config:
        raise ValueError(f"MCP config not found for mcp_id: {mcp_id}")
    
    logger.debug(f"Creating client for mcp_id: {mcp_id}, tool: {tool_name}")
    
    return create_oauth_aware_client(
        server_url=server_url,
        user_id=user_id,
        mcp_config=mcp_config,
        credential_service=credential_service,
        **kwargs
    )


def create_legacy_compatible_client(
    server_url: str,
    headers: Optional[Dict[str, str]] = None,
    oauth_credentials: Optional[Dict[str, Any]] = None,
    user_id: Optional[str] = None,
    mcp_config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> OAuthAwareMCPClient:
    """
    Create client with legacy compatibility.
    
    This function provides backward compatibility with the existing
    MCP executor interface while adding OAuth discovery capabilities.
    
    Args:
        server_url: MCP server URL
        headers: Legacy headers (for backward compatibility)
        oauth_credentials: Pre-fetched OAuth credentials (legacy)
        user_id: User identifier
        mcp_config: MCP configuration
        **kwargs: Additional client configuration
        
    Returns:
        Configured client instance
    """
    logger = logging.getLogger(__name__)
    
    # If oauth_credentials are provided (legacy flow), use them
    if oauth_credentials and oauth_credentials.get('success'):
        logger.info("Using legacy OAuth credentials flow")

        auth_manager = AuthenticationManager()
        access_token = oauth_credentials.get('access_token')
        if access_token:
            auth_manager.add_bearer_token(access_token)

        # Create connection config from kwargs
        connection_config = ConnectionConfig(
            max_retries=kwargs.pop('max_retries', 3),
            enable_health_check=kwargs.pop('enable_health_check', True),
        )

        # Remove parameters that MCPClient doesn't accept directly
        kwargs.pop('timeout', None)

        return OAuthAwareMCPClient(
            server_url=server_url,
            auth_manager=auth_manager,
            connection_config=connection_config,
            user_id=user_id,
            mcp_config=mcp_config,
            **kwargs
        )
    
    # If headers are provided (legacy flow), use them
    elif headers:
        logger.info("Using legacy headers flow")

        auth_manager = AuthenticationManager()
        auth_manager.add_custom_auth(headers=headers)

        # Create connection config from kwargs
        connection_config = ConnectionConfig(
            max_retries=kwargs.pop('max_retries', 3),
            enable_health_check=kwargs.pop('enable_health_check', True),
        )

        # Remove parameters that MCPClient doesn't accept directly
        kwargs.pop('timeout', None)

        return OAuthAwareMCPClient(
            server_url=server_url,
            auth_manager=auth_manager,
            connection_config=connection_config,
            user_id=user_id,
            mcp_config=mcp_config,
            **kwargs
        )
    
    # Otherwise, use OAuth discovery flow
    else:
        logger.info("Using OAuth discovery flow")
        return create_oauth_aware_client(
            server_url=server_url,
            user_id=user_id or "unknown",
            mcp_config=mcp_config or {},
            **kwargs
        )


# Utility functions

def extract_oauth_details_from_config(mcp_config: Dict[str, Any]) -> Optional[Dict[str, str]]:
    """Extract oauth_details from MCP configuration."""
    oauth_details = mcp_config.get('oauth_details')
    if oauth_details and isinstance(oauth_details, dict):
        return {
            'provider': oauth_details.get('provider'),
            'tool_name': oauth_details.get('tool_name')
        }
    return None


def should_use_oauth_discovery(
    mcp_config: Dict[str, Any],
    user_id: Optional[str] = None
) -> bool:
    """Check if OAuth discovery should be used for the given configuration."""
    logger = logging.getLogger(__name__)

    oauth_details = extract_oauth_details_from_config(mcp_config)
    logger.debug(f"OAuth details extracted: {oauth_details}")
    logger.debug(f"User ID provided: {user_id}")

    should_use = (
        oauth_details is not None and
        oauth_details.get('provider') and
        oauth_details.get('tool_name') and
        user_id is not None
    )

    logger.debug(f"Should use OAuth discovery: {should_use}")
    return should_use
