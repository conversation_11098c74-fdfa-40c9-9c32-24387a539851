"""
Schemas for OAuth Discovery Authentication Flow.

This module defines the data structures used in the OAuth discovery process,
including authentication requirements and credential responses.
"""

import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from app.schemas.client import AuthenticationError


@dataclass
class AuthenticationRequirement:
    """
    Represents a single authentication requirement from an MCP server.

    This corresponds to the schema defined for authentication errors:
    {
        "provider": "google",
        "auth_type": "bearer",
        "header_name": "Authorization",
        "header_format": "Bearer {access_token}",
        "token_source": "access_token",
        "required_scopes": ["scope1", "scope2"]  # optional
    }
    """

    provider: str  # "google", "microsoft", "slack", etc.
    auth_type: str  # "bearer", "api_key", "basic", "custom"
    header_name: str  # "Authorization", "X-API-Key", etc.
    header_format: str  # "Bearer {access_token}", "{api_key}", etc.
    token_source: str  # "access_token", "refresh_token", "bot_token", etc.
    required_scopes: List[str] = None  # Optional OAuth scopes

    def __post_init__(self):
        if self.required_scopes is None:
            self.required_scopes = []

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AuthenticationRequirement":
        """Create AuthenticationRequirement from dictionary."""
        return cls(
            provider=data.get("provider", ""),
            auth_type=data.get("auth_type", ""),
            header_name=data.get("header_name", ""),
            header_format=data.get("header_format", ""),
            token_source=data.get("token_source", ""),
            required_scopes=data.get("required_scopes", []),
        )

    def validate(self) -> bool:
        """Validate that all required fields are present."""
        required_fields = [
            self.provider,
            self.auth_type,
            self.header_name,
            self.header_format,
            self.token_source,
        ]
        return all(field for field in required_fields)


@dataclass
class AuthenticationErrorDetails:
    """
    Represents the complete authentication error response structure.

    Corresponds to the full error schema:
    {
        "error": {
            "code": "AUTHENTICATION_REQUIRED",
            "message": "Authentication required to access this resource",
            "details": {
                "authentication_requirements": [...]
            }
        }
    }
    """

    code: str
    message: str
    authentication_requirements: List[AuthenticationRequirement]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AuthenticationErrorDetails":
        """Create AuthenticationErrorDetails from dictionary."""
        error_data = data.get("error", {})
        details = error_data.get("details", {})

        requirements = []
        for req_data in details.get("authentication_requirements", []):
            requirement = AuthenticationRequirement.from_dict(req_data)
            if requirement.validate():
                requirements.append(requirement)

        return cls(
            code=error_data.get("code", ""),
            message=error_data.get("message", ""),
            authentication_requirements=requirements,
        )


@dataclass
class OAuthCredentials:
    """
    Represents OAuth credentials fetched from the credential service.

    Maps to the credential service response structure:
    {
        "success": true,
        "user_id": "12345",
        "tool_name": "gmail",
        "provider": "google",
        "access_token": "ya29.a0AS3H6...",
        "refresh_token": "1//0g7jN_oaw...",
        "token_type": "Bearer",
        "expires_in": 3599,
        "scope": "https://www.googleapis.com/auth/gmail.modify ...",
        "bot_token": "",
        "bot_user_id": "",
        "user_token": "",
        "user_id_slack": "",
        "team_id": "",
        "team_name": ""
    }
    """

    success: bool
    user_id: str
    tool_name: str
    provider: str
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_type: Optional[str] = None
    expires_in: Optional[int] = None
    scope: Optional[str] = None

    # Slack-specific tokens
    bot_token: Optional[str] = None
    bot_user_id: Optional[str] = None
    user_token: Optional[str] = None
    user_id_slack: Optional[str] = None
    team_id: Optional[str] = None
    team_name: Optional[str] = None

    # Generic fields for other providers
    api_key: Optional[str] = None
    client_id: Optional[str] = None
    client_secret: Optional[str] = None

    @classmethod
    def from_api_response(cls, response: Dict[str, Any]) -> "OAuthCredentials":
        """Create OAuthCredentials from API response."""
        return cls(
            success=response.get("success", False),
            user_id=response.get("user_id", ""),
            tool_name=response.get("tool_name", ""),
            provider=response.get("provider", ""),
            access_token=response.get("access_token"),
            refresh_token=response.get("refresh_token"),
            token_type=response.get("token_type"),
            expires_in=response.get("expires_in"),
            scope=response.get("scope"),
            bot_token=response.get("bot_token"),
            bot_user_id=response.get("bot_user_id"),
            user_token=response.get("user_token"),
            user_id_slack=response.get("user_id_slack"),
            team_id=response.get("team_id"),
            team_name=response.get("team_name"),
            api_key=response.get("api_key"),
            client_id=response.get("client_id"),
            client_secret=response.get("client_secret"),
        )

    def get_token_by_source(self, token_source: str) -> Optional[str]:
        """Get token value by token source name dynamically."""
        # Use getattr to dynamically access any field on the credentials object
        # This allows us to support any token_source that the MCP server requests
        return getattr(self, token_source, None)

    def has_required_tokens(self, token_sources: List[str]) -> bool:
        """Check if credentials contain all required token sources."""
        for token_source in token_sources:
            if not self.get_token_by_source(token_source):
                return False
        return True


@dataclass
class HeaderConstructionResult:
    """Result of constructing authentication headers."""

    headers: Dict[str, str]
    successful_requirements: List[AuthenticationRequirement]
    failed_requirements: List[
        tuple[AuthenticationRequirement, str]
    ]  # (requirement, error_message)

    @property
    def success(self) -> bool:
        """True if at least one authentication requirement was satisfied."""
        return len(self.successful_requirements) > 0

    @property
    def partial_success(self) -> bool:
        """True if some but not all requirements were satisfied."""
        return (
            len(self.successful_requirements) > 0 and len(self.failed_requirements) > 0
        )


class OAuthDiscoveryError(AuthenticationError):
    """Specific error for OAuth discovery process failures."""

    def __init__(
        self,
        message: str,
        requirements: Optional[List[AuthenticationRequirement]] = None,
        credentials: Optional[OAuthCredentials] = None,
        original_error: Optional[Exception] = None,
    ):
        super().__init__(message)
        self.requirements = requirements or []
        self.credentials = credentials
        self.original_error = original_error


class CredentialMappingError(OAuthDiscoveryError):
    """Error when token sources cannot be mapped to available credentials."""

    pass


class HeaderConstructionError(OAuthDiscoveryError):
    """Error when authentication headers cannot be constructed."""

    pass


class AuthenticationRequirementError(OAuthDiscoveryError):
    """Error when authentication requirements are invalid or incomplete."""

    pass


# Utility functions for working with authentication requirements


def validate_authentication_requirements(
    requirements: List[AuthenticationRequirement],
) -> tuple[List[AuthenticationRequirement], List[str]]:
    """
    Validate a list of authentication requirements.

    Returns:
        Tuple of (valid_requirements, error_messages)
    """
    valid_requirements = []
    error_messages = []

    for i, req in enumerate(requirements):
        if not req.validate():
            error_messages.append(f"Requirement {i}: Missing required fields")
            continue

        # Additional validation
        if req.auth_type not in ["bearer", "api_key", "basic", "custom"]:
            error_messages.append(
                f"Requirement {i}: Invalid auth_type '{req.auth_type}'"
            )
            continue

        if not req.header_format or "{" not in req.header_format:
            error_messages.append(
                f"Requirement {i}: Invalid header_format '{req.header_format}'"
            )
            continue

        valid_requirements.append(req)

    return valid_requirements, error_messages


def group_requirements_by_provider(
    requirements: List[AuthenticationRequirement],
) -> Dict[str, List[AuthenticationRequirement]]:
    """Group authentication requirements by provider."""
    grouped = {}
    for req in requirements:
        if req.provider not in grouped:
            grouped[req.provider] = []
        grouped[req.provider].append(req)
    return grouped


@dataclass
class CachedHeaderRequirements:
    """
    Cached authentication requirements for a specific server and tool.

    This stores the header structure/requirements globally to avoid repeated
    discovery calls, while keeping credential values user-specific.
    """

    server_url: str
    tool_name: str
    requirements: List[AuthenticationRequirement]
    cached_at: float = field(default_factory=time.time)
    ttl_seconds: int = 3600  # 1 hour default TTL

    @property
    def is_expired(self) -> bool:
        """Check if the cached requirements have expired."""
        return time.time() - self.cached_at > self.ttl_seconds

    @classmethod
    def create_cache_key(cls, server_url: str, tool_name: str) -> str:
        """Create a cache key for header requirements."""
        # Normalize server URL to avoid cache misses due to trailing slashes, etc.
        normalized_url = server_url.rstrip("/")
        return f"{normalized_url}_{tool_name}"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "server_url": self.server_url,
            "tool_name": self.tool_name,
            "requirements": [
                {
                    "provider": req.provider,
                    "auth_type": req.auth_type,
                    "header_name": req.header_name,
                    "header_format": req.header_format,
                    "token_source": req.token_source,
                    "required_scopes": req.required_scopes,
                }
                for req in self.requirements
            ],
            "cached_at": self.cached_at,
            "ttl_seconds": self.ttl_seconds,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CachedHeaderRequirements":
        """Create from dictionary."""
        requirements = []
        for req_data in data.get("requirements", []):
            req = AuthenticationRequirement(
                provider=req_data.get("provider", ""),
                auth_type=req_data.get("auth_type", ""),
                header_name=req_data.get("header_name", ""),
                header_format=req_data.get("header_format", ""),
                token_source=req_data.get("token_source", ""),
                required_scopes=req_data.get("required_scopes", []),
            )
            requirements.append(req)

        return cls(
            server_url=data.get("server_url", ""),
            tool_name=data.get("tool_name", ""),
            requirements=requirements,
            cached_at=data.get("cached_at", time.time()),
            ttl_seconds=data.get("ttl_seconds", 3600),
        )
