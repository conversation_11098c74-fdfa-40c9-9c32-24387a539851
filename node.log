DataComponent -> SelectDataExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent']
2025-07-24 13:30:56 - SelectDataComponent - INFO - [<module>:529] SelectDataComponent successfully registered in COMPONENT_REGISTRY
2025-07-24 13:30:56 - SelectDataComponent - INFO - [<module>] SelectDataComponent successfully registered in COMPONENT_REGISTRY
2025-07-24 13:30:56 - SplitTextComponent - INFO - [setup_logger:467] Logger SplitTextComponent configured with log file: logs/2025-07-24/SplitTextComponent_13-30-56.log
2025-07-24 13:30:56 - SplitTextComponent - INFO - [setup_logger] Logger SplitTextComponent configured with log file: logs/2025-07-24/SplitTextComponent_13-30-56.log
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: SplitTextComponent -> SplitTextComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-07-24 13:30:56 - RegexExtractorComponent - INFO - [setup_logger:467] Logger RegexExtractorComponent configured with log file: logs/2025-07-24/RegexExtractorComponent_13-30-56.log
2025-07-24 13:30:56 - RegexExtractorComponent - INFO - [setup_logger] Logger RegexExtractorComponent configured with log file: logs/2025-07-24/RegexExtractorComponent_13-30-56.log
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: RegexExtractorComponent -> RegexExtractorComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: RegexExtractorComponent -> RegexExtractorComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: text_analysis -> TextAnalysisComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-07-24 13:30:56 - DataToDataFrameComponent - INFO - [setup_logger:467] Logger DataToDataFrameComponent configured with log file: logs/2025-07-24/DataToDataFrameComponent_13-30-56.log
2025-07-24 13:30:56 - DataToDataFrameComponent - INFO - [setup_logger] Logger DataToDataFrameComponent configured with log file: logs/2025-07-24/DataToDataFrameComponent_13-30-56.log
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: conditional -> ConditionalComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: conditional -> ConditionalComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: StartOutboundCallComponent -> StartOutboundCallComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: StartOutboundCallComponent -> StartOutboundCallComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: DelayComponent -> DelayComponentExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: DelayComponent -> DelayComponentExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: basic_llm_chain -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: basic_llm_chain -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: question_answer_module -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: question_answer_module -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: information_extractor -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: information_extractor -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: classifier -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: classifier -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: summarizer -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: summarizer -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: sentiment_analyzer -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: sentiment_analyzer -> UnifiedAIComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer']
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.message_to_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.message_to_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.gmail_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.gmail_component
2025-07-24 13:30:56 - GmailComponent - INFO - [setup_logger:467] Logger GmailComponent configured with log file: logs/2025-07-24/GmailComponent_13-30-56.log
2025-07-24 13:30:56 - GmailComponent - INFO - [setup_logger] Logger GmailComponent configured with log file: logs/2025-07-24/GmailComponent_13-30-56.log
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: GmailComponent -> GmailComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.gmail_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.gmail_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.data_compose_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.data_compose_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: DataComposeComponent -> DataComposeExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: DataComposeComponent -> DataComposeExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.data_compose_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.data_compose_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.combine_text_component_new
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.combine_text_component_new
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.combine_text_component_new
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.combine_text_component_new
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.id_generator_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.id_generator_component
2025-07-24 13:30:56 - IDGeneratorComponent - INFO - [setup_logger:467] Logger IDGeneratorComponent configured with log file: logs/2025-07-24/IDGeneratorComponent_13-30-56.log
2025-07-24 13:30:56 - IDGeneratorComponent - INFO - [setup_logger] Logger IDGeneratorComponent configured with log file: logs/2025-07-24/IDGeneratorComponent_13-30-56.log
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.id_generator_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.id_generator_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.delay_time
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.delay_time
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.delay_time
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.delay_time
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.regex_extractor_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.regex_extractor_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.regex_extractor_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.regex_extractor_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.split_text_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.split_text_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.split_text_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.split_text_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.outbound_caller
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.outbound_caller
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.outbound_caller
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.outbound_caller
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.universal_converter_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.universal_converter_component
2025-07-24 13:30:56 - UniversalConverterComponent - INFO - [setup_logger:467] Logger UniversalConverterComponent configured with log file: logs/2025-07-24/UniversalConverterComponent_13-30-56.log
2025-07-24 13:30:56 - UniversalConverterComponent - INFO - [setup_logger] Logger UniversalConverterComponent configured with log file: logs/2025-07-24/UniversalConverterComponent_13-30-56.log
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: UniversalConverterComponent -> UniversalConverterExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: UniversalConverterComponent -> UniversalConverterExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent']
2025-07-24 13:30:56 - UniversalConverterComponent - INFO - [<module>:616] UniversalConverterComponent successfully registered in COMPONENT_REGISTRY
2025-07-24 13:30:56 - UniversalConverterComponent - INFO - [<module>] UniversalConverterComponent successfully registered in COMPONENT_REGISTRY
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.universal_converter_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.universal_converter_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.alter_metadata_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.alter_metadata_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.alter_metadata_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.alter_metadata_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.merge_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.merge_data_component
2025-07-24 13:30:56 - MergeDataComponent - INFO - [setup_logger:467] Logger MergeDataComponent configured with log file: logs/2025-07-24/MergeDataComponent_13-30-56.log
2025-07-24 13:30:56 - MergeDataComponent - INFO - [setup_logger] Logger MergeDataComponent configured with log file: logs/2025-07-24/MergeDataComponent_13-30-56.log
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: MergeDataComponent -> MergeDataExecutor
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.merge_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.merge_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.gmail_tracker_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.gmail_tracker_component
2025-07-24 13:30:56 - GmailTrackerComponent - INFO - [setup_logger:467] Logger GmailTrackerComponent configured with log file: logs/2025-07-24/GmailTrackerComponent_13-30-56.log
2025-07-24 13:30:56 - GmailTrackerComponent - INFO - [setup_logger] Logger GmailTrackerComponent configured with log file: logs/2025-07-24/GmailTrackerComponent_13-30-56.log
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent', 'GmailTrackerComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [decorator] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent', 'GmailTrackerComponent']
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.gmail_tracker_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.gmail_tracker_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.doc_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.doc_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.doc_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.doc_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.conditional_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.conditional_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.conditional_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.conditional_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.ai_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.ai_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.ai_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.ai_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.convert_script_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.convert_script_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.convert_script_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.convert_script_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.text_analysis_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.text_analysis_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.text_analysis_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.text_analysis_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.select_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.select_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.select_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.select_data_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.data_to_dataframe_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.data_to_dataframe_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.data_to_dataframe_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.data_to_dataframe_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1413] Importing component module: app.components.api_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Importing component module: app.components.api_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1417] Successfully imported component module: app.components.api_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Successfully imported component module: app.components.api_component
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules:1426] Imported 21 component modules: ['app.components.message_to_data_component', 'app.components.gmail_component', 'app.components.data_compose_component', 'app.components.combine_text_component_new', 'app.components.id_generator_component', 'app.components.delay_time', 'app.components.regex_extractor_component', 'app.components.split_text_component', 'app.components.outbound_caller', 'app.components.universal_converter_component', 'app.components.alter_metadata_component', 'app.components.merge_data_component', 'app.components.gmail_tracker_component', 'app.components.doc_component', 'app.components.conditional_component', 'app.components.ai_component', 'app.components.convert_script_data_component', 'app.components.text_analysis_component', 'app.components.select_data_component', 'app.components.data_to_dataframe_component', 'app.components.api_component']
2025-07-24 13:30:56 - ComponentSystem - INFO - [discover_component_modules] Imported 21 component modules: ['app.components.message_to_data_component', 'app.components.gmail_component', 'app.components.data_compose_component', 'app.components.combine_text_component_new', 'app.components.id_generator_component', 'app.components.delay_time', 'app.components.regex_extractor_component', 'app.components.split_text_component', 'app.components.outbound_caller', 'app.components.universal_converter_component', 'app.components.alter_metadata_component', 'app.components.merge_data_component', 'app.components.gmail_tracker_component', 'app.components.doc_component', 'app.components.conditional_component', 'app.components.ai_component', 'app.components.convert_script_data_component', 'app.components.text_analysis_component', 'app.components.select_data_component', 'app.components.data_to_dataframe_component', 'app.components.api_component']
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:119] Dynamically imported 21 component modules
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] Dynamically imported 21 component modules
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:122] Component registry after dynamic imports: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent', 'GmailTrackerComponent']
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] Component registry after dynamic imports: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent', 'GmailTrackerComponent']
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:128] ✓ SelectDataComponent is registered
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] ✓ SelectDataComponent is registered
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:128] ✓ ApiRequestNode is registered
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] ✓ ApiRequestNode is registered
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:128] ✓ CombineTextComponent is registered
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] ✓ CombineTextComponent is registered
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:132] Components imported successfully
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] Components imported successfully
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:139] Registered components: []
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] Registered components: []
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:143] Initializing ToolExecutor
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] Initializing ToolExecutor
2025-07-24 13:30:56 - ToolExecutor - INFO - [setup_logger:467] Logger ToolExecutor configured with log file: logs/2025-07-24/ToolExecutor_13-30-56.log
2025-07-24 13:30:56 - ToolExecutor - INFO - [setup_logger] Logger ToolExecutor configured with log file: logs/2025-07-24/ToolExecutor_13-30-56.log
2025-07-24 13:30:56 - ToolExecutor - INFO - [setup_tool_executor_logger:97] Kafka logging enabled for ToolExecutor, sending to dedicated topic: tool_executor_logs
2025-07-24 13:30:56 - ToolExecutor - INFO - [setup_tool_executor_logger] Kafka logging enabled for ToolExecutor, sending to dedicated topic: tool_executor_logs
2025-07-24 13:30:56 - ToolExecutor - INFO - [get_tool_executor:244] Creating new global ToolExecutor instance
2025-07-24 13:30:56 - ToolExecutor - INFO - [get_tool_executor] Creating new global ToolExecutor instance
2025-07-24 13:30:56 - ToolExecutor - INFO - [__init__:76] Initializing ToolExecutor
2025-07-24 13:30:56 - ToolExecutor - INFO - [__init__] Initializing ToolExecutor
2025-07-24 13:30:56 - ToolExecutor - INFO - [__init__:78] ToolExecutor initialized successfully
2025-07-24 13:30:56 - ToolExecutor - INFO - [__init__] ToolExecutor initialized successfully
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:146] ToolExecutor initialized successfully
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] ToolExecutor initialized successfully
2025-07-24 13:30:56 - NodeExecutor - INFO - [main:149] Starting the ApiRequestNode component as the main entry point for tool requests
2025-07-24 13:30:56 - NodeExecutor - INFO - [main] Starting the ApiRequestNode component as the main entry point for tool requests
2025-07-24 13:30:56 - ApiRequestNode - INFO - [__init__:78] Initializing API Component
2025-07-24 13:30:56 - ApiRequestNode - INFO - [__init__] Initializing API Component
2025-07-24 13:30:56 - ApiRequestNode - INFO - [__init__:81] API Component initialized successfully
2025-07-24 13:30:56 - ApiRequestNode - INFO - [__init__] API Component initialized successfully
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: 34.172.106.233:9092
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Bootstrap Servers: 34.172.106.233:9092
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Group ID: node_executor_service
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Topic: node-execution-request
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Auto Offset Reset: latest (starting from the latest offset)
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Auto Commit: Disabled (using manual offset commits)
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Fetch Min Bytes: 100
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Fetch Max Wait: 500ms
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Session Timeout: 10000ms
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component]   Heartbeat Interval: 3000ms
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-07-24 13:30:56 - ComponentSystem - INFO - [start_component] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-07-24 13:30:56 - aiokafka.consumer.subscription_state - INFO - [_change_subscription] Updating subscribed topics to: frozenset({'node-execution-request'})
2025-07-24 13:30:59 - aiokafka.consumer.group_coordinator - INFO - [ensure_coordinator_known] Discovered coordinator 1 for group node_executor_service
2025-07-24 13:30:59 - aiokafka.consumer.group_coordinator - INFO - [_on_join_prepare] Revoking previously assigned partitions set() for group node_executor_service
2025-07-24 13:30:59 - aiokafka.consumer.group_coordinator - INFO - [perform_group_join] (Re-)joining group node_executor_service
2025-07-24 13:31:01 - aiokafka.consumer.group_coordinator - INFO - [perform_group_join] Joined group 'node_executor_service' (generation 424) with member_id aiokafka-0.12.0-31d9b24f-697f-4903-a3b2-11c5a21e0300
2025-07-24 13:31:01 - aiokafka.consumer.group_coordinator - INFO - [_send_sync_group_request] Successfully synced group node_executor_service with generation 424
2025-07-24 13:31:01 - aiokafka.consumer.group_coordinator - INFO - [_on_join_complete] Setting newly assigned partitions {TopicPartition(topic='node-execution-request', partition=0)} for group node_executor_service
2025-07-24 13:31:01 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-07-24 13:31:01 - ComponentSystem - INFO - [start_component] Kafka consumer started successfully for component: ApiRequestNode
2025-07-24 13:31:01 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-07-24 13:31:01 - ComponentSystem - INFO - [start_component] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-07-24 13:31:01 - NodeExecutor - INFO - [main:151] ApiRequestNode component started successfully
2025-07-24 13:31:01 - NodeExecutor - INFO - [main] ApiRequestNode component started successfully
2025-07-24 13:31:01 - NodeExecutor - INFO - [main:154] Node Executor is now running in tool-based mode
2025-07-24 13:31:01 - NodeExecutor - INFO - [main] Node Executor is now running in tool-based mode
2025-07-24 13:31:01 - NodeExecutor - INFO - [main:155] All components can be accessed using the tool_name parameter in requests
2025-07-24 13:31:01 - NodeExecutor - INFO - [main] All components can be accessed using the tool_name parameter in requests
2025-07-24 13:31:01 - NodeExecutor - INFO - [main:156] Available tools: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent', 'GmailTrackerComponent']
2025-07-24 13:31:01 - NodeExecutor - INFO - [main] Available tools: ['ApiRequestNode', 'CombineTextComponent', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'RegexExtractorComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'conditional', 'StartOutboundCallComponent', 'DelayComponent', 'basic_llm_chain', 'question_answer_module', 'information_extractor', 'classifier', 'summarizer', 'sentiment_analyzer', 'GmailComponent', 'DataComposeComponent', 'IDGeneratorComponent', 'UniversalConverterComponent', 'MergeDataComponent', 'GmailTrackerComponent']
2025-07-24 13:31:01 - NodeExecutor - INFO - [main:158] Node Executor is now running. Press Ctrl+C to stop.
2025-07-24 13:31:01 - NodeExecutor - INFO - [main] Node Executor is now running. Press Ctrl+C to stop.
2025-07-24 13:31:01 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-07-24 13:31:01 - ComponentSystem - INFO - [_consume_messages] Consumer loop started for component: ApiRequestNode
2025-07-24 13:34:02 - aiokafka.consumer.group_coordinator - WARNING - [_do_heartbeat] Heartbeat failed for group node_executor_service because it is rebalancing
2025-07-24 13:34:02 - aiokafka.consumer.group_coordinator - INFO - [_on_join_prepare] Revoking previously assigned partitions frozenset({TopicPartition(topic='node-execution-request', partition=0)}) for group node_executor_service
2025-07-24 13:34:02 - aiokafka.consumer.group_coordinator - INFO - [perform_group_join] (Re-)joining group node_executor_service
2025-07-24 13:34:02 - aiokafka.consumer.group_coordinator - INFO - [perform_group_join] Joined group 'node_executor_service' (generation 425) with member_id aiokafka-0.12.0-31d9b24f-697f-4903-a3b2-11c5a21e0300
2025-07-24 13:34:02 - aiokafka.consumer.group_coordinator - INFO - [perform_group_join] Elected group leader -- performing partition assignments using roundrobin
2025-07-24 13:34:02 - aiokafka.consumer.group_coordinator - INFO - [_send_sync_group_request] Successfully synced group node_executor_service with generation 425
2025-07-24 13:34:02 - aiokafka.consumer.group_coordinator - INFO - [_on_join_complete] Setting newly assigned partitions {TopicPartition(topic='node-execution-request', partition=0)} for group node_executor_service
2025-07-24 13:38:04 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1905, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:04 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1905, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:04 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "is_empty",
        "next_transition": "transition-CombineTextComponent-*************",
        "ends_at": [
          "transition-CombineTextComponent-*************"
        ]
      }
    ],
    "input_data": "hello",
    "source": "node_output",
    "default_transition": "transition--ia1r43wJL8z8Wz_ODjZo",
    "default_ends_at": [
      "transition--ia1r43wJL8z8Wz_ODjZo"
    ],
    "evaluation_strategy": "all_matches",
    "evaluation_data": "hello"
  },
  "request_id": "c5a4072f-a054-40de-b637-4003f2206d78",
  "correlation_id": "c438d529-d40a-4179-a77b-cd4a5891e145-****************",
  "transition_id": "transition-ConditionalNode-1753344121771",
  "node_label": "Switch-Case Router"
}
2025-07-24 13:38:04 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "is_empty",
        "next_transition": "transition-CombineTextComponent-*************",
        "ends_at": [
          "transition-CombineTextComponent-*************"
        ]
      }
    ],
    "input_data": "hello",
    "source": "node_output",
    "default_transition": "transition--ia1r43wJL8z8Wz_ODjZo",
    "default_ends_at": [
      "transition--ia1r43wJL8z8Wz_ODjZo"
    ],
    "evaluation_strategy": "all_matches",
    "evaluation_data": "hello"
  },
  "request_id": "c5a4072f-a054-40de-b637-4003f2206d78",
  "correlation_id": "c438d529-d40a-4179-a77b-cd4a5891e145-****************",
  "transition_id": "transition-ConditionalNode-1753344121771",
  "node_label": "Switch-Case Router"
}
2025-07-24 13:38:04 - ComponentSystem - INFO - [_process_message:713] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Executing tool conditional for RequestID=c5a4072f-a054-40de-b637-4003f2206d78, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:04 - ComponentSystem - INFO - [_process_message] Executing tool conditional for RequestID=c5a4072f-a054-40de-b637-4003f2206d78, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool:94] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Executing tool for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool:97] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "is_empty",
        "next_transition": "transition-CombineTextComponent-*************",
        "ends_at": [
          "transition-CombineTextComponent-*************"
        ]
      }
    ],
    "input_data": "hello",
    "source": "node_output",
    "default_transition": "transition--ia1r43wJL8z8Wz_ODjZo",
    "default_ends_at": [
      "transition--ia1r43wJL8z8Wz_ODjZo"
    ],
    "evaluation_strategy": "all_matches",
    "evaluation_data": "hello"
  },
  "request_id": "c5a4072f-a054-40de-b637-4003f2206d78",
  "correlation_id": "c438d529-d40a-4179-a77b-cd4a5891e145-****************",
  "transition_id": "transition-ConditionalNode-1753344121771",
  "node_label": "Switch-Case Router"
}
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "operator": "is_empty",
        "next_transition": "transition-CombineTextComponent-*************",
        "ends_at": [
          "transition-CombineTextComponent-*************"
        ]
      }
    ],
    "input_data": "hello",
    "source": "node_output",
    "default_transition": "transition--ia1r43wJL8z8Wz_ODjZo",
    "default_ends_at": [
      "transition--ia1r43wJL8z8Wz_ODjZo"
    ],
    "evaluation_strategy": "all_matches",
    "evaluation_data": "hello"
  },
  "request_id": "c5a4072f-a054-40de-b637-4003f2206d78",
  "correlation_id": "c438d529-d40a-4179-a77b-cd4a5891e145-****************",
  "transition_id": "transition-ConditionalNode-1753344121771",
  "node_label": "Switch-Case Router"
}
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool:111] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Tool name: conditional for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool] Tool name: conditional for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - app.components.conditional_component - INFO - [__init__] ConditionalComponent initialized successfully
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool:144] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Processing payload with component conditional for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool] Processing payload with component conditional for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - app.components.conditional_component - INFO - [process] Processing conditional routing for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - app.components.conditional_component - WARNING - [validate_input] Input validation failed: 1 validation error for ConditionalRequestSchema
conditions.0.expected_value
  Field required [type=missing, input_value={'operator': 'is_empty', ...mponent-*************']}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-07-24 13:38:04 - app.components.conditional_component - WARNING - [process] Input validation failed: 1 validation error for ConditionalRequestSchema
conditions.0.expected_value
  Field required [type=missing, input_value={'operator': 'is_empty', ...mponent-*************']}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool:148] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Component conditional processed payload successfully for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool] Component conditional processed payload successfully for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool:154] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] ToolExecutor returning raw component result for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ComponentSystem - INFO - [_process_message:717] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Tool conditional executed successfully for RequestID=c5a4072f-a054-40de-b637-4003f2206d78, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:04 - ComponentSystem - INFO - [_process_message] Tool conditional executed successfully for RequestID=c5a4072f-a054-40de-b637-4003f2206d78, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:04 - ComponentSystem - INFO - [_send_result:1007] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Preparing to send result for component ApiRequestNode, RequestID=c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer:244] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Creating Kafka producer for component ApiRequestNode with configuration:
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer] Creating Kafka producer for component ApiRequestNode with configuration:
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer:247] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************]   Bootstrap Servers: 34.172.106.233:9092
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer]   Bootstrap Servers: 34.172.106.233:9092
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer:248] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************]   Acks: all (ensuring message is written to all in-sync replicas)
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer]   Acks: all (ensuring message is written to all in-sync replicas)
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer:252] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************]   Request Timeout: 60000ms
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer]   Request Timeout: 60000ms
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer:255] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer:259] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Creating new Kafka producer for component: ApiRequestNode with servers: 34.172.106.233:9092
2025-07-24 13:38:04 - ComponentSystem - INFO - [get_producer] Creating new Kafka producer for component: ApiRequestNode with servers: 34.172.106.233:9092
2025-07-24 13:38:06 - ComponentSystem - INFO - [get_producer:266] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Kafka producer started successfully for component: ApiRequestNode
2025-07-24 13:38:06 - ComponentSystem - INFO - [get_producer] Kafka producer started successfully for component: ApiRequestNode
2025-07-24 13:38:06 - ComponentSystem - INFO - [_send_result:1039] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Component returned error status for RequestID=c5a4072f-a054-40de-b637-4003f2206d78: 1 validation error for ConditionalRequestSchema
conditions.0.expected_value
  Field required [type=missing, input_value={'operator': 'is_empty', ...mponent-*************']}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-07-24 13:38:06 - ComponentSystem - INFO - [_send_result] Component returned error status for RequestID=c5a4072f-a054-40de-b637-4003f2206d78: 1 validation error for ConditionalRequestSchema
conditions.0.expected_value
  Field required [type=missing, input_value={'operator': 'is_empty', ...mponent-*************']}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-07-24 13:38:06 - ComponentSystem - INFO - [_send_result:1105] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Sending Kafka response: RequestID=c5a4072f-a054-40de-b637-4003f2206d78, Response={
  "request_id": "c5a4072f-a054-40de-b637-4003f2206d78",
  "component_type": "ApiRequestNode",
  "status": "error",
  "message": "Request failed",
  "timestamp": 1753344486.800286,
  "transition_id": "transition-ConditionalNode-1753344121771",
  "result": null,
  "error": "1 validation error for ConditionalRequestSchema\nconditions.0.expected_value\n  Field required [type=missing, input_value={'operator': 'is_empty', ...mponent-*************']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing"
}
2025-07-24 13:38:06 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=c5a4072f-a054-40de-b637-4003f2206d78, Response={
  "request_id": "c5a4072f-a054-40de-b637-4003f2206d78",
  "component_type": "ApiRequestNode",
  "status": "error",
  "message": "Request failed",
  "timestamp": 1753344486.800286,
  "transition_id": "transition-ConditionalNode-1753344121771",
  "result": null,
  "error": "1 validation error for ConditionalRequestSchema\nconditions.0.expected_value\n  Field required [type=missing, input_value={'operator': 'is_empty', ...mponent-*************']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing"
}
2025-07-24 13:38:07 - ComponentSystem - INFO - [_send_result:1121] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Sent result for component ApiRequestNode to topic node_results for RequestID=c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:07 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=c5a4072f-a054-40de-b637-4003f2206d78
2025-07-24 13:38:07 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Successfully committed offset 1906 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:07 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1906 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:07 - ComponentSystem - INFO - [_process_message:936] [ReqID:c5a4072f-a054-40de-b637-4003f2206d78] [CorrID:c438d529-d40a-4179-a77b-cd4a5891e145-****************] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1905, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
2025-07-24 13:38:07 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1905, TaskID=ApiRequestNode-node-execution-request-0-1905-1753344484.831161
