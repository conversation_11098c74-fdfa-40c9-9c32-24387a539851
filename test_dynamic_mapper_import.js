// Test if the dynamic mapper import is working
const path = require('path');

// Test the require approach used in api.ts
console.log('🧪 Testing require approach from api.ts...');
try {
  const { mapJsonSchemaTypeToInputType: dynamicMapper } = require('./workflow-builder-app/src/lib/dynamicInputTypeMapper');
  console.log('✅ Dynamic mapper imported successfully');
  
  // Test it with array type
  const result = dynamicMapper('array', { type: 'array', items: { type: 'string' } });
  console.log('🎯 Array type mapping result:', result);
  
} catch (error) {
  console.log('❌ Dynamic mapper import failed:', error.message);
  console.log('🔧 This explains why arrays are showing as strings!');
  
  // Test the emergency fallback
  console.log('\n🚨 Testing emergency fallback...');
  const typeMap = {
    string: "string",
    number: "number",
    integer: "number", 
    boolean: "boolean",
    object: "object",
    array: "array",
  };
  
  const fallbackResult = typeMap['array'] || "string";
  console.log('📋 Emergency fallback result for array:', fallbackResult);
  
  // But if the component isn't found, it might still show as string
  console.log('\n💡 Even if emergency fallback returns "array", if the component system fails,');
  console.log('   it might still render as StringInput instead of ArrayInput');
}

// Test different import approaches
console.log('\n🧪 Testing different import approaches...');

// Try ES6 import syntax (this won't work in Node.js directly but shows the pattern)
console.log('import { mapJsonSchemaTypeToInputType } from "./dynamicInputTypeMapper";');
console.log('   ^ This might work better in the TypeScript environment');

// Try importing the class directly
try {
  const { DynamicInputTypeMapper } = require('./workflow-builder-app/src/lib/dynamicInputTypeMapper');
  console.log('✅ DynamicInputTypeMapper class imported successfully');
  
  const mapping = DynamicInputTypeMapper.getMapping({ type: 'array', items: { type: 'string' } });
  console.log('🎯 Array type mapping via class:', mapping);
  
} catch (error) {
  console.log('❌ DynamicInputTypeMapper class import failed:', error.message);
}