prat<PERSON><PERSON><PERSON><PERSON>@Pratham-ka-MacBook-Air backend % cd workflow-service 
pratham<PERSON>r<PERSON>@Pratham-ka-MacBook-Air workflow-service % git add .
pratham<PERSON>rwal@Pratham-ka-MacBook-Air workflow-service % git commit -m "seach working for wo
rkflows"
[combine-text-and-workflow-fixed 8dc0fe7] seach working for workflows
 Committer: <PERSON><PERSON><PERSON> <pratham<PERSON><EMAIL>>
Your name and email address were configured automatically based
on your username and hostname. Please check that they are accurate.
You can suppress this message by setting them explicitly. Run the
following command and follow the instructions in your editor to edit
your configuration file:

    git config --global --edit

After doing this, you may fix the identity used for this commit with:

    git commit --amend --reset-author

 1 file changed, 3 insertions(+), 2 deletions(-)
prathamagarwal@Pratham-ka-MacBook-Air workflow-service % git pull origin dev
remote: Enumerating objects: 1, done.
remote: Counting objects: 100% (1/1), done.
remote: Total 1 (delta 0), reused 0 (delta 0), pack-reused 0
Unpacking objects: 100% (1/1), 320 bytes | 320.00 KiB/s, done.
From gitlab.rapidinnovation.tech:ruh.ai/workflow-service
 * branch            dev        -> FETCH_HEAD
   1764671..30e0454  dev        -> origin/dev
Successfully rebased and updated refs/heads/combine-text-and-workflow-fixed.
prathamagarwal@Pratham-ka-MacBook-Air workflow-service % git push 
Enumerating objects: 9, done.
Counting objects: 100% (9/9), done.
Delta compression using up to 8 threads
Compressing objects: 100% (5/5), done.
Writing objects: 100% (5/5), 529 bytes | 529.00 KiB/s, done.
Total 5 (delta 4), reused 0 (delta 0), pack-reused 0
remote: 
remote: To create a merge request for combine-text-and-workflow-fixed, visit:
remote:   http://gitlab.rapidinnovation.tech/ruh.ai/workflow-service/-/merge_requests/new?merge_request%5Bsource_branch%5D=combine-text-and-workflow-fixed
remote: 
To gitlab.rapidinnovation.tech:ruh.ai/workflow-service.git
   5a0fab2..1be1273  combine-text-and-workflow-fixed -> combine-text-and-workflow-fixed
prathamagarwal@Pratham-ka-MacBook-Air workflow-service % ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Generating gRPC code...
Successfully generated gRPC code
Successfully fixed imports in generated files
Initializing database...
Starting Workflow Service...
DEBUG: StartNode class loaded
Warning: Could not import processing components: No module named 'app.utils.workflow_builder.inputs'
DEBUG: BaseAgentComponent class loaded with is_abstract = True
DEBUG: AI component AgenticAI loaded
DEBUG: BasicLLMChain class loaded
DEBUG: ApiRequestNode class loaded
2025-07-17 14:14:03 - workflow-service - INFO - Loading all components...
2025-07-17 14:14:03 - component-loader - INFO - Loading all components...
2025-07-17 14:14:03 - component-loader - INFO - Base component path: /Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/components
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import some component packages: No module named 'app.utils.workflow_builder.inputs'
Warning: MCP config file not found at /Users/<USER>/Desktop/ruh_ai/backend/workflow-service/mcp_server_configs.json
2025-07-17 14:14:03 - component-loader - INFO - Found class MCPToolsComponent in module app.components.tools.mcp_tools
2025-07-17 14:14:03 - component-loader - INFO - Found class BaseNode in module app.components.core.base_node
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.save_to_file: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.universal_converter: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.data_compose: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - INFO - Found class CombineTextComponent in module app.components.processing.combine_text
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.alter_metadata: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.delay_time: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.split_text: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - INFO - Found class MergeDataComponent in module app.components.processing.merge_data
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.select_data: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.data_to_dataframe: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - ERROR - Failed to import app.components.processing.message_to_data: No module named 'app.utils.workflow_builder.inputs'
2025-07-17 14:14:03 - component-loader - INFO - Found class StartNode in module app.components.io.start_node
2025-07-17 14:14:03 - component-loader - INFO - Found class SlackComponent in module app.components.hitl.slack_component
2025-07-17 14:14:03 - component-loader - INFO - Found class GmailComponent in module app.components.hitl.gmail_component
2025-07-17 14:14:03 - component-loader - INFO - Found class TelegramComponent in module app.components.hitl.telegram_component
2025-07-17 14:14:03 - component-loader - INFO - Found class EmailComponent in module app.components.hitl.email_component
2025-07-17 14:14:03 - component-loader - INFO - Found class BaseHITLComponent in module app.components.hitl.base_hitl_component
2025-07-17 14:14:03 - component-loader - INFO - Found class DiscordComponent in module app.components.hitl.discord_component
2025-07-17 14:14:03 - component-loader - INFO - Found class HITLOrchestrator in module app.components.hitl.hitl_orchestrator
2025-07-17 14:14:03 - component-loader - INFO - Found class WhatsAppComponent in module app.components.hitl.whatsapp_component
2025-07-17 14:14:03 - component-loader - INFO - Found class BaseDataInteractionComponent in module app.components.data_interaction.base_data_interaction_component
2025-07-17 14:14:03 - component-loader - INFO - Found class WebhookComponent in module app.components.data_interaction.webhook
2025-07-17 14:14:03 - component-loader - INFO - Found class ApiRequestNode in module app.components.data_interaction.api_request
2025-07-17 14:14:03 - component-loader - INFO - Found class BasicLLMChain in module app.components.ai.basic_llm_chain
2025-07-17 14:14:03 - component-loader - INFO - Found class Classifier in module app.components.ai.classifier
2025-07-17 14:14:03 - component-loader - INFO - Found class AgenticAI in module app.components.ai.agentic_ai
2025-07-17 14:14:03 - component-loader - INFO - Found class BaseAgentComponent in module app.components.ai.base_agent_component
2025-07-17 14:14:03 - component-loader - INFO - Found class QuestionAnswerModule in module app.components.ai.question_answer_module
2025-07-17 14:14:03 - component-loader - INFO - Found class StartOutboundCallComponent in module app.components.ai.outbound_caller
2025-07-17 14:14:03 - component-loader - INFO - Found class Summarizer in module app.components.ai.summarizer
2025-07-17 14:14:03 - component-loader - INFO - Found class InformationExtractor in module app.components.ai.information_extractor
2025-07-17 14:14:03 - component-loader - INFO - Found class SentimentAnalyzer in module app.components.ai.sentiment_analyzer
2025-07-17 14:14:03 - component-loader - INFO - Found class ErrorHandling in module app.components.control_flow.loopNode
2025-07-17 14:14:03 - component-loader - INFO - Found class ExitCondition in module app.components.control_flow.loopNode
2025-07-17 14:14:03 - component-loader - INFO - Found class IterationSettings in module app.components.control_flow.loopNode
2025-07-17 14:14:03 - component-loader - INFO - Found class IterationSource in module app.components.control_flow.loopNode
2025-07-17 14:14:03 - component-loader - INFO - Found class LoopConfig in module app.components.control_flow.loopNode
2025-07-17 14:14:03 - component-loader - INFO - Found class LoopNode in module app.components.control_flow.loopNode
2025-07-17 14:14:03 - component-loader - INFO - Found class RangeConfig in module app.components.control_flow.loopNode
2025-07-17 14:14:03 - component-loader - INFO - Found class ResultAggregation in module app.components.control_flow.loopNode
2025-07-17 14:14:03 - component-loader - INFO - Found class ConditionalNode in module app.components.control_flow.conditionalNode
2025-07-17 14:14:03 - component-loader - INFO - Found class IDGeneratorComponent in module app.components.helper.id_generator
2025-07-17 14:14:03 - component-loader - INFO - Found class DocExtractorComponent in module app.components.helper.doc_extractor
2025-07-17 14:14:03 - component-loader - INFO - Loaded 30 component modules
2025-07-17 14:14:03 - workflow-service - INFO - Successfully loaded 30 component modules
2025-07-17 14:14:06 - workflow-service - INFO - WorkflowService initialized
Workflow service started on port 50056
2025-07-17 14:47:38 - workflow-service - INFO - discoverComponents request received
2025-07-17 14:47:38 [info     ] discoverComponents request received force_refresh=True
DEBUG: Component 'AgenticAI' in category 'AI' registered successfully
DEBUG: Component AgenticAI registered successfully
DEBUG: Component 'BasicLLMChain' in category 'AI' registered successfully
DEBUG: Component BasicLLMChain registered successfully
DEBUG: Component 'Classifier' in category 'AI' registered successfully
DEBUG: Component Classifier registered successfully
DEBUG: Component 'InformationExtractor' in category 'AI' registered successfully
DEBUG: Component InformationExtractor registered successfully
DEBUG: Component 'StartOutboundCallComponent' in category 'AI' registered successfully
DEBUG: Component StartOutboundCallComponent registered successfully
DEBUG: Component 'QuestionAnswerModule' in category 'AI' registered successfully
DEBUG: Component QuestionAnswerModule registered successfully
DEBUG: Component 'SentimentAnalyzer' in category 'AI' registered successfully
DEBUG: Component SentimentAnalyzer registered successfully
DEBUG: Component 'Summarizer' in category 'AI' registered successfully
DEBUG: Component Summarizer registered successfully
DEBUG: Component 'ConditionalNode' in category 'Logic' registered successfully
DEBUG: Component ConditionalNode registered successfully
DEBUG: Component 'LoopNode' in category 'Logic' registered successfully
DEBUG: Component LoopNode registered successfully
DEBUG: Component 'ApiRequestNode' in category 'Data Interaction' registered successfully
DEBUG: Component ApiRequestNode registered successfully
DEBUG: Component 'StartNode' in category 'IO' registered successfully
DEBUG: Component StartNode registered successfully
Error importing module No module named 'app.utils.workflow_builder.inputs'
Error walking package: app.components.processing
2025-07-17 14:47:38 [info     ] Components discovered successfully count=12
2025-07-17 14:47:38 - workflow-service - INFO - discoverComponents request received
2025-07-17 14:47:38 [info     ] discoverComponents request received force_refresh=True
DEBUG: Component 'AgenticAI' in category 'AI' registered successfully
DEBUG: Component AgenticAI registered successfully
DEBUG: Component 'BasicLLMChain' in category 'AI' registered successfully
DEBUG: Component BasicLLMChain registered successfully
DEBUG: Component 'Classifier' in category 'AI' registered successfully
DEBUG: Component Classifier registered successfully
DEBUG: Component 'InformationExtractor' in category 'AI' registered successfully
DEBUG: Component InformationExtractor registered successfully
DEBUG: Component 'StartOutboundCallComponent' in category 'AI' registered successfully
DEBUG: Component StartOutboundCallComponent registered successfully
DEBUG: Component 'QuestionAnswerModule' in category 'AI' registered successfully
DEBUG: Component QuestionAnswerModule registered successfully
DEBUG: Component 'SentimentAnalyzer' in category 'AI' registered successfully
DEBUG: Component SentimentAnalyzer registered successfully
DEBUG: Component 'Summarizer' in category 'AI' registered successfully
DEBUG: Component Summarizer registered successfully
DEBUG: Component 'ConditionalNode' in category 'Logic' registered successfully
DEBUG: Component ConditionalNode registered successfully
DEBUG: Component 'LoopNode' in category 'Logic' registered successfully
DEBUG: Component LoopNode registered successfully
DEBUG: Component 'ApiRequestNode' in category 'Data Interaction' registered successfully
DEBUG: Component ApiRequestNode registered successfully
DEBUG: Component 'StartNode' in category 'IO' registered successfully
DEBUG: Component StartNode registered successfully
Error importing module No module named 'app.utils.workflow_builder.inputs'
Error walking package: app.components.processing
2025-07-17 14:47:38 [info     ] Components discovered successfully count=12
