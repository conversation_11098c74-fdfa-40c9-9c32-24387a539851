"""
DataComposeComponent - Component for composing multiple data structures into a structured dictionary.

This component creates a new dictionary where each input becomes a separate key-value pair
with custom key names, allowing for structured data composition without merging.
"""

import copy
import logging
from typing import Any, Dict, Optional
from pydantic import BaseModel, Field, field_validator, ValidationError

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class DataComposeRequest(BaseModel):
    """
    Schema for data compose request.
    """
    main_input: Any = Field(..., description="Main data structure to compose")
    num_additional_inputs: int = Field(2, description="Number of additional inputs to process")
    output_key_1: Optional[str] = Field("data_1", description="Custom key name for main input")
    output_key_2: Optional[str] = Field("data_2", description="Custom key name for first additional input")
    output_key_3: Optional[str] = Field("data_3", description="Custom key name for second additional input")
    output_key_4: Optional[str] = Field("data_4", description="Custom key name for third additional input")
    output_key_5: Optional[str] = Field("data_5", description="Custom key name for fourth additional input")
    output_key_6: Optional[str] = Field("data_6", description="Custom key name for fifth additional input")
    output_key_7: Optional[str] = Field("data_7", description="Custom key name for sixth additional input")
    output_key_8: Optional[str] = Field("data_8", description="Custom key name for seventh additional input")
    output_key_9: Optional[str] = Field("data_9", description="Custom key name for eighth additional input")
    output_key_10: Optional[str] = Field("data_10", description="Custom key name for ninth additional input")
    output_key_11: Optional[str] = Field("data_11", description="Custom key name for tenth additional input")
    
    # Dynamic input fields (will be validated separately)
    input_1: Optional[Any] = Field(None, description="First additional data structure")
    input_2: Optional[Any] = Field(None, description="Second additional data structure")
    input_3: Optional[Any] = Field(None, description="Third additional data structure")
    input_4: Optional[Any] = Field(None, description="Fourth additional data structure")
    input_5: Optional[Any] = Field(None, description="Fifth additional data structure")
    input_6: Optional[Any] = Field(None, description="Sixth additional data structure")
    input_7: Optional[Any] = Field(None, description="Seventh additional data structure")
    input_8: Optional[Any] = Field(None, description="Eighth additional data structure")
    input_9: Optional[Any] = Field(None, description="Ninth additional data structure")
    input_10: Optional[Any] = Field(None, description="Tenth additional data structure")

    # Add validator to ensure num_additional_inputs is valid
    @field_validator('num_additional_inputs')
    def check_num_additional_inputs(cls, v):
        if not isinstance(v, int) or v < 0 or v > 10:
            raise ValueError(f"Number of additional inputs must be between 0 and 10, got {v}")
        return v


@register_component("DataComposeComponent")
class DataComposeExecutor(BaseComponent):
    """
    Component for composing multiple data structures into a structured dictionary.

    This component creates a new dictionary where each input becomes a separate 
    key-value pair with custom key names. Unlike merge operations, this preserves
    each input as a distinct entry in the output structure.
    
    Features:
    - Compose up to 11 data structures (1 main + 10 additional)
    - Custom key naming for each input
    - Preserves original data structure of each input
    - No data merging or modification
    """

    # Maximum number of additional inputs to support
    MAX_ADDITIONAL_INPUTS = 10

    def __init__(self):
        """
        Initialize the DataComposeExecutor component.
        """
        super().__init__()
        self.request_schema = DataComposeRequest
        logger.info("DataComposeExecutor initialized")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate the data compose request payload.

        Args:
            payload: The request payload

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating data compose request for request_id: {request_id}")
        logger.debug(f"Payload for validation (request_id={request_id}): {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for validation.")
            # Use the tool_parameters as the actual payload for validation
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"VALIDATION PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # First use schema validation (from parent class)
            # We still validate the original payload for backward compatibility
            schema_validation = await super().validate(payload)
            if not schema_validation.is_valid:
                # Try validating the parameters instead
                try:
                    DataComposeRequest(**parameters)
                except ValidationError as e:
                    logger.error(f"Schema validation failed for request_id {request_id}: {schema_validation.error_message}")
                    return schema_validation
            logger.debug(f"Schema validation passed for request_id {request_id}")

            # Validate main_input exists
            main_input = parameters.get("main_input")
            if main_input is None:
                error_msg = f"Missing required field 'main_input' for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"missing_field": "main_input"}
                )

            # Validate num_additional_inputs
            num_additional_inputs = parameters.get("num_additional_inputs", 2)
            if not isinstance(num_additional_inputs, int) or num_additional_inputs < 0 or num_additional_inputs > self.MAX_ADDITIONAL_INPUTS:
                error_msg = f"Invalid num_additional_inputs for request_id {request_id}: {num_additional_inputs}. Must be between 0 and {self.MAX_ADDITIONAL_INPUTS}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"invalid_num_inputs": num_additional_inputs}
                )

            logger.info(f"Data compose payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected validation error for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            return ValidationResult(
                is_valid=False,
                error_message=error_msg,
                error_details={"exception": str(e)}
            )

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the data composition operation on multiple input data structures.

        Args:
            payload: The request payload containing:
                - main_input: The main data structure
                - input_1, input_2, etc.: Additional data structures to compose
                - num_additional_inputs: Number of additional inputs to process
                - output_key_1, output_key_2, etc.: Custom key names for each input

        Returns:
            A dictionary with the composed result
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing data compose request for request_id: {request_id}")
        logger.debug(f"Payload for processing (request_id={request_id}): {payload}")

        try:
            # Check if the payload has a tool_parameters field (from the API component)
            if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
                logger.info(f"Found 'tool_parameters' field in payload. Using it for processing.")
                # Use the tool_parameters as the actual payload for processing
                parameters = payload["tool_parameters"]
                # Keep the request_id from the original payload
                parameters["request_id"] = request_id
            else:
                # Use the original payload
                parameters = payload

            # Get main input
            main_input = parameters.get("main_input")
            if main_input is None:
                error_msg = f"Missing required field 'main_input' for request_id {request_id}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg
                }

            # Get number of additional inputs
            num_additional_inputs = parameters.get("num_additional_inputs", 2)
            if not isinstance(num_additional_inputs, int):
                num_additional_inputs = int(num_additional_inputs)
            num_additional_inputs = min(max(0, num_additional_inputs), self.MAX_ADDITIONAL_INPUTS)

            # Log input values for debugging
            logger.info(f"Composing data for request_id {request_id}. Num additional inputs: {num_additional_inputs}")
            logger.debug(f"Main input type: {type(main_input).__name__}")

            # Create a new dictionary with custom keys
            result = {}

            # Add main input with first key
            output_key_1 = parameters.get("output_key_1", "data_1")
            result[output_key_1] = copy.deepcopy(main_input)
            logger.info(f"Added main input as '{output_key_1}' for request_id {request_id}")

            # Add additional inputs with their respective keys
            for i in range(1, num_additional_inputs + 1):
                input_name = f"input_{i}"
                input_value = parameters.get(input_name)

                if input_value is not None:
                    # Get the corresponding output key (output_key_2 for input_1, output_key_3 for input_2, etc.)
                    output_key_name = f"output_key_{i + 1}"
                    output_key_value = parameters.get(output_key_name, f"data_{i + 1}")

                    result[output_key_value] = copy.deepcopy(input_value)
                    logger.info(f"Added {input_name} as '{output_key_value}' for request_id {request_id}")

            logger.info(f"Data composition completed for request_id {request_id} with keys: {list(result.keys())}")
            return {
                "status": "success",
                "result": result
            }

        except Exception as e:
            error_msg = f"Error processing data compose request for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.exception(e)
            return {
                "status": "error",
                "error": error_msg
            }
