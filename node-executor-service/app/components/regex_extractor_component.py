"""
Regex Extractor Component - Extracts data from text using regular expressions.
"""
import logging
import re
import signal
import traceback
from typing import Dict, Any, List, Union

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("RegexExtractorComponent")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


@register_component("RegexExtractorComponent")
class RegexExtractorComponent(BaseComponent):
    """
    Component for extracting data from text using regular expressions.
    """
    
    def __init__(self):
        """
        Initialize the Regex Extractor component.
        """
        logger.info("Initializing Regex Extractor Component")
        super().__init__()
        self.timeout_seconds = 2  # ReDoS protection
        logger.info("Regex Extractor Component initialized successfully")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a regex extraction payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating regex extraction payload for request_id: {request_id}")
        logger.debug(f"Payload to validate for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for validation.")
            parameters = payload["tool_parameters"]
            parameters["request_id"] = request_id
        else:
            parameters = payload

        try:
            # Check required fields
            if "input_text" not in parameters or not parameters["input_text"]:
                error_msg = f"Missing required field 'input_text' in payload for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"input_text": "required field missing"})
            logger.debug(f"Required field 'input_text' is present for request_id {request_id}")

            if "regex_pattern" not in parameters or not parameters["regex_pattern"]:
                error_msg = f"Missing required field 'regex_pattern' in payload for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"regex_pattern": "required field missing"})
            logger.debug(f"Required field 'regex_pattern' is present for request_id {request_id}")

            # Validate regex pattern syntax
            try:
                re.compile(parameters["regex_pattern"])
                logger.debug(f"Regex pattern syntax is valid for request_id {request_id}")
            except re.error as e:
                error_msg = f"Invalid regex pattern syntax for request_id {request_id}: {str(e)}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"regex_pattern": f"invalid syntax: {str(e)}"})

            # Validate extraction_mode if present
            extraction_mode = parameters.get("extraction_mode", "first_match")
            valid_modes = ["first_match", "all_matches"]
            if extraction_mode not in valid_modes:
                error_msg = f"Invalid extraction_mode '{extraction_mode}' for request_id {request_id}. Must be one of: {valid_modes}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"extraction_mode": f"must be one of: {valid_modes}"})
            logger.debug(f"extraction_mode validation passed for request_id {request_id}")

            # Validate output_format if present
            output_format = parameters.get("output_format", "full_match")
            valid_formats = ["full_match", "first_capture_group", "named_groups"]
            if output_format not in valid_formats:
                error_msg = f"Invalid output_format '{output_format}' for request_id {request_id}. Must be one of: {valid_formats}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"output_format": f"must be one of: {valid_formats}"})
            logger.debug(f"output_format validation passed for request_id {request_id}")

            # Validate on_no_match if present
            on_no_match = parameters.get("on_no_match", "continue_empty")
            valid_no_match_options = ["continue_empty", "fail_workflow"]
            if on_no_match not in valid_no_match_options:
                error_msg = f"Invalid on_no_match '{on_no_match}' for request_id {request_id}. Must be one of: {valid_no_match_options}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"on_no_match": f"must be one of: {valid_no_match_options}"})
            logger.debug(f"on_no_match validation passed for request_id {request_id}")

            # Validate boolean flags if present
            for flag_name in ["case_insensitive", "multiline", "dot_all"]:
                flag_value = parameters.get(flag_name)
                if flag_value is not None and not isinstance(flag_value, bool):
                    error_msg = f"Field '{flag_name}' must be a boolean, got {type(flag_value).__name__} for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(is_valid=False, error_message=error_msg, error_details={flag_name: "must be a boolean"})
            logger.debug(f"Boolean flags validation passed for request_id {request_id}")

            logger.info(f"Regex extraction payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg, error_details={"validation_error": str(e)})

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process regex extraction request.

        Args:
            payload: The request payload

        Returns:
            The processing result
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing regex extraction request for request_id: {request_id}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            parameters = payload["tool_parameters"]
            parameters["request_id"] = request_id
        else:
            parameters = payload

        try:
            # Extract parameters
            input_text = parameters["input_text"]
            regex_pattern = parameters["regex_pattern"]
            extraction_mode = parameters.get("extraction_mode", "first_match")
            output_format = parameters.get("output_format", "full_match")
            case_insensitive = parameters.get("case_insensitive", False)
            multiline = parameters.get("multiline", False)
            dot_all = parameters.get("dot_all", False)
            on_no_match = parameters.get("on_no_match", "continue_empty")

            logger.info(f"Processing regex extraction for request_id {request_id} with pattern: '{regex_pattern}', mode: {extraction_mode}, format: {output_format}")

            # Convert input to string if not already
            if not isinstance(input_text, str):
                logger.debug(f"Converting input_text from type {type(input_text).__name__} to string for request_id {request_id}")
                try:
                    input_text = str(input_text)
                    logger.debug(f"Converted input to string for request_id {request_id}")
                except Exception as e:
                    error_msg = f"Failed to convert input to string for request_id {request_id}: {str(e)}"
                    logger.error(error_msg)
                    return {"status": "error", "error": error_msg}

            # Build regex flags
            flags = 0
            if case_insensitive:
                flags |= re.IGNORECASE
                logger.debug(f"Applied IGNORECASE flag for request_id {request_id}")
            if multiline:
                flags |= re.MULTILINE
                logger.debug(f"Applied MULTILINE flag for request_id {request_id}")
            if dot_all:
                flags |= re.DOTALL
                logger.debug(f"Applied DOTALL flag for request_id {request_id}")

            # Compile pattern with timeout protection
            try:
                compiled_pattern = re.compile(regex_pattern, flags)
                logger.debug(f"Successfully compiled regex pattern for request_id {request_id}")
            except re.error as e:
                error_msg = f"Invalid regex pattern for request_id {request_id}: {str(e)}"
                logger.error(error_msg)
                return {"status": "error", "error": error_msg}

            # Execute regex with timeout protection
            matches = []
            try:
                # Set timeout alarm for ReDoS protection
                def timeout_handler(signum, frame):
                    raise TimeoutError("Regex execution timed out")
                
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(self.timeout_seconds)
                
                if extraction_mode == "first_match":
                    match = compiled_pattern.search(input_text)
                    matches = [match] if match else []
                    logger.debug(f"First match search completed for request_id {request_id}, found: {len(matches)} matches")
                else:  # all_matches
                    matches = list(compiled_pattern.finditer(input_text))
                    logger.debug(f"All matches search completed for request_id {request_id}, found: {len(matches)} matches")
                
                # Clear alarm
                signal.alarm(0)
                
            except TimeoutError:
                signal.alarm(0)  # Clear alarm
                error_msg = f"Regex execution timed out for request_id {request_id} (possible ReDoS attack)"
                logger.error(error_msg)
                return {"status": "error", "error": error_msg}
            except Exception as e:
                signal.alarm(0)  # Clear alarm
                error_msg = f"Regex execution failed for request_id {request_id}: {str(e)}"
                logger.error(error_msg)
                logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
                return {"status": "error", "error": error_msg}

            # Handle no matches
            if not matches:
                logger.info(f"No matches found for request_id {request_id}")
                if on_no_match == "fail_workflow":
                    error_msg = f"No matches found for the given pattern for request_id {request_id}"
                    logger.error(error_msg)
                    return {"status": "error", "error": error_msg}
                else:  # continue_empty
                    logger.info(f"Continuing with empty results for request_id {request_id}")
                    return {
                        "status": "success",
                        "matches": [],
                        "first_match": None,
                        "match_count": 0,
                        "is_match_found": False
                    }

            # Format results based on output_format
            try:
                formatted_matches = self._format_matches(matches, output_format)
                logger.debug(f"Successfully formatted {len(formatted_matches)} matches for request_id {request_id}")
            except Exception as e:
                error_msg = f"Error formatting matches for request_id {request_id}: {str(e)}"
                logger.error(error_msg)
                logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
                return {"status": "error", "error": error_msg}

            logger.info(f"Regex extraction successful for request_id {request_id}. Found {len(matches)} matches.")
            return {
                "status": "success",
                "matches": formatted_matches,
                "first_match": formatted_matches[0] if formatted_matches else None,
                "match_count": len(matches),
                "is_match_found": len(matches) > 0
            }

        except Exception as e:
            error_msg = f"Unexpected error processing regex extraction request for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {"status": "error", "error": error_msg}

    def _format_matches(self, matches: List, output_format: str) -> List[Union[str, Dict[str, str]]]:
        """
        Format match results based on output format.
        
        Args:
            matches: List of regex match objects
            output_format: Format type ('full_match', 'first_capture_group', 'named_groups')
            
        Returns:
            Formatted list of matches
        """
        logger.debug(f"Formatting {len(matches)} matches with format: {output_format}")
        
        if output_format == "full_match":
            result = [match.group(0) for match in matches]
            logger.debug(f"Formatted as full matches: {len(result)} items")
            return result
        elif output_format == "first_capture_group":
            result = []
            for match in matches:
                if match.groups():
                    result.append(match.group(1))
                else:
                    result.append(match.group(0))  # Fallback to full match if no groups
            logger.debug(f"Formatted as first capture groups: {len(result)} items")
            return result
        elif output_format == "named_groups":
            result = []
            for match in matches:
                group_dict = match.groupdict()
                if group_dict:
                    result.append(group_dict)
                else:
                    result.append({"match": match.group(0)})  # Fallback if no named groups
            logger.debug(f"Formatted as named groups: {len(result)} items")
            return result
        else:
            # Fallback to full match
            logger.warning(f"Unknown output format '{output_format}', falling back to full_match")
            result = [match.group(0) for match in matches]
            return result