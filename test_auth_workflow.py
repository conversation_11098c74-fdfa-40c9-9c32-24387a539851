#!/usr/bin/env python3
"""
Test script to verify authentication analysis functionality
"""

import json
import sys
import os

# Add the workflow-service to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

from app.services.workflow_auth_analyzer import analyze_and_update_workflow_auth

def test_authentication_analysis():
    """Test the authentication analysis with a sample workflow"""
    
    # Sample workflow with authentication requirements
    test_workflow = {
        "start_nodes": [
            {
                "id": "start_1",
                "type": "start_node",
                "name": "Start Node"
            }
        ],
        "available_nodes": [
            {
                "id": "ai_1",
                "type": "agentic_ai",
                "name": "AI Agent",
                "env_keys": ["OPENAI_API_KEY", "ANTHROPIC_API_KEY"],
                "oauth_details": {
                    "provider": "google",
                    "scopes": ["https://www.googleapis.com/auth/gmail.readonly"]
                }
            },
            {
                "id": "api_1", 
                "type": "api_request",
                "name": "API Request",
                "env_keys": ["SLACK_BOT_TOKEN"]
            },
            {
                "id": "simple_1",
                "type": "delay_component",
                "name": "Simple Delay"
                # No authentication requirements
            }
        ]
    }
    
    print("Original workflow:")
    print(json.dumps(test_workflow, indent=2))
    print("\n" + "="*50 + "\n")
    
    # Analyze and update workflow
    try:
        updated_workflow = analyze_and_update_workflow_auth(test_workflow)
        
        print("Updated workflow with authentication analysis:")
        print(json.dumps(updated_workflow, indent=2))
        
        # Check if auth summary was added
        auth_summary_found = False
        for node in updated_workflow.get("available_nodes", []):
            if node.get("type") == "auth_summary":
                auth_summary_found = True
                auth_summary = node.get("auth_summary", {})
                
                print("\n" + "="*50)
                print("AUTHENTICATION ANALYSIS RESULTS:")
                print("="*50)
                print(f"Requires Authentication: {auth_summary.get('requires_authentication')}")
                print(f"Environment Keys Required: {auth_summary.get('env_keys_required')}")
                print(f"OAuth Providers Required: {auth_summary.get('oauth_providers_required')}")
                print(f"Total Auth Items: {auth_summary.get('total_auth_items')}")
                print(f"Auth Complexity: {auth_summary.get('auth_complexity')}")
                print(f"Components with Auth: {len(auth_summary.get('components_with_auth', []))}")
                
                for component in auth_summary.get('components_with_auth', []):
                    print(f"  - {component.get('component_name')}: {component.get('env_keys', [])} {component.get('oauth_provider', '')}")
                
                break
        
        if not auth_summary_found:
            print("❌ ERROR: Authentication summary not found in updated workflow!")
            return False
        
        print("\n✅ Authentication analysis completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Authentication analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_no_auth_workflow():
    """Test with a workflow that has no authentication requirements"""
    
    test_workflow = {
        "start_nodes": [
            {
                "id": "start_1",
                "type": "start_node",
                "name": "Start Node"
            }
        ],
        "available_nodes": [
            {
                "id": "delay_1",
                "type": "delay_component", 
                "name": "Simple Delay"
            },
            {
                "id": "text_1",
                "type": "combine_text",
                "name": "Combine Text"
            }
        ]
    }
    
    print("\n" + "="*50)
    print("TESTING WORKFLOW WITH NO AUTHENTICATION")
    print("="*50)
    
    try:
        updated_workflow = analyze_and_update_workflow_auth(test_workflow)
        
        # Check auth summary
        for node in updated_workflow.get("available_nodes", []):
            if node.get("type") == "auth_summary":
                auth_summary = node.get("auth_summary", {})
                print(f"Requires Authentication: {auth_summary.get('requires_authentication')}")
                print(f"Auth Complexity: {auth_summary.get('auth_complexity')}")
                
                if not auth_summary.get('requires_authentication') and auth_summary.get('auth_complexity') == 'none':
                    print("✅ Correctly identified no authentication requirements!")
                    return True
                else:
                    print("❌ ERROR: Should have identified no authentication requirements!")
                    return False
        
        print("❌ ERROR: No auth summary found!")
        return False
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing Workflow Authentication Analysis")
    print("="*50)
    
    # Test 1: Workflow with authentication requirements
    success1 = test_authentication_analysis()
    
    # Test 2: Workflow without authentication requirements  
    success2 = test_no_auth_workflow()
    
    if success1 and success2:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
