// Debug script to test format field processing
console.log("=== DEBUG: Format Field Processing ===");

// Simulate the format field schema from Google Docs create_document
const formatFieldSchema = {
    "anyOf": [
        {
            "enum": ["plain", "html", "markdown"],
            "type": "string"
        },
        {
            "type": "null"
        }
    ],
    "default": "plain",
    "title": "Format"
};

// Test 1: Check how DynamicInputTypeMapper processes anyOf with enum
console.log("\n1. Testing DynamicInputTypeMapper.getMapping() with format field schema:");
console.log("Input schema:", JSON.stringify(formatFieldSchema, null, 2));

// Simulate the logic from DynamicInputTypeMapper
function simulateGetMapping(schema) {
    const { type, format, enum: enumValues, anyOf } = schema;
    
    console.log("- anyOf:", anyOf);
    console.log("- enum at root:", enumValues);
    
    // Handle anyOf structures - choose the first non-null type
    if (anyOf && anyOf.length > 0) {
        console.log("- Processing anyOf structure");
        
        // Find the first schema that's not null type
        const nonNullSchema = anyOf.find(s => s.type !== 'null');
        console.log("- Found non-null schema:", nonNullSchema);
        
        if (nonNullSchema) {
            // Merge the properties from the original schema with the selected anyOf option
            const mergedSchema = {
                ...schema,
                ...nonNullSchema,
                // Keep anyOf for reference but use the selected type
                anyOf: undefined
            };
            console.log("- Merged schema:", JSON.stringify(mergedSchema, null, 2));
            return simulateGetMapping(mergedSchema);
        }
    }
    
    // Handle enum types (convert to dropdown)
    if (enumValues && enumValues.length > 0) {
        console.log("- Found enum values:", enumValues);
        return {
            component: 'SelectInput',
            inputType: 'dropdown',
            props: { options: enumValues.map(val => ({ value: val, label: String(val) })) }
        };
    }
    
    console.log("- No enum found, falling back to string");
    return {
        component: 'StringInput',
        inputType: 'string'
    };
}

const result = simulateGetMapping(formatFieldSchema);
console.log("Result:", JSON.stringify(result, null, 2));

// Test 2: Check what options should be generated
console.log("\n2. Expected options for SelectInput:");
const expectedOptions = ["plain", "html", "markdown"].map(val => ({ value: val, label: String(val) }));
console.log("Expected options:", JSON.stringify(expectedOptions, null, 2));

// Test 3: Check if the component registry includes SelectInput
console.log("\n3. Component Registry Check:");
const componentRegistry = {
  StringInput: "StringInput",
  NumberInput: "NumberInput", 
  BooleanInput: "BooleanInput",
  ObjectInput: "ObjectInput",
  ArrayInput: "ArrayInput",
  SelectInput: "SelectInput",
  HandleInput: "HandleInput",
  CredentialInput: "CredentialInput",
  DynamicInput: "DynamicInput",
};

console.log("SelectInput in registry:", componentRegistry.SelectInput ? "YES" : "NO");

console.log("\n=== END DEBUG ===");