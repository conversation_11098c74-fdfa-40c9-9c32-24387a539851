#!/usr/bin/env python3
"""
Simple test to verify the regex extractor component files are correctly structured.
"""

import re
import asyncio

def test_regex_logic():
    """Test the core regex logic without dependencies."""
    print("🧪 Testing Core Regex Logic...")
    
    test_cases = [
        {
            "name": "Extract Email",
            "input_text": "Contact <NAME_EMAIL>",
            "regex_pattern": r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
            "extraction_mode": "first_match",
            "output_format": "full_match",
            "expected": ["<EMAIL>"]
        },
        {
            "name": "Extract All Hashtags",
            "input_text": "#automation #nocode is the future",
            "regex_pattern": r"#(\w+)",
            "extraction_mode": "all_matches",
            "output_format": "first_capture_group",
            "expected": ["automation", "nocode"]
        },
        {
            "name": "Named Groups",
            "input_text": "https://app.myservice.io/dashboard",
            "regex_pattern": r"https?://(?P<domain>[^/]+)/(?P<path>.*)",
            "extraction_mode": "first_match",
            "output_format": "named_groups",
            "expected": [{"domain": "app.myservice.io", "path": "dashboard"}]
        }
    ]
    
    def format_matches(matches, output_format):
        """Format matches based on output format."""
        if output_format == "full_match":
            return [match.group(0) for match in matches]
        elif output_format == "first_capture_group":
            return [match.group(1) if match.groups() else match.group(0) for match in matches]
        elif output_format == "named_groups":
            return [match.groupdict() if match.groupdict() else {"match": match.group(0)} for match in matches]
        else:
            return [match.group(0) for match in matches]
    
    passed = 0
    failed = 0
    
    for test_case in test_cases:
        print(f"\n  Test: {test_case['name']}")
        
        try:
            # Compile pattern
            pattern = re.compile(test_case["regex_pattern"])
            
            # Execute matching
            if test_case["extraction_mode"] == "first_match":
                match = pattern.search(test_case["input_text"])
                matches = [match] if match else []
            else:  # all_matches
                matches = list(pattern.finditer(test_case["input_text"]))
            
            # Format results
            formatted_matches = format_matches(matches, test_case["output_format"])
            
            # Check results
            if formatted_matches == test_case["expected"]:
                print(f"    ✅ Passed - Result: {formatted_matches}")
                passed += 1
            else:
                print(f"    ❌ Failed - Expected: {test_case['expected']}, Got: {formatted_matches}")
                failed += 1
                
        except Exception as e:
            print(f"    ❌ Exception: {str(e)}")
            failed += 1
    
    print(f"\n📊 Core Logic Tests: {passed} passed, {failed} failed")
    return failed == 0

def test_file_structure():
    """Test that the component files exist and have the right structure."""
    print("\n🧪 Testing File Structure...")
    
    import os
    
    files_to_check = [
        "/Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/components/processing/regex_extractor.py",
        "/Users/<USER>/Desktop/ruh_ai/backend/node-executor-service/app/components/regex_extractor_component.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"  ✅ File exists: {os.path.basename(file_path)}")
            
            # Check file content
            with open(file_path, 'r') as f:
                content = f.read()
                
            if "RegexExtractorComponent" in content:
                print(f"    ✅ Contains RegexExtractorComponent class")
            else:
                print(f"    ❌ Missing RegexExtractorComponent class")
                
            if "regex_pattern" in content and "input_text" in content:
                print(f"    ✅ Contains required input fields")
            else:
                print(f"    ❌ Missing required input fields")
                
        else:
            print(f"  ❌ File missing: {file_path}")
            return False
    
    print("📊 File Structure Tests: All passed")
    return True

def main():
    """Run all tests."""
    print("🚀 Testing Regex Extractor Component Implementation")
    print("=" * 60)
    
    # Test file structure
    structure_success = test_file_structure()
    
    # Test core logic
    logic_success = test_regex_logic()
    
    print("\n" + "=" * 60)
    if structure_success and logic_success:
        print("🎉 Basic tests passed! The implementation structure looks good.")
        print("\n📝 Next steps:")
        print("  1. The components are created and structured correctly")
        print("  2. The core regex logic works as expected")
        print("  3. You can now test the components within their respective service environments")
        print("  4. Make sure to restart the services to pick up the new components")
        return 0
    else:
        print("💥 Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)