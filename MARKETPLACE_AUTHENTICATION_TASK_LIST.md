# Marketplace Authentication Requirements - Implementation Task List

## Document Information
- **Created**: July 22, 2025
- **Status**: Ready for Implementation
- **Related PRD**: MARKETPLACE_AUTHENTICATION_REQUIREMENTS_PRD.md

---

## Implementation Overview

This task list provides detailed technical implementation steps for the Marketplace Authentication Requirements feature. Tasks are organized by service and implementation phase.

**Estimated Timeline**: 6-8 weeks
**Team Size**: 3-4 developers (1 Backend, 1 Frontend, 1 Full-stack, 1 DevOps)

---

## Phase 1: Backend Infrastructure (Weeks 1-2)

### 1.1 Database Schema & Migrations

#### Task 1.1.1: Create Database Migration Scripts
**Service**: workflow-service  
**Priority**: P0 (Blocking)  
**Assignee**: Backend Developer  
**Estimated**: 1 day  

**Implementation Details:**
- **File**: `workflow-service/app/db/migrations/versions/add_credential_requirements.py`
- **Requirements**:
  - Create `workflow_credential_requirements` table
  - Add `credential_requirements` JSONB column to `workflows` table
  - Add `authentication_complexity` column to `workflows` table
  - Create performance indexes

**Acceptance Criteria:**
```sql
-- New table structure
CREATE TABLE workflow_credential_requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    credential_type VARCHAR(100) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    is_required BOOLEAN DEFAULT true,
    component_count INTEGER DEFAULT 1,
    description TEXT,
    provider_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(workflow_id, field_name)
);

-- Indexes for performance
CREATE INDEX idx_wcr_workflow_id ON workflow_credential_requirements(workflow_id);
CREATE INDEX idx_wcr_credential_type ON workflow_credential_requirements(credential_type);
CREATE INDEX idx_wcr_provider ON workflow_credential_requirements(provider_name);

-- Enhanced workflows table
ALTER TABLE workflows ADD COLUMN credential_requirements JSONB;
ALTER TABLE workflows ADD COLUMN authentication_complexity VARCHAR(20) DEFAULT 'none';

CREATE INDEX idx_workflows_auth_complexity ON workflows(authentication_complexity);
CREATE INDEX idx_workflows_credential_reqs ON workflows USING GIN(credential_requirements);
```

**Testing Requirements:**
- [ ] Migration runs successfully on empty database
- [ ] Migration runs successfully on database with existing workflows
- [ ] All indexes created correctly
- [ ] Foreign key constraints work properly
- [ ] Rollback migration works correctly

#### Task 1.1.2: Create Credential Requirement Models
**Service**: workflow-service  
**Priority**: P0 (Blocking)  
**Assignee**: Backend Developer  
**Estimated**: 0.5 days  

**Implementation Details:**
- **File**: `workflow-service/app/models/workflow_builder/credential_requirements.py`

```python
from sqlalchemy import Column, String, Boolean, Integer, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

class WorkflowCredentialRequirement(Base):
    __tablename__ = "workflow_credential_requirements"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("workflows.id", ondelete="CASCADE"), nullable=False)
    credential_type = Column(String(100), nullable=False)
    field_name = Column(String(100), nullable=False)
    is_required = Column(Boolean, default=True)
    component_count = Column(Integer, default=1)
    description = Column(Text)
    provider_name = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    workflow = relationship("Workflow", back_populates="credential_requirements_detailed")

# Pydantic Models
class CredentialRequirementSpec(BaseModel):
    credential_type: str = Field(..., description="Type of credential (api_key, oauth, password)")
    field_name: str = Field(..., description="Field name in component")
    is_required: bool = Field(True, description="Whether credential is required")
    component_count: int = Field(1, description="Number of components using this credential")
    description: Optional[str] = Field(None, description="Human-readable description")
    provider_name: Optional[str] = Field(None, description="Provider name (openai, github, etc.)")

class WorkflowCredentialRequirements(BaseModel):
    workflow_id: str
    credential_requirements: List[CredentialRequirementSpec]
    authentication_complexity: str = Field(..., description="none|simple|moderate|complex")
    total_requirements: int
    required_providers: List[str]
    
class CredentialCoverageReport(BaseModel):
    total_requirements: int
    available_credentials: int
    missing_credentials: int
    coverage_percentage: float
    detailed_status: List[Dict[str, Any]]
```

**Acceptance Criteria:**
- [ ] Models include all required fields from PRD
- [ ] Proper relationships with existing Workflow model
- [ ] Pydantic models for API serialization
- [ ] Input validation for all fields
- [ ] Model tests pass

### 1.2 Credential Analysis Engine

#### Task 1.2.1: Implement Workflow Credential Analyzer
**Service**: workflow-service  
**Priority**: P0 (Blocking)  
**Assignee**: Backend Developer  
**Estimated**: 2 days  

**Implementation Details:**
- **File**: `workflow-service/app/services/credential_analyzer.py`

```python
from typing import Dict, List, Any, Set
from app.models.workflow_builder.credential_requirements import CredentialRequirementSpec, WorkflowCredentialRequirements
import re
import json

class WorkflowCredentialAnalyzer:
    
    # Credential type mappings
    CREDENTIAL_TYPE_MAPPING = {
        "credential": "api_key",
        "oauth": "oauth",
        "password": "password",
        "api_key": "api_key"
    }
    
    # Provider detection patterns
    PROVIDER_PATTERNS = {
        "openai": ["openai", "gpt", "chatgpt"],
        "github": ["github", "gh_"],
        "google": ["google", "gmail", "gdrive"],
        "slack": ["slack"],
        "discord": ["discord"],
        "telegram": ["telegram", "tg_"],
        "anthropic": ["anthropic", "claude"]
    }
    
    def extract_credential_requirements(self, workflow_data: Dict) -> WorkflowCredentialRequirements:
        """Extract all credential requirements from workflow definition"""
        requirements = []
        all_nodes = []
        
        # Combine start_nodes and available_nodes
        if "start_nodes" in workflow_data:
            all_nodes.extend(workflow_data["start_nodes"])
        if "available_nodes" in workflow_data:
            all_nodes.extend(workflow_data["available_nodes"])
            
        # Track credential usage across components
        credential_usage = {}
        
        for node in all_nodes:
            node_requirements = self.analyze_node_inputs(node)
            for req in node_requirements:
                key = f"{req.credential_type}:{req.field_name}"
                if key in credential_usage:
                    credential_usage[key]["component_count"] += 1
                else:
                    credential_usage[key] = {
                        "requirement": req,
                        "component_count": 1
                    }
        
        # Build final requirements list
        for usage in credential_usage.values():
            req = usage["requirement"]
            req.component_count = usage["component_count"]
            requirements.append(req)
        
        # Calculate complexity and providers
        complexity = self.calculate_complexity(requirements)
        providers = self.extract_providers(requirements)
        
        return WorkflowCredentialRequirements(
            workflow_id=workflow_data.get("id", ""),
            credential_requirements=requirements,
            authentication_complexity=complexity,
            total_requirements=len(requirements),
            required_providers=providers
        )
    
    def analyze_node_inputs(self, node: Dict) -> List[CredentialRequirementSpec]:
        """Analyze a single node for credential requirements"""
        requirements = []
        
        # Check if node has inputs
        inputs = node.get("inputs", {})
        component_name = node.get("name", node.get("type", "unknown"))
        
        for field_name, input_config in inputs.items():
            if isinstance(input_config, dict):
                input_type = input_config.get("input_type", "")
                
                if input_type in self.CREDENTIAL_TYPE_MAPPING:
                    req = CredentialRequirementSpec(
                        credential_type=self.CREDENTIAL_TYPE_MAPPING[input_type],
                        field_name=field_name,
                        is_required=input_config.get("required", True),
                        description=input_config.get("description", f"{field_name} for {component_name}"),
                        provider_name=self.detect_provider(field_name, input_config)
                    )
                    requirements.append(req)
        
        return requirements
    
    def detect_provider(self, field_name: str, input_config: Dict) -> str:
        """Detect provider from field name and configuration"""
        field_lower = field_name.lower()
        description = input_config.get("description", "").lower()
        
        for provider, patterns in self.PROVIDER_PATTERNS.items():
            for pattern in patterns:
                if pattern in field_lower or pattern in description:
                    return provider
        
        return "generic"
    
    def calculate_complexity(self, requirements: List[CredentialRequirementSpec]) -> str:
        """Calculate authentication complexity based on requirements"""
        if not requirements:
            return "none"
        
        required_count = sum(1 for req in requirements if req.is_required)
        oauth_count = sum(1 for req in requirements if req.credential_type == "oauth")
        
        if required_count == 0:
            return "none"
        elif required_count <= 2 and oauth_count == 0:
            return "simple"
        elif required_count <= 5 and oauth_count <= 2:
            return "moderate"
        else:
            return "complex"
    
    def extract_providers(self, requirements: List[CredentialRequirementSpec]) -> List[str]:
        """Extract unique provider names from requirements"""
        providers = set()
        for req in requirements:
            if req.provider_name and req.provider_name != "generic":
                providers.add(req.provider_name)
        return sorted(list(providers))

# Utility Functions
class MCPToolAnalyzer:
    """Specialized analyzer for MCP tools"""
    
    def analyze_mcp_tool_auth(self, tool_config: Dict) -> List[CredentialRequirementSpec]:
        """Analyze MCP tool configuration for authentication requirements"""
        requirements = []
        
        # Check predefined fields in tool schema
        input_schema = tool_config.get("input_schema", {})
        predefined_fields = input_schema.get("predefined_fields", [])
        
        for field in predefined_fields:
            field_name = field.get("field_name", "")
            data_type = field.get("data_type", {}).get("type", "")
            is_required = field.get("required", False)
            
            # Detect if field is credential-related
            if self.is_credential_field(field_name, field):
                req = CredentialRequirementSpec(
                    credential_type="api_key",  # Default for MCP tools
                    field_name=field_name,
                    is_required=is_required,
                    description=field.get("description", f"Authentication for {field_name}"),
                    provider_name=self.detect_mcp_provider(field_name, tool_config)
                )
                requirements.append(req)
        
        return requirements
    
    def is_credential_field(self, field_name: str, field_config: Dict) -> bool:
        """Determine if a field is credential-related"""
        credential_indicators = [
            "api_key", "token", "auth", "password", "secret", 
            "credential", "key", "bearer", "oauth"
        ]
        
        field_lower = field_name.lower()
        description = field_config.get("description", "").lower()
        
        return any(indicator in field_lower or indicator in description 
                  for indicator in credential_indicators)
    
    def detect_mcp_provider(self, field_name: str, tool_config: Dict) -> str:
        """Detect provider for MCP tools"""
        server_path = tool_config.get("server_script_path", "").lower()
        
        # Extract provider from server path
        for provider, patterns in WorkflowCredentialAnalyzer.PROVIDER_PATTERNS.items():
            for pattern in patterns:
                if pattern in server_path or pattern in field_name.lower():
                    return provider
        
        return "mcp_tool"
```

**Acceptance Criteria:**
- [ ] Correctly identifies credential inputs from workflow JSON
- [ ] Supports all input types: credential, oauth, password, api_key
- [ ] Accurately detects providers from field names and descriptions
- [ ] Calculates complexity levels correctly
- [ ] Handles MCP tool authentication requirements
- [ ] Aggregates credential usage across multiple components
- [ ] Unit tests cover all credential types and edge cases

#### Task 1.2.2: Implement Credential Requirement Storage Service
**Service**: workflow-service  
**Priority**: P0 (Blocking)  
**Assignee**: Backend Developer  
**Estimated**: 1 day  

**Implementation Details:**
- **File**: `workflow-service/app/services/credential_requirement_service.py`

```python
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from typing import List, Dict, Optional
from app.models.workflow_builder.credential_requirements import (
    WorkflowCredentialRequirement, 
    WorkflowCredentialRequirements,
    CredentialRequirementSpec
)
from app.models.workflow import Workflow
from app.services.credential_analyzer import WorkflowCredentialAnalyzer
import json

class CredentialRequirementService:
    
    def __init__(self, db: Session):
        self.db = db
        self.analyzer = WorkflowCredentialAnalyzer()
    
    def extract_and_store_requirements(self, workflow_id: str) -> WorkflowCredentialRequirements:
        """Extract credential requirements and store them in database"""
        
        # Get workflow data
        workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if not workflow:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        # Prepare workflow data for analysis
        workflow_data = {
            "id": workflow_id,
            "start_nodes": json.loads(workflow.start_nodes) if workflow.start_nodes else [],
            "available_nodes": json.loads(workflow.available_nodes) if workflow.available_nodes else []
        }
        
        # Analyze requirements
        requirements = self.analyzer.extract_credential_requirements(workflow_data)
        
        # Clear existing requirements
        self.db.query(WorkflowCredentialRequirement).filter(
            WorkflowCredentialRequirement.workflow_id == workflow_id
        ).delete()
        
        # Store new requirements
        for req in requirements.credential_requirements:
            db_requirement = WorkflowCredentialRequirement(
                workflow_id=workflow_id,
                credential_type=req.credential_type,
                field_name=req.field_name,
                is_required=req.is_required,
                component_count=req.component_count,
                description=req.description,
                provider_name=req.provider_name
            )
            self.db.add(db_requirement)
        
        # Update workflow with aggregated data
        workflow.credential_requirements = requirements.dict(exclude={"workflow_id"})
        workflow.authentication_complexity = requirements.authentication_complexity
        
        self.db.commit()
        return requirements
    
    def get_workflow_requirements(self, workflow_id: str) -> Optional[WorkflowCredentialRequirements]:
        """Get stored credential requirements for workflow"""
        
        # Try to get from workflow JSONB field first (faster)
        workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if not workflow:
            return None
        
        if workflow.credential_requirements:
            return WorkflowCredentialRequirements(
                workflow_id=workflow_id,
                **workflow.credential_requirements
            )
        
        # Fallback to detailed table
        requirements = self.db.query(WorkflowCredentialRequirement).filter(
            WorkflowCredentialRequirement.workflow_id == workflow_id
        ).all()
        
        if not requirements:
            return None
        
        # Convert to response model
        requirement_specs = [
            CredentialRequirementSpec(
                credential_type=req.credential_type,
                field_name=req.field_name,
                is_required=req.is_required,
                component_count=req.component_count,
                description=req.description,
                provider_name=req.provider_name
            )
            for req in requirements
        ]
        
        providers = list(set(req.provider_name for req in requirements if req.provider_name))
        
        return WorkflowCredentialRequirements(
            workflow_id=workflow_id,
            credential_requirements=requirement_specs,
            authentication_complexity=workflow.authentication_complexity or "none",
            total_requirements=len(requirement_specs),
            required_providers=providers
        )
    
    def get_batch_requirements(self, workflow_ids: List[str]) -> Dict[str, WorkflowCredentialRequirements]:
        """Get requirements for multiple workflows efficiently"""
        
        # Batch query workflows
        workflows = self.db.query(Workflow).filter(
            Workflow.id.in_(workflow_ids)
        ).all()
        
        result = {}
        for workflow in workflows:
            if workflow.credential_requirements:
                result[workflow.id] = WorkflowCredentialRequirements(
                    workflow_id=workflow.id,
                    **workflow.credential_requirements
                )
        
        return result
    
    def update_requirements_if_changed(self, workflow_id: str) -> bool:
        """Update requirements if workflow has changed"""
        
        # Get current stored requirements
        stored_requirements = self.get_workflow_requirements(workflow_id)
        
        # Get fresh analysis
        fresh_requirements = self.extract_and_store_requirements(workflow_id)
        
        # Compare if requirements changed
        if not stored_requirements:
            return True
        
        return (stored_requirements.credential_requirements != fresh_requirements.credential_requirements)
    
    def get_marketplace_auth_summary(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """Get authentication summary for marketplace workflows"""
        
        workflows = self.db.query(
            Workflow.id,
            Workflow.name,
            Workflow.authentication_complexity,
            func.json_array_length(Workflow.credential_requirements['credential_requirements']).label('credential_count')
        ).filter(
            Workflow.visibility == 'PUBLIC'
        ).offset(offset).limit(limit).all()
        
        return [
            {
                "workflow_id": w.id,
                "name": w.name,
                "authentication_complexity": w.authentication_complexity or "none",
                "credential_count": w.credential_count or 0
            }
            for w in workflows
        ]
```

**Acceptance Criteria:**
- [ ] Efficiently stores and retrieves credential requirements
- [ ] Supports batch operations for multiple workflows
- [ ] Handles JSON and relational storage patterns
- [ ] Updates requirements when workflows change
- [ ] Provides marketplace summary data
- [ ] Handles edge cases (missing workflows, empty requirements)
- [ ] Database transactions are properly managed

### 1.3 Workflow Service API Enhancements

#### Task 1.3.1: Add Credential Analysis Endpoints
**Service**: workflow-service  
**Priority**: P0 (Blocking)  
**Assignee**: Backend Developer  
**Estimated**: 1 day  

**Implementation Details:**
- **File**: `workflow-service/app/api/workflow_routes.py`

```python
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from app.services.credential_requirement_service import CredentialRequirementService
from app.models.workflow_builder.credential_requirements import WorkflowCredentialRequirements
from app.db.session import get_db
from pydantic import BaseModel

router = APIRouter()

class WorkflowAnalysisRequest(BaseModel):
    workflow_data: Dict[str, Any]

class BatchRequirementsRequest(BaseModel):
    workflow_ids: List[str]

@router.post("/workflows/analyze-credentials", response_model=WorkflowCredentialRequirements)
async def analyze_workflow_credentials(
    request: WorkflowAnalysisRequest,
    db: Session = Depends(get_db)
):
    """Analyze workflow data for credential requirements without storing"""
    try:
        service = CredentialRequirementService(db)
        analyzer = service.analyzer
        
        # Analyze the provided workflow data
        requirements = analyzer.extract_credential_requirements(request.workflow_data)
        return requirements
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to analyze workflow: {str(e)}")

@router.get("/workflows/{workflow_id}/credentials", response_model=WorkflowCredentialRequirements)
async def get_workflow_credential_requirements(
    workflow_id: str,
    refresh: bool = Query(False, description="Force refresh requirements from workflow data"),
    db: Session = Depends(get_db)
):
    """Get credential requirements for a specific workflow"""
    try:
        service = CredentialRequirementService(db)
        
        if refresh:
            # Force refresh from workflow data
            requirements = service.extract_and_store_requirements(workflow_id)
        else:
            # Get stored requirements
            requirements = service.get_workflow_requirements(workflow_id)
            
            if not requirements:
                # First time analysis
                requirements = service.extract_and_store_requirements(workflow_id)
        
        return requirements
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get requirements: {str(e)}")

@router.post("/workflows/batch-credentials")
async def get_batch_credential_requirements(
    request: BatchRequirementsRequest,
    db: Session = Depends(get_db)
):
    """Get credential requirements for multiple workflows"""
    try:
        service = CredentialRequirementService(db)
        requirements = service.get_batch_requirements(request.workflow_ids)
        
        return {
            "workflows": requirements
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get batch requirements: {str(e)}")

@router.get("/marketplace/auth-summary")
async def get_marketplace_auth_summary(
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    complexity: str = Query(None, description="Filter by complexity: none, simple, moderate, complex"),
    db: Session = Depends(get_db)
):
    """Get authentication summary for marketplace workflows"""
    try:
        service = CredentialRequirementService(db)
        summaries = service.get_marketplace_auth_summary(limit, offset)
        
        # Apply complexity filter if specified
        if complexity:
            summaries = [s for s in summaries if s["authentication_complexity"] == complexity]
        
        return {
            "workflows": summaries,
            "total": len(summaries),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get auth summary: {str(e)}")

# Background task for processing existing workflows
@router.post("/admin/backfill-credential-requirements")
async def backfill_credential_requirements(
    batch_size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Admin endpoint to backfill credential requirements for existing workflows"""
    try:
        from app.models.workflow import Workflow
        
        # Get workflows without credential requirements
        workflows = db.query(Workflow).filter(
            Workflow.credential_requirements.is_(None)
        ).limit(batch_size).all()
        
        service = CredentialRequirementService(db)
        processed = 0
        errors = []
        
        for workflow in workflows:
            try:
                service.extract_and_store_requirements(workflow.id)
                processed += 1
            except Exception as e:
                errors.append(f"Workflow {workflow.id}: {str(e)}")
        
        return {
            "processed": processed,
            "errors": errors,
            "remaining": db.query(Workflow).filter(
                Workflow.credential_requirements.is_(None)
            ).count()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backfill failed: {str(e)}")
```

**Acceptance Criteria:**
- [ ] All endpoints return correct response formats
- [ ] Error handling for invalid workflow IDs
- [ ] Batch endpoint handles large request efficiently
- [ ] Admin backfill endpoint works for existing workflows
- [ ] API documentation is auto-generated
- [ ] Performance targets met (<500ms for single workflow)

#### Task 1.3.2: Update Marketplace Workflow Responses
**Service**: workflow-service  
**Priority**: P1 (High)  
**Assignee**: Backend Developer  
**Estimated**: 0.5 days  

**Implementation Details:**
- **File**: `workflow-service/app/services/marketplace_functions.py`

```python
# Add to existing MarketplaceWorkflowResponse
from app.models.workflow_builder.credential_requirements import CredentialRequirementSpec

class EnhancedMarketplaceWorkflowResponse(BaseModel):
    # ... existing fields
    credential_requirements: List[CredentialRequirementSpec] = []
    authentication_complexity: str = "none"
    required_providers: List[str] = []
    credential_count: int = 0

# Update getMarketplaceWorkflows method
def getMarketplaceWorkflows(self, page: int = 1, page_size: int = 20, 
                          search: str = None, category: str = None,
                          auth_complexity: str = None) -> List[EnhancedMarketplaceWorkflowResponse]:
    
    query = self.db.query(Workflow).filter(Workflow.visibility == 'PUBLIC')
    
    # Add authentication complexity filter
    if auth_complexity:
        query = query.filter(Workflow.authentication_complexity == auth_complexity)
    
    # ... existing filters
    
    workflows = query.offset((page - 1) * page_size).limit(page_size).all()
    
    # Enhance responses with credential information
    enhanced_workflows = []
    for workflow in workflows:
        response = EnhancedMarketplaceWorkflowResponse(
            # ... existing fields
            credential_requirements=json.loads(workflow.credential_requirements or '{}').get('credential_requirements', []),
            authentication_complexity=workflow.authentication_complexity or 'none',
            required_providers=json.loads(workflow.credential_requirements or '{}').get('required_providers', []),
            credential_count=len(json.loads(workflow.credential_requirements or '{}').get('credential_requirements', []))
        )
        enhanced_workflows.append(response)
    
    return enhanced_workflows
```

**Acceptance Criteria:**
- [ ] Marketplace responses include authentication data
- [ ] Filtering by authentication complexity works
- [ ] Performance impact is minimal
- [ ] Backward compatibility maintained

---

## Phase 2: API Gateway Development (Weeks 3-4)

### 2.1 New API Endpoints

#### Task 2.1.1: Implement Credential Analysis Endpoints
**Service**: api-gateway  
**Priority**: P0 (Blocking)  
**Assignee**: Backend Developer  
**Estimated**: 1.5 days  

**Implementation Details:**
- **File**: `api-gateway/app/api/routers/credential_routes.py`

```python
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Dict, Any
from app.core.auth_guard import role_required
from app.services.workflow_service import WorkflowServiceClient
from app.services.credential_analysis_service import CredentialAnalysisService
from pydantic import BaseModel

router = APIRouter()

class WorkflowAnalysisRequest(BaseModel):
    workflow_data: Dict[str, Any]

class CredentialRequirement(BaseModel):
    credential_type: str
    field_name: str
    is_required: bool = True

class BatchValidationRequest(BaseModel):
    requirements: List[CredentialRequirement]

@router.post("/credentials/analyze-workflow")
async def analyze_workflow_credentials(
    request: WorkflowAnalysisRequest,
    current_user: dict = Depends(role_required(["user", "admin"]))
):
    """Analyze workflow data for credential requirements"""
    try:
        workflow_client = WorkflowServiceClient()
        analysis_result = await workflow_client.analyze_workflow_credentials(request.workflow_data)
        
        return analysis_result
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Analysis failed: {str(e)}")

@router.post("/credentials/batch-validate")
async def validate_credential_availability(
    request: BatchValidationRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """Validate user's credential availability against requirements"""
    try:
        analysis_service = CredentialAnalysisService()
        coverage_report = await analysis_service.check_user_credential_coverage(
            current_user["user_id"], 
            request.requirements
        )
        
        return coverage_report
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")

@router.get("/credentials/setup-guide")
async def get_credential_setup_guide(
    credential_type: str = Query(..., description="Type of credential to set up"),
    provider: str = Query(None, description="Provider name"),
    current_user: dict = Depends(role_required(["user"]))
):
    """Get setup guide for specific credential type"""
    try:
        analysis_service = CredentialAnalysisService()
        setup_guide = await analysis_service.generate_setup_guide(credential_type, provider)
        
        return setup_guide
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get setup guide: {str(e)}")
```

**Acceptance Criteria:**
- [ ] All endpoints properly authenticated
- [ ] Request/response validation working
- [ ] Error handling comprehensive
- [ ] Integration with workflow-service working
- [ ] API documentation complete

#### Task 2.1.2: Create Credential Analysis Service
**Service**: api-gateway  
**Priority**: P0 (Blocking)  
**Assignee**: Backend Developer  
**Estimated**: 1 day  

**Implementation Details:**
- **File**: `api-gateway/app/services/credential_analysis_service.py`

```python
from typing import List, Dict, Any
from app.services.user_service import UserServiceClient
from app.grpc_.user_pb2 import ListCredentialsRequest
import asyncio

class CredentialAnalysisService:
    
    def __init__(self):
        self.user_service = UserServiceClient()
        
        # Setup guides for different credential types
        self.setup_guides = {
            "api_key": {
                "openai": {
                    "title": "OpenAI API Key Setup",
                    "steps": [
                        "Visit https://platform.openai.com/api-keys",
                        "Sign in to your OpenAI account",
                        "Click 'Create new secret key'",
                        "Copy the key and save it securely",
                        "Add the key to your credentials"
                    ],
                    "estimated_time": 5,
                    "difficulty": "easy"
                },
                "github": {
                    "title": "GitHub Personal Access Token",
                    "steps": [
                        "Go to GitHub Settings > Developer settings > Personal access tokens",
                        "Click 'Generate new token (classic)'",
                        "Select required scopes",
                        "Generate token and copy it",
                        "Add the token to your credentials"
                    ],
                    "estimated_time": 8,
                    "difficulty": "medium"
                }
            },
            "oauth": {
                "google": {
                    "title": "Google OAuth Setup",
                    "steps": [
                        "Click 'Connect Google Account' button",
                        "Sign in to your Google account",
                        "Grant required permissions",
                        "Connection will be saved automatically"
                    ],
                    "estimated_time": 3,
                    "difficulty": "easy"
                }
            }
        }
    
    async def check_user_credential_coverage(self, user_id: str, requirements: List[Dict]) -> Dict:
        """Check which credentials user has vs requirements"""
        try:
            # Get user's existing credentials
            credentials_response = await self.user_service.list_credentials(
                ListCredentialsRequest(owner_id=user_id)
            )
            
            user_credentials = {cred.key_name: cred for cred in credentials_response.credentials}
            
            # Analyze coverage
            total_requirements = len(requirements)
            available_credentials = 0
            detailed_status = []
            
            for req in requirements:
                field_name = req.get("field_name")
                credential_type = req.get("credential_type")
                is_required = req.get("is_required", True)
                
                # Check if user has this credential
                has_credential = field_name in user_credentials
                if has_credential:
                    available_credentials += 1
                
                detailed_status.append({
                    "credential_type": credential_type,
                    "field_name": field_name,
                    "is_required": is_required,
                    "status": "available" if has_credential else "missing",
                    "credential_id": user_credentials[field_name].id if has_credential else None
                })
            
            missing_credentials = total_requirements - available_credentials
            coverage_percentage = (available_credentials / total_requirements) * 100 if total_requirements > 0 else 100
            
            return {
                "coverage_report": {
                    "total_requirements": total_requirements,
                    "available_credentials": available_credentials,
                    "missing_credentials": missing_credentials,
                    "coverage_percentage": round(coverage_percentage, 2)
                },
                "detailed_status": detailed_status
            }
            
        except Exception as e:
            raise Exception(f"Failed to check credential coverage: {str(e)}")
    
    async def generate_setup_guide(self, credential_type: str, provider: str = None) -> Dict:
        """Generate setup guide for credential type and provider"""
        
        guide_key = provider if provider else "generic"
        
        if credential_type in self.setup_guides and guide_key in self.setup_guides[credential_type]:
            guide = self.setup_guides[credential_type][guide_key]
            return {
                "credential_type": credential_type,
                "provider": provider,
                "guide": guide
            }
        
        # Generic guide
        return {
            "credential_type": credential_type,
            "provider": provider,
            "guide": {
                "title": f"Set up {credential_type.replace('_', ' ').title()}",
                "steps": [
                    f"Obtain your {credential_type} from the service provider",
                    "Copy the credential value",
                    "Add it to your credentials with a descriptive name"
                ],
                "estimated_time": 5,
                "difficulty": "medium"
            }
        }
    
    async def suggest_credential_setup_order(self, requirements: List[Dict]) -> List[Dict]:
        """Suggest optimal order for setting up multiple credentials"""
        
        # Sort by difficulty and setup time
        difficulty_order = {"easy": 1, "medium": 2, "hard": 3}
        
        enriched_requirements = []
        for req in requirements:
            credential_type = req.get("credential_type")
            provider = req.get("provider_name")
            
            guide = await self.generate_setup_guide(credential_type, provider)
            setup_info = guide["guide"]
            
            enriched_requirements.append({
                **req,
                "estimated_time": setup_info.get("estimated_time", 5),
                "difficulty": setup_info.get("difficulty", "medium"),
                "setup_guide": guide
            })
        
        # Sort by difficulty, then by time
        enriched_requirements.sort(
            key=lambda x: (
                difficulty_order.get(x["difficulty"], 2),
                x["estimated_time"]
            )
        )
        
        return enriched_requirements
```

**Acceptance Criteria:**
- [ ] Credential coverage calculation accurate
- [ ] Setup guides comprehensive for major providers
- [ ] Optimal setup order suggestions working
- [ ] Error handling for service failures
- [ ] Unit tests for all calculation logic

### 2.2 Enhanced Marketplace APIs

#### Task 2.2.1: Update Marketplace Routes with Authentication Data
**Service**: api-gateway  
**Priority**: P1 (High)  
**Assignee**: Backend Developer  
**Estimated**: 1 day  

**Implementation Details:**
- **File**: `api-gateway/app/api/routers/marketplace_routes.py`

```python
# Add new endpoints and enhance existing ones

@router.get("/marketplace/workflows/{workflow_id}/credentials")
async def get_workflow_credential_requirements(
    workflow_id: str,
    current_user: dict = Depends(role_required(["user"]))
):
    """Get credential requirements for specific workflow"""
    try:
        workflow_client = WorkflowServiceClient()
        requirements = await workflow_client.get_workflow_credential_requirements(workflow_id)
        
        # Check user's credential coverage
        if requirements and requirements.get("credential_requirements"):
            analysis_service = CredentialAnalysisService()
            coverage = await analysis_service.check_user_credential_coverage(
                current_user["user_id"],
                requirements["credential_requirements"]
            )
            requirements["user_coverage"] = coverage
        
        return requirements
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get requirements: {str(e)}")

@router.get("/marketplace/workflows/batch-auth-info")
async def get_batch_authentication_info(
    workflow_ids: str = Query(..., description="Comma-separated workflow IDs"),
    include_user_coverage: bool = Query(False, description="Include user's credential coverage"),
    current_user: dict = Depends(role_required(["user"]))
):
    """Get authentication info for multiple workflows"""
    try:
        workflow_id_list = [id.strip() for id in workflow_ids.split(",")]
        
        workflow_client = WorkflowServiceClient()
        auth_info = await workflow_client.get_batch_credential_requirements(workflow_id_list)
        
        # Optionally include user coverage
        if include_user_coverage:
            analysis_service = CredentialAnalysisService()
            
            for workflow_id, requirements in auth_info["workflows"].items():
                if requirements.get("credential_requirements"):
                    coverage = await analysis_service.check_user_credential_coverage(
                        current_user["user_id"],
                        requirements["credential_requirements"]
                    )
                    requirements["user_coverage"] = coverage
        
        return auth_info
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get batch auth info: {str(e)}")

# Enhance existing getMarketplaceWorkflows endpoint
@router.get("/marketplace/workflows")
async def get_marketplace_workflows(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: str = Query(None),
    category: str = Query(None),
    tags: str = Query(None),
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    # New authentication filters
    auth_complexity: str = Query(None, description="Filter by complexity: none, simple, moderate, complex"),
    provider: str = Query(None, description="Filter by required provider"),
    max_credentials: int = Query(None, description="Maximum number of credentials"),
    current_user: dict = Depends(role_required(["user"]))
):
    """Enhanced marketplace workflows with authentication filtering"""
    try:
        workflow_service = WorkflowService()
        
        # Build filters including authentication filters
        filters = {
            "page": page,
            "page_size": page_size,
            "search": search,
            "category": category,
            "tags": tags,
            "sort_by": sort_by,
            "sort_order": sort_order,
            "auth_complexity": auth_complexity,
            "provider": provider,
            "max_credentials": max_credentials
        }
        
        workflows = await workflow_service.get_marketplace_workflows_enhanced(filters)
        
        return {
            "workflows": workflows,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": len(workflows)  # Would be enhanced with actual total
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get workflows: {str(e)}")
```

**Acceptance Criteria:**
- [ ] Authentication filters work correctly
- [ ] User coverage calculation integrated
- [ ] Batch endpoints perform efficiently
- [ ] Backward compatibility maintained
- [ ] API response format consistent

---

## Phase 3: Frontend Implementation (Weeks 5-6)

### 3.1 Authentication Requirements Components

#### Task 3.1.1: Create Authentication Requirements Screen
**Service**: workflow-builder-app  
**Priority**: P0 (Blocking)  
**Assignee**: Frontend Developer  
**Estimated**: 2 days  

**Implementation Details:**
- **File**: `workflow-builder-app/src/components/marketplace/AuthenticationRequirementsScreen.tsx`

```typescript
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle2, 
  XCircle, 
  Clock, 
  Shield, 
  Key,
  ExternalLink,
  ChevronRight 
} from 'lucide-react';

interface AuthRequirement {
  credential_type: string;
  field_name: string;
  is_required: boolean;
  component_count: number;
  description: string;
  provider_name?: string;
  status: 'available' | 'missing';
  credential_id?: string;
  setup_guide?: SetupGuide;
}

interface SetupGuide {
  title: string;
  steps: string[];
  estimated_time: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface CoverageReport {
  total_requirements: number;
  available_credentials: number;
  missing_credentials: number;
  coverage_percentage: number;
}

interface AuthenticationRequirementsScreenProps {
  workflowId: string;
  workflowName: string;
  requirements: AuthRequirement[];
  coverageReport: CoverageReport;
  onProceed: () => void;
  onSetupCredentials: (requirements: AuthRequirement[]) => void;
  onCancel: () => void;
  loading?: boolean;
}

const AuthenticationRequirementsScreen: React.FC<AuthenticationRequirementsScreenProps> = ({
  workflowId,
  workflowName,
  requirements,
  coverageReport,
  onProceed,
  onSetupCredentials,
  onCancel,
  loading = false
}) => {
  const [selectedRequirements, setSelectedRequirements] = useState<string[]>([]);
  
  const missingRequirements = requirements.filter(req => req.status === 'missing');
  const requiredMissing = missingRequirements.filter(req => req.is_required);
  
  const canProceed = requiredMissing.length === 0;
  const totalSetupTime = missingRequirements.reduce((total, req) => 
    total + (req.setup_guide?.estimated_time || 5), 0
  );

  const getComplexityColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProviderIcon = (provider: string) => {
    const icons: Record<string, string> = {
      openai: '🤖',
      github: '🐱',
      google: '🌐',
      slack: '💬',
      discord: '🎮'
    };
    return icons[provider] || '🔑';
  };

  const handleSetupSelected = () => {
    const selectedReqs = missingRequirements.filter(req => 
      selectedRequirements.includes(req.field_name)
    );
    onSetupCredentials(selectedReqs);
  };

  const handleSetupAll = () => {
    onSetupCredentials(missingRequirements);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Authentication Requirements</h1>
        <p className="text-muted-foreground">
          Setup required credentials to use <strong>{workflowName}</strong>
        </p>
      </div>

      {/* Coverage Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Authentication Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Setup Progress</span>
            <span className={`font-semibold ${getComplexityColor(coverageReport.coverage_percentage)}`}>
              {coverageReport.available_credentials} of {coverageReport.total_requirements} credentials ready
            </span>
          </div>
          
          <Progress 
            value={coverageReport.coverage_percentage} 
            className="h-3"
          />
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="space-y-1">
              <div className="text-2xl font-bold text-green-600">
                {coverageReport.available_credentials}
              </div>
              <div className="text-sm text-muted-foreground">Ready</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-red-600">
                {coverageReport.missing_credentials}
              </div>
              <div className="text-sm text-muted-foreground">Missing</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-blue-600">
                {totalSetupTime}m
              </div>
              <div className="text-sm text-muted-foreground">Est. Setup</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Requirements List */}
      <Card>
        <CardHeader>
          <CardTitle>Required Credentials</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {requirements.map((req, index) => (
              <div 
                key={index}
                className={`border rounded-lg p-4 ${
                  req.status === 'missing' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">
                        {getProviderIcon(req.provider_name || 'generic')}
                      </span>
                      <h3 className="font-semibold">{req.field_name.replace(/_/g, ' ').toUpperCase()}</h3>
                      {req.is_required && (
                        <Badge variant="destructive" size="sm">Required</Badge>
                      )}
                      {req.component_count > 1 && (
                        <Badge variant="secondary" size="sm">
                          Used in {req.component_count} components
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-muted-foreground">
                      {req.description}
                    </p>
                    
                    {req.setup_guide && req.status === 'missing' && (
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {req.setup_guide.estimated_time} min
                        </div>
                        <div className="flex items-center gap-1">
                          <Badge variant="outline" size="sm">
                            {req.setup_guide.difficulty}
                          </Badge>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {req.status === 'available' ? (
                      <CheckCircle2 className="h-6 w-6 text-green-600" />
                    ) : (
                      <>
                        <input
                          type="checkbox"
                          checked={selectedRequirements.includes(req.field_name)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedRequirements([...selectedRequirements, req.field_name]);
                            } else {
                              setSelectedRequirements(selectedRequirements.filter(
                                name => name !== req.field_name
                              ));
                            }
                          }}
                          className="h-4 w-4"
                        />
                        <XCircle className="h-6 w-6 text-red-600" />
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-between gap-4">
        <Button variant="outline" onClick={onCancel}>
          Cancel Import
        </Button>
        
        <div className="flex items-center gap-3">
          {missingRequirements.length > 0 && (
            <>
              {selectedRequirements.length > 0 && (
                <Button 
                  variant="outline" 
                  onClick={handleSetupSelected}
                  className="flex items-center gap-2"
                >
                  <Key className="h-4 w-4" />
                  Setup Selected ({selectedRequirements.length})
                </Button>
              )}
              
              <Button 
                onClick={handleSetupAll}
                className="flex items-center gap-2"
              >
                <Key className="h-4 w-4" />
                Setup All Missing
                <span className="bg-white/20 px-2 py-1 rounded text-xs">
                  {totalSetupTime}m
                </span>
              </Button>
            </>
          )}
          
          <Button 
            onClick={onProceed}
            disabled={!canProceed || loading}
            className="flex items-center gap-2 min-w-[140px]"
          >
            {loading ? (
              "Importing..."
            ) : (
              <>
                Import Workflow
                <ChevronRight className="h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Warning for missing required credentials */}
      {requiredMissing.length > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <XCircle className="h-5 w-5 text-amber-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-amber-800">
                  Missing Required Credentials
                </h4>
                <p className="text-sm text-amber-700 mt-1">
                  You need to set up {requiredMissing.length} required credential(s) before you can use this workflow. 
                  The workflow may not function properly without these credentials.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AuthenticationRequirementsScreen;
```

**Acceptance Criteria:**
- [ ] Displays all credential requirements clearly
- [ ] Shows setup progress and coverage percentage
- [ ] Handles missing vs available credentials appropriately
- [ ] Provides estimated setup time
- [ ] Supports bulk credential setup
- [ ] Responsive design works on mobile
- [ ] Accessibility features implemented

#### Task 3.1.2: Create Credential Setup Wizard
**Service**: workflow-builder-app  
**Priority**: P0 (Blocking)  
**Assignee**: Frontend Developer  
**Estimated**: 2 days  

**Implementation Details:**
- **File**: `workflow-builder-app/src/components/marketplace/CredentialSetupWizard.tsx`

```typescript
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle2, 
  ChevronLeft, 
  ChevronRight, 
  ExternalLink,
  Copy,
  Eye,
  EyeOff,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SetupStep {
  requirement: AuthRequirement;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  credential_id?: string;
  error_message?: string;
}

interface CredentialSetupWizardProps {
  requirements: AuthRequirement[];
  onComplete: (credentialIds: string[]) => void;
  onCancel: () => void;
}

const CredentialSetupWizard: React.FC<CredentialSetupWizardProps> = ({
  requirements,
  onComplete,
  onCancel
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [setupSteps, setSetupSteps] = useState<SetupStep[]>(
    requirements.map(req => ({ requirement: req, status: 'pending' }))
  );
  const [credentialValue, setCredentialValue] = useState('');
  const [credentialName, setCredentialName] = useState('');
  const [credentialDescription, setCredentialDescription] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const { toast } = useToast();
  const totalSteps = requirements.length;
  const currentRequirement = requirements[currentStep];
  const isLastStep = currentStep === totalSteps - 1;
  const completedSteps = setupSteps.filter(step => step.status === 'completed').length;

  // Initialize credential name and description
  useEffect(() => {
    if (currentRequirement) {
      setCredentialName(currentRequirement.field_name.replace(/_/g, ' '));
      setCredentialDescription(currentRequirement.description);
      setCredentialValue('');
    }
  }, [currentStep, currentRequirement]);

  const handleNext = async () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete wizard
      const credentialIds = setupSteps
        .filter(step => step.status === 'completed')
        .map(step => step.credential_id!)
        .filter(Boolean);
      onComplete(credentialIds);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    // Mark current step as skipped and move to next
    const updatedSteps = [...setupSteps];
    updatedSteps[currentStep].status = 'pending';
    setSetupSteps(updatedSteps);
    
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleNext();
    }
  };

  const handleCreateCredential = async () => {
    if (!credentialValue.trim()) {
      toast({
        title: "Error",
        description: "Please enter a credential value",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    
    try {
      // Call API to create credential
      const response = await fetch('/api/v1/credentials', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          key_name: credentialName,
          value: credentialValue,
          description: credentialDescription
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create credential');
      }

      const result = await response.json();
      
      // Update step status
      const updatedSteps = [...setupSteps];
      updatedSteps[currentStep] = {
        ...updatedSteps[currentStep],
        status: 'completed',
        credential_id: result.id
      };
      setSetupSteps(updatedSteps);

      toast({
        title: "Success",
        description: "Credential created successfully",
      });

      // Move to next step
      setTimeout(() => {
        if (currentStep < totalSteps - 1) {
          setCurrentStep(currentStep + 1);
        } else {
          handleNext();
        }
      }, 1000);

    } catch (error) {
      const updatedSteps = [...setupSteps];
      updatedSteps[currentStep] = {
        ...updatedSteps[currentStep],
        status: 'error',
        error_message: error.message
      };
      setSetupSteps(updatedSteps);

      toast({
        title: "Error",
        description: "Failed to create credential",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Text copied to clipboard",
    });
  };

  const renderSetupGuide = () => {
    const guide = currentRequirement.setup_guide;
    if (!guide) return null;

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Setup Guide: {guide.title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>⏱️ {guide.estimated_time} minutes</span>
            <span>📊 {guide.difficulty.toUpperCase()}</span>
          </div>
          
          <div className="space-y-2">
            {guide.steps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold mt-0.5">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="text-sm">{step}</p>
                  {step.includes('http') && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => window.open(step.match(/https?:\/\/[^\s]+/)?.[0], '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open Link
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold">Credential Setup Wizard</h1>
        <p className="text-muted-foreground">
          Step {currentStep + 1} of {totalSteps}: Setting up {currentRequirement?.field_name}
        </p>
      </div>

      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>{completedSteps} of {totalSteps} completed</span>
          <span>{Math.round((completedSteps / totalSteps) * 100)}%</span>
        </div>
        <Progress value={(completedSteps / totalSteps) * 100} className="h-2" />
      </div>

      {/* Step Indicator */}
      <div className="flex items-center justify-center space-x-2">
        {setupSteps.map((step, index) => (
          <div
            key={index}
            className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold ${
              step.status === 'completed' 
                ? 'bg-green-500 text-white' 
                : step.status === 'error'
                ? 'bg-red-500 text-white'
                : index === currentStep
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-600'
            }`}
          >
            {step.status === 'completed' ? (
              <CheckCircle2 className="h-4 w-4" />
            ) : step.status === 'error' ? (
              <AlertCircle className="h-4 w-4" />
            ) : (
              index + 1
            )}
          </div>
        ))}
      </div>

      {/* Setup Guide */}
      {renderSetupGuide()}

      {/* Credential Form */}
      <Card>
        <CardHeader>
          <CardTitle>Enter Credential Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="credentialName">Credential Name</Label>
            <Input
              id="credentialName"
              value={credentialName}
              onChange={(e) => setCredentialName(e.target.value)}
              placeholder="e.g., OpenAI API Key"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="credentialValue">
              {currentRequirement?.credential_type === 'password' ? 'Password' : 'Credential Value'}
            </Label>
            <div className="relative">
              <Input
                id="credentialValue"
                type={showPassword ? 'text' : 'password'}
                value={credentialValue}
                onChange={(e) => setCredentialValue(e.target.value)}
                placeholder={`Enter your ${currentRequirement?.credential_type || 'credential'}`}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="credentialDescription">Description (Optional)</Label>
            <Textarea
              id="credentialDescription"
              value={credentialDescription}
              onChange={(e) => setCredentialDescription(e.target.value)}
              placeholder="Add a description for this credential"
              rows={2}
            />
          </div>

          {setupSteps[currentStep]?.status === 'error' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-red-800">
                <AlertCircle className="h-4 w-4" />
                <span className="font-semibold">Error</span>
              </div>
              <p className="text-red-700 text-sm mt-1">
                {setupSteps[currentStep].error_message}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          {currentStep > 0 && (
            <Button variant="outline" onClick={handlePrevious}>
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
          )}
        </div>

        <div className="flex gap-2">
          {!currentRequirement?.is_required && (
            <Button variant="outline" onClick={handleSkip}>
              Skip Optional
            </Button>
          )}
          
          <Button 
            onClick={handleCreateCredential}
            disabled={loading || !credentialValue.trim()}
          >
            {loading ? (
              "Creating..."
            ) : isLastStep ? (
              "Complete Setup"
            ) : (
              <>
                Create & Continue
                <ChevronRight className="h-4 w-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CredentialSetupWizard;
```

**Acceptance Criteria:**
- [ ] Step-by-step credential creation process
- [ ] Setup guides with external links
- [ ] Progress tracking and step indicators
- [ ] Error handling and validation
- [ ] Support for different credential types
- [ ] Skip option for optional credentials
- [ ] Secure password input with show/hide toggle

### 3.2 Marketplace Enhancement

#### Task 3.2.1: Enhance Marketplace Workflow Cards
**Service**: workflow-builder-app  
**Priority**: P1 (High)  
**Assignee**: Frontend Developer  
**Estimated**: 1 day  

**Implementation Details:**
- **File**: `workflow-builder-app/src/components/marketplace/EnhancedWorkflowCard.tsx`

```typescript
import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Star, 
  Download, 
  Shield, 
  Key,
  Clock,
  Users
} from 'lucide-react';

interface EnhancedWorkflowCardProps {
  workflow: {
    id: string;
    name: string;
    description: string;
    image_url?: string;
    category: string;
    use_count: number;
    average_rating: number;
    credential_requirements: any[];
    authentication_complexity: string;
    required_providers: string[];
    credential_count: number;
    owner_name: string;
  };
  onImport: (workflowId: string) => void;
  onViewDetails: (workflowId: string) => void;
}

const EnhancedWorkflowCard: React.FC<EnhancedWorkflowCardProps> = ({
  workflow,
  onImport,
  onViewDetails
}) => {
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'none': return 'bg-green-100 text-green-800';
      case 'simple': return 'bg-blue-100 text-blue-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'complex': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProviderIcon = (provider: string) => {
    const icons: Record<string, string> = {
      openai: '🤖',
      github: '🐱',
      google: '🌐',
      slack: '💬',
      discord: '🎮'
    };
    return icons[provider] || '🔑';
  };

  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-lg group-hover:text-blue-600 transition-colors">
              {workflow.name}
            </h3>
            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
              {workflow.description}
            </p>
          </div>
          {workflow.image_url && (
            <img 
              src={workflow.image_url} 
              alt={workflow.name}
              className="w-12 h-12 rounded-lg object-cover ml-3"
            />
          )}
        </div>

        {/* Authentication Info */}
        <div className="flex items-center gap-2 mt-3">
          <Badge 
            variant="secondary" 
            className={`${getComplexityColor(workflow.authentication_complexity)} text-xs`}
          >
            <Shield className="h-3 w-3 mr-1" />
            {workflow.authentication_complexity.charAt(0).toUpperCase() + workflow.authentication_complexity.slice(1)} Auth
          </Badge>
          
          {workflow.credential_count > 0 && (
            <Badge variant="outline" className="text-xs">
              <Key className="h-3 w-3 mr-1" />
              {workflow.credential_count} credential{workflow.credential_count !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>

        {/* Required Providers */}
        {workflow.required_providers.length > 0 && (
          <div className="flex items-center gap-1 mt-2">
            <span className="text-xs text-muted-foreground">Requires:</span>
            <div className="flex gap-1">
              {workflow.required_providers.slice(0, 3).map((provider, index) => (
                <span key={index} className="text-sm" title={provider}>
                  {getProviderIcon(provider)}
                </span>
              ))}
              {workflow.required_providers.length > 3 && (
                <span className="text-xs text-muted-foreground">
                  +{workflow.required_providers.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span>{workflow.average_rating.toFixed(1)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Download className="h-4 w-4" />
              <span>{workflow.use_count.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              <span>{workflow.owner_name}</span>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            {workflow.category}
          </Badge>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={(e) => {
              e.stopPropagation();
              onViewDetails(workflow.id);
            }}
            className="flex-1"
          >
            View Details
          </Button>
          <Button 
            size="sm" 
            onClick={(e) => {
              e.stopPropagation();
              onImport(workflow.id);
            }}
            className="flex-1"
          >
            Import
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedWorkflowCard;
```

**Acceptance Criteria:**
- [ ] Authentication complexity clearly displayed
- [ ] Required providers shown with icons
- [ ] Credential count visible
- [ ] Hover effects and visual feedback
- [ ] Responsive design
- [ ] Click handlers for import and details

#### Task 3.2.2: Add Authentication Filters to Marketplace
**Service**: workflow-builder-app  
**Priority**: P1 (High)  
**Assignee**: Frontend Developer  
**Estimated**: 1 day  

**Implementation Details:**
- **File**: `workflow-builder-app/src/components/marketplace/AuthenticationFilterPanel.tsx`

```typescript
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Shield, Key, Filter } from 'lucide-react';

interface AuthenticationFilters {
  complexity: string[];
  providers: string[];
  maxCredentials: number;
  requiresAuth: boolean | null;
}

interface AuthenticationFilterPanelProps {
  filters: AuthenticationFilters;
  onFiltersChange: (filters: AuthenticationFilters) => void;
  availableProviders: string[];
  className?: string;
}

const AuthenticationFilterPanel: React.FC<AuthenticationFilterPanelProps> = ({
  filters,
  onFiltersChange,
  availableProviders,
  className = ""
}) => {
  const complexityOptions = [
    { value: 'none', label: 'No Authentication', color: 'bg-green-100 text-green-800' },
    { value: 'simple', label: 'Simple (1-2 credentials)', color: 'bg-blue-100 text-blue-800' },
    { value: 'moderate', label: 'Moderate (3-5 credentials)', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'complex', label: 'Complex (5+ credentials)', color: 'bg-red-100 text-red-800' }
  ];

  const providerDisplayNames: Record<string, string> = {
    openai: 'OpenAI',
    github: 'GitHub',
    google: 'Google',
    slack: 'Slack',
    discord: 'Discord',
    anthropic: 'Anthropic',
    telegram: 'Telegram'
  };

  const handleComplexityChange = (complexity: string, checked: boolean) => {
    const newComplexity = checked 
      ? [...filters.complexity, complexity]
      : filters.complexity.filter(c => c !== complexity);
    
    onFiltersChange({
      ...filters,
      complexity: newComplexity
    });
  };

  const handleProviderChange = (provider: string, checked: boolean) => {
    const newProviders = checked
      ? [...filters.providers, provider]
      : filters.providers.filter(p => p !== provider);
    
    onFiltersChange({
      ...filters,
      providers: newProviders
    });
  };

  const handleMaxCredentialsChange = (value: number[]) => {
    onFiltersChange({
      ...filters,
      maxCredentials: value[0]
    });
  };

  const handleRequiresAuthChange = (requiresAuth: boolean | null) => {
    onFiltersChange({
      ...filters,
      requiresAuth
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      complexity: [],
      providers: [],
      maxCredentials: 10,
      requiresAuth: null
    });
  };

  const hasActiveFilters = 
    filters.complexity.length > 0 || 
    filters.providers.length > 0 || 
    filters.maxCredentials < 10 || 
    filters.requiresAuth !== null;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Authentication Filters
          </CardTitle>
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              Clear all
            </button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Authentication Requirement */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Authentication Requirement</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="no-auth"
                checked={filters.requiresAuth === false}
                onCheckedChange={(checked) => 
                  handleRequiresAuthChange(checked ? false : null)
                }
              />
              <Label htmlFor="no-auth" className="text-sm">
                No authentication required
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="requires-auth"
                checked={filters.requiresAuth === true}
                onCheckedChange={(checked) => 
                  handleRequiresAuthChange(checked ? true : null)
                }
              />
              <Label htmlFor="requires-auth" className="text-sm">
                Requires authentication
              </Label>
            </div>
          </div>
        </div>

        {/* Complexity Level */}
        <div className="space-y-3">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Complexity Level
          </Label>
          <div className="space-y-2">
            {complexityOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`complexity-${option.value}`}
                  checked={filters.complexity.includes(option.value)}
                  onCheckedChange={(checked) => 
                    handleComplexityChange(option.value, checked)
                  }
                />
                <Label 
                  htmlFor={`complexity-${option.value}`} 
                  className="text-sm flex-1"
                >
                  <Badge 
                    variant="secondary" 
                    className={`${option.color} text-xs mr-2`}
                  >
                    {option.label.split(' ')[0]}
                  </Badge>
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Maximum Credentials */}
        <div className="space-y-3">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Key className="h-4 w-4" />
            Maximum Credentials: {filters.maxCredentials}
          </Label>
          <Slider
            value={[filters.maxCredentials]}
            onValueChange={handleMaxCredentialsChange}
            max={10}
            min={0}
            step={1}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>0</span>
            <span>10+</span>
          </div>
        </div>

        {/* Required Providers */}
        {availableProviders.length > 0 && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Required Providers</Label>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {availableProviders.map((provider) => (
                <div key={provider} className="flex items-center space-x-2">
                  <Checkbox
                    id={`provider-${provider}`}
                    checked={filters.providers.includes(provider)}
                    onCheckedChange={(checked) => 
                      handleProviderChange(provider, checked)
                    }
                  />
                  <Label 
                    htmlFor={`provider-${provider}`} 
                    className="text-sm flex items-center gap-2"
                  >
                    <span className="text-base">
                      {provider === 'openai' ? '🤖' : 
                       provider === 'github' ? '🐱' :
                       provider === 'google' ? '🌐' :
                       provider === 'slack' ? '💬' :
                       provider === 'discord' ? '🎮' : '🔑'}
                    </span>
                    {providerDisplayNames[provider] || provider}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-3 border-t">
            <Label className="text-xs font-medium text-muted-foreground">
              Active Filters ({[
                ...filters.complexity,
                ...filters.providers,
                ...(filters.maxCredentials < 10 ? ['max credentials'] : []),
                ...(filters.requiresAuth !== null ? ['auth requirement'] : [])
              ].length})
            </Label>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AuthenticationFilterPanel;
```

**Acceptance Criteria:**
- [ ] All authentication-related filters functional
- [ ] Clear visual feedback for active filters
- [ ] Responsive design for different screen sizes
- [ ] Filter state properly managed
- [ ] Integration with marketplace API

---

## Phase 4: Integration & Testing (Weeks 7-8)

### 4.1 Integration Tasks

#### Task 4.1.1: Integrate Frontend with Backend APIs
**Service**: workflow-builder-app  
**Priority**: P0 (Blocking)  
**Assignee**: Full-stack Developer  
**Estimated**: 1.5 days  

**Implementation Details:**
- **File**: `workflow-builder-app/src/lib/authentication-api.ts`

```typescript
import { authenticatedApi } from './authenticatedApi';

export interface CredentialRequirement {
  credential_type: string;
  field_name: string;
  is_required: boolean;
  component_count: number;
  description: string;
  provider_name?: string;
}

export interface WorkflowCredentialRequirements {
  workflow_id: string;
  credential_requirements: CredentialRequirement[];
  authentication_complexity: string;
  total_requirements: number;
  required_providers: string[];
}

export interface CoverageReport {
  total_requirements: number;
  available_credentials: number;
  missing_credentials: number;
  coverage_percentage: number;
}

export interface DetailedCoverageReport extends CoverageReport {
  detailed_status: Array<{
    credential_type: string;
    field_name: string;
    is_required: boolean;
    status: 'available' | 'missing';
    credential_id?: string;
  }>;
}

class AuthenticationAPI {
  
  async analyzeWorkflowCredentials(workflowData: any): Promise<WorkflowCredentialRequirements> {
    const response = await authenticatedApi.post('/credentials/analyze-workflow', {
      workflow_data: workflowData
    });
    return response.data;
  }

  async getWorkflowCredentialRequirements(workflowId: string, refresh = false): Promise<WorkflowCredentialRequirements> {
    const response = await authenticatedApi.get(
      `/marketplace/workflows/${workflowId}/credentials`,
      { params: { refresh } }
    );
    return response.data;
  }

  async validateCredentialAvailability(requirements: CredentialRequirement[]): Promise<DetailedCoverageReport> {
    const response = await authenticatedApi.post('/credentials/batch-validate', {
      requirements
    });
    return response.data;
  }

  async getBatchAuthenticationInfo(workflowIds: string[], includeUserCoverage = false): Promise<{
    workflows: Record<string, WorkflowCredentialRequirements & { user_coverage?: DetailedCoverageReport }>
  }> {
    const response = await authenticatedApi.get('/marketplace/workflows/batch-auth-info', {
      params: {
        workflow_ids: workflowIds.join(','),
        include_user_coverage: includeUserCoverage
      }
    });
    return response.data;
  }

  async getCredentialSetupGuide(credentialType: string, provider?: string): Promise<{
    credential_type: string;
    provider?: string;
    guide: {
      title: string;
      steps: string[];
      estimated_time: number;
      difficulty: 'easy' | 'medium' | 'hard';
    };
  }> {
    const response = await authenticatedApi.get('/credentials/setup-guide', {
      params: { credential_type: credentialType, provider }
    });
    return response.data;
  }

  async getMarketplaceWorkflows(params: {
    page?: number;
    page_size?: number;
    search?: string;
    category?: string;
    auth_complexity?: string;
    provider?: string;
    max_credentials?: number;
  } = {}): Promise<{
    workflows: any[];
    pagination: any;
  }> {
    const response = await authenticatedApi.get('/marketplace/workflows', { params });
    return response.data;
  }
}

export const authenticationAPI = new AuthenticationAPI();
```

**Acceptance Criteria:**
- [ ] All API endpoints integrated correctly
- [ ] Error handling for network failures
- [ ] TypeScript types match backend schemas
- [ ] Authentication headers handled properly
- [ ] Response caching where appropriate

#### Task 4.1.2: Update Workflow Import Flow
**Service**: workflow-builder-app  
**Priority**: P0 (Blocking)  
**Assignee**: Full-stack Developer  
**Estimated**: 1 day  

**Implementation Details:**
- **File**: `workflow-builder-app/src/components/marketplace/WorkflowImportFlow.tsx`

```typescript
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { authenticationAPI } from '@/lib/authentication-api';
import AuthenticationRequirementsScreen from './AuthenticationRequirementsScreen';
import CredentialSetupWizard from './CredentialSetupWizard';
import { useToast } from '@/hooks/use-toast';

interface WorkflowImportFlowProps {
  workflowId: string;
  workflowName: string;
  onCancel: () => void;
}

type ImportStep = 'loading' | 'requirements' | 'setup' | 'importing' | 'complete';

const WorkflowImportFlow: React.FC<WorkflowImportFlowProps> = ({
  workflowId,
  workflowName,
  onCancel
}) => {
  const [currentStep, setCurrentStep] = useState<ImportStep>('loading');
  const [requirements, setRequirements] = useState<any>(null);
  const [coverageReport, setCoverageReport] = useState<any>(null);
  const [missingRequirements, setMissingRequirements] = useState<any[]>([]);
  const [error, setError] = useState<string>('');
  
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    loadRequirements();
  }, [workflowId]);

  const loadRequirements = async () => {
    try {
      setCurrentStep('loading');
      
      // Get workflow credential requirements
      const workflowRequirements = await authenticationAPI.getWorkflowCredentialRequirements(workflowId);
      setRequirements(workflowRequirements);

      // Check user's credential coverage
      if (workflowRequirements.credential_requirements.length > 0) {
        const coverage = await authenticationAPI.validateCredentialAvailability(
          workflowRequirements.credential_requirements
        );
        setCoverageReport(coverage);

        // Identify missing requirements
        const missing = coverage.detailed_status
          .filter(status => status.status === 'missing')
          .map(status => {
            const requirement = workflowRequirements.credential_requirements.find(
              req => req.field_name === status.field_name
            );
            return {
              ...requirement,
              status: status.status
            };
          });

        setMissingRequirements(missing);
      }

      setCurrentStep('requirements');
      
    } catch (err) {
      setError('Failed to load authentication requirements');
      console.error('Error loading requirements:', err);
    }
  };

  const handleProceedWithImport = async () => {
    try {
      setCurrentStep('importing');

      // Import workflow
      const response = await fetch('/api/v1/marketplace/use', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          item_id: workflowId,
          item_type: 'workflow'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to import workflow');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: `Workflow "${workflowName}" imported successfully`,
      });

      // Navigate to imported workflow
      router.push(`/workflows/${result.workflow_id}`);
      
    } catch (err) {
      setError('Failed to import workflow');
      setCurrentStep('requirements');
      toast({
        title: "Error",
        description: "Failed to import workflow",
        variant: "destructive"
      });
    }
  };

  const handleSetupCredentials = async (selectedRequirements: any[]) => {
    // Add setup guides to requirements
    const enrichedRequirements = await Promise.all(
      selectedRequirements.map(async (req) => {
        try {
          const guide = await authenticationAPI.getCredentialSetupGuide(
            req.credential_type,
            req.provider_name
          );
          return {
            ...req,
            setup_guide: guide.guide
          };
        } catch (err) {
          console.error('Failed to get setup guide:', err);
          return req;
        }
      })
    );

    setMissingRequirements(enrichedRequirements);
    setCurrentStep('setup');
  };

  const handleSetupComplete = async (credentialIds: string[]) => {
    toast({
      title: "Success",
      description: `${credentialIds.length} credential(s) created successfully`,
    });

    // Refresh requirements to update coverage
    await loadRequirements();
  };

  const handleSetupCancel = () => {
    setCurrentStep('requirements');
  };

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6 text-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
          <p className="text-red-700">{error}</p>
          <div className="mt-4 flex gap-2 justify-center">
            <button
              onClick={loadRequirements}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Retry
            </button>
            <button
              onClick={onCancel}
              className="px-4 py-2 border border-red-300 text-red-700 rounded hover:bg-red-50"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (currentStep === 'loading') {
    return (
      <div className="max-w-2xl mx-auto p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>Loading authentication requirements...</p>
      </div>
    );
  }

  if (currentStep === 'importing') {
    return (
      <div className="max-w-2xl mx-auto p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
        <p>Importing workflow...</p>
      </div>
    );
  }

  if (currentStep === 'setup') {
    return (
      <CredentialSetupWizard
        requirements={missingRequirements}
        onComplete={handleSetupComplete}
        onCancel={handleSetupCancel}
      />
    );
  }

  if (currentStep === 'requirements' && requirements && coverageReport) {
    // Enhance requirements with status
    const enhancedRequirements = requirements.credential_requirements.map((req: any) => {
      const status = coverageReport.detailed_status.find(
        (s: any) => s.field_name === req.field_name
      );
      return {
        ...req,
        status: status?.status || 'missing',
        credential_id: status?.credential_id
      };
    });

    return (
      <AuthenticationRequirementsScreen
        workflowId={workflowId}
        workflowName={workflowName}
        requirements={enhancedRequirements}
        coverageReport={coverageReport}
        onProceed={handleProceedWithImport}
        onSetupCredentials={handleSetupCredentials}
        onCancel={onCancel}
        loading={currentStep === 'importing'}
      />
    );
  }

  return null;
};

export default WorkflowImportFlow;
```

**Acceptance Criteria:**
- [ ] Complete import flow with authentication steps
- [ ] Error handling for each step
- [ ] Progress indication throughout flow
- [ ] Proper navigation between steps
- [ ] Integration with existing workflow import API

### 4.2 Testing Tasks

#### Task 4.2.1: Write Comprehensive Unit Tests
**Services**: All  
**Priority**: P1 (High)  
**Assignee**: All Developers  
**Estimated**: 2 days  

**Testing Coverage Requirements:**
- [ ] Backend credential analysis logic (80% coverage)
- [ ] API endpoint validation and error handling
- [ ] Frontend component rendering and interactions
- [ ] API integration layer
- [ ] Database operations and migrations

#### Task 4.2.2: End-to-End Testing
**Services**: All  
**Priority**: P1 (High)  
**Assignee**: QA/Full-stack Developer  
**Estimated**: 1 day  

**Test Scenarios:**
- [ ] Complete workflow import flow with authentication requirements
- [ ] Credential setup wizard functionality
- [ ] Marketplace filtering by authentication requirements
- [ ] Batch API performance with multiple workflows
- [ ] Error handling for invalid credentials

### 4.3 Performance Optimization

#### Task 4.3.1: Database Performance Optimization
**Service**: workflow-service  
**Priority**: P1 (High)  
**Assignee**: Backend Developer  
**Estimated**: 1 day  

**Implementation Details:**
```sql
-- Additional performance indexes
CREATE INDEX CONCURRENTLY idx_workflows_marketplace_auth 
ON workflows (visibility, authentication_complexity) 
WHERE visibility = 'PUBLIC';

CREATE INDEX CONCURRENTLY idx_wcr_provider_required 
ON workflow_credential_requirements (provider_name, is_required);

-- Materialized view for marketplace queries
CREATE MATERIALIZED VIEW marketplace_auth_summary AS
SELECT 
    w.id,
    w.name,
    w.authentication_complexity,
    w.category,
    COUNT(wcr.id) as credential_count,
    ARRAY_AGG(DISTINCT wcr.provider_name) FILTER (WHERE wcr.provider_name IS NOT NULL) as providers,
    w.use_count,
    w.average_rating,
    w.created_at
FROM workflows w
LEFT JOIN workflow_credential_requirements wcr ON w.id = wcr.workflow_id
WHERE w.visibility = 'PUBLIC'
GROUP BY w.id, w.name, w.authentication_complexity, w.category, w.use_count, w.average_rating, w.created_at;

-- Refresh schedule
CREATE OR REPLACE FUNCTION refresh_marketplace_auth_summary()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY marketplace_auth_summary;
END;
$$ LANGUAGE plpgsql;
```

**Acceptance Criteria:**
- [ ] Query response times under 500ms
- [ ] Materialized view refresh process
- [ ] Index usage verified in query plans
- [ ] Performance benchmarks documented

#### Task 4.3.2: API Response Caching
**Service**: api-gateway  
**Priority**: P1 (High)  
**Assignee**: Backend Developer  
**Estimated**: 0.5 days  

**Implementation Details:**
```python
from app.utils.redis.redis_client import redis_client
import json
import hashlib

class AuthenticationCacheService:
    
    def __init__(self):
        self.cache_ttl = 3600  # 1 hour
        self.batch_cache_ttl = 1800  # 30 minutes
    
    def get_cache_key(self, workflow_id: str, user_id: str = None) -> str:
        base_key = f"workflow_auth:{workflow_id}"
        if user_id:
            base_key += f":user:{user_id}"
        return base_key
    
    def get_batch_cache_key(self, workflow_ids: List[str]) -> str:
        ids_hash = hashlib.md5(",".join(sorted(workflow_ids)).encode()).hexdigest()
        return f"batch_auth:{ids_hash}"
    
    async def get_cached_requirements(self, workflow_id: str, user_id: str = None) -> Optional[Dict]:
        cache_key = self.get_cache_key(workflow_id, user_id)
        cached_data = await redis_client.get(cache_key)
        
        if cached_data:
            return json.loads(cached_data)
        return None
    
    async def cache_requirements(self, workflow_id: str, data: Dict, user_id: str = None):
        cache_key = self.get_cache_key(workflow_id, user_id)
        await redis_client.setex(
            cache_key, 
            self.cache_ttl, 
            json.dumps(data, default=str)
        )
```

**Acceptance Criteria:**
- [ ] Redis caching implemented for frequent queries
- [ ] Cache invalidation strategy
- [ ] Cache hit rate monitoring
- [ ] Performance improvement measured

---

## Phase 5: Documentation & Deployment (Week 8)

### 5.1 Documentation Tasks

#### Task 5.1.1: API Documentation
**Service**: api-gateway  
**Priority**: P2 (Medium)  
**Assignee**: Backend Developer  
**Estimated**: 0.5 days  

**Requirements:**
- [ ] OpenAPI specifications for new endpoints
- [ ] Example requests and responses
- [ ] Error codes and handling
- [ ] Rate limiting information

#### Task 5.1.2: User Documentation
**Service**: documentation  
**Priority**: P2 (Medium)  
**Assignee**: Technical Writer  
**Estimated**: 1 day  

**Requirements:**
- [ ] User guide for authentication requirements
- [ ] Credential setup tutorials
- [ ] Marketplace filtering documentation
- [ ] Troubleshooting guide

### 5.2 Deployment Tasks

#### Task 5.2.1: Production Deployment
**Services**: All  
**Priority**: P0 (Blocking)  
**Assignee**: DevOps Engineer  
**Estimated**: 1 day  

**Requirements:**
- [ ] Database migrations in production
- [ ] Feature flag configuration
- [ ] Monitoring and alerting setup
- [ ] Rollback plan prepared

#### Task 5.2.2: Post-Deployment Monitoring
**Services**: All  
**Priority**: P1 (High)  
**Assignee**: DevOps Engineer  
**Estimated**: Ongoing  

**Requirements:**
- [ ] Performance monitoring dashboards
- [ ] Error rate tracking
- [ ] User adoption metrics
- [ ] Database performance monitoring

---

## Task Dependencies & Critical Path

### Critical Path Analysis
1. **Database Schema** → **Credential Analysis** → **API Development** → **Frontend Integration**
2. **Workflow Service APIs** → **API Gateway Integration** → **Frontend Components**
3. **Authentication Screen** → **Setup Wizard** → **Import Flow Integration**

### Parallel Development Tracks
- **Track 1**: Backend infrastructure (Tasks 1.1-1.3)
- **Track 2**: API Gateway development (Tasks 2.1-2.2) - depends on Track 1
- **Track 3**: Frontend components (Tasks 3.1-3.2) - can start after Track 1 completion
- **Track 4**: Integration & testing (Tasks 4.1-4.3) - depends on Tracks 2 & 3

---

## Success Criteria & Definition of Done

### Technical Success Criteria
- [ ] All API endpoints respond within performance targets (<500ms)
- [ ] Frontend components pass accessibility standards (WCAG 2.1 AA)
- [ ] 95% test coverage for new functionality
- [ ] Zero critical security vulnerabilities
- [ ] Database queries optimized with proper indexing

### Business Success Criteria
- [ ] 80% of users complete authentication requirements screen
- [ ] 25% increase in marketplace workflow adoption within 30 days
- [ ] 40% reduction in authentication-related support tickets
- [ ] 4.5/5 user satisfaction rating for import experience

### User Experience Success Criteria
- [ ] Users can identify authentication requirements before import
- [ ] Credential setup can be completed within guided flow
- [ ] Marketplace filtering by authentication requirements works intuitively
- [ ] Import process completion rate improves by 25%

---

## Risk Mitigation & Contingency Plans

### Technical Risks
1. **Database Performance**: Implement caching and materialized views
2. **API Response Times**: Use batch processing and Redis caching
3. **Frontend Complexity**: Progressive disclosure and guided flows
4. **Integration Issues**: Comprehensive testing and staged rollout

### Business Risks
1. **User Adoption**: Gradual rollout with user feedback
2. **Support Burden**: Comprehensive documentation and FAQs
3. **Marketplace Quality**: Enhanced filtering and requirement display

### Operational Risks
1. **Deployment Issues**: Feature flags and rollback procedures
2. **Monitoring Gaps**: Comprehensive dashboards and alerting
3. **Performance Degradation**: Load testing and scaling plans

---

*This task list provides a comprehensive roadmap for implementing marketplace workflow authentication requirements. Each task includes specific implementation details, acceptance criteria, and estimated timelines to ensure successful delivery.*

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Create comprehensive PRD for authentication requirements feature", "status": "completed", "priority": "high"}, {"id": "2", "content": "Create detailed task list with technical specifications", "status": "completed", "priority": "high"}, {"id": "3", "content": "Save PRD and task list as markdown files", "status": "completed", "priority": "medium"}]