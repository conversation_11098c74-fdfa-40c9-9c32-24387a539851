# Assuming necessary imports for SQLAlchemy
from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, ForeignKey, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.models.user import Base
from app.utils.constants.constants import (
    DEFAULT_AI_MODEL,
    DEFAULT_AI_PROVIDER,
    DEFAULT_TEMPERATURE,
    DEFAULT_MAX_OUTPUT_TOKENS
)
import uuid

class Preference(Base): # Replace 'Base' with your actual declarative base
    __tablename__ = "preferences"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), unique=True, nullable=False)
    provider = Column(String, nullable=False, default=DEFAULT_AI_PROVIDER)
    model = Column(String, nullable=False, default=DEFAULT_AI_MODEL)
    temperature = Column(Float, nullable=False, default=DEFAULT_TEMPERATURE)
    max_output_tokens = Column(Integer, nullable=False, default=DEFAULT_MAX_OUTPUT_TOKENS)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), server_default=func.now())

    # Define relationship to the User model if you have one
    # user = relationship("User") # Adjust "User" based on your User model class name
