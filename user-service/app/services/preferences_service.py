# app/services/preferences_service.py

import uuid
import grpc
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.models.preferences import Preference
from app.grpc import user_pb2, user_pb2_grpc
from app.db.session import SessionLocal


class PreferencesService:
    def __init__(self):
        pass

    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def create_preference(
        self, request: user_pb2.CreatePreferenceRequest, context: grpc.ServicerContext
    ) -> user_pb2.CreatePreferenceResponse:
        """
        DEPRECATED: Creates a new preference entry for a user.
        Use update_preference() instead, which handles both create and update operations.
        """
        db = self.get_db()
        try:
            # Check if preference already exists for the user
            existing_preference = (
                db.query(Preference).filter(Preference.user_id == request.user_id).first()
            )
            if existing_preference:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("Preference already exists for this user.")
                return user_pb2.CreatePreferenceResponse(
                    success=False, message="Preference already exists for this user."
                )

            # Create new preference entry
            new_preference = Preference(
                user_id=request.user_id,
                provider=request.provider,
                model=request.model,
                temperature=request.temperature,
                max_output_tokens=request.max_output_tokens,
            )
            db.add(new_preference)
            db.commit()
            db.refresh(new_preference)

            return user_pb2.CreatePreferenceResponse(
                success=True,
                message="Preference created successfully.",
                preference=user_pb2.PreferenceInfo(
                    id=str(new_preference.id),
                    user_id=str(new_preference.user_id),
                    provider=new_preference.provider,
                    model=new_preference.model,
                    temperature=new_preference.temperature,
                    max_output_tokens=new_preference.max_output_tokens,
                    created_at=new_preference.created_at.isoformat(),
                    updated_at=new_preference.updated_at.isoformat(),
                ),
            )
        except IntegrityError:
            db.rollback()
            context.set_code(grpc.StatusCode.ALREADY_EXISTS)
            context.set_details("Database integrity error. Preference might already exist.")
            return user_pb2.CreatePreferenceResponse(
                success=False, message="Database integrity error. Preference might already exist."
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.CreatePreferenceResponse(
                success=False, message=f"Failed to create preference: {e}"
            )
        finally:
            db.close()

    def get_preference(
        self, request: user_pb2.GetPreferenceRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetPreferenceResponse:
        """Retrieves a user's preference."""
        db = self.get_db()

        try:
            # Find preference by user_id
            preference = db.query(Preference).filter(Preference.user_id == request.user_id).first()

            if not preference:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Preference not found for this user.")
                return user_pb2.GetPreferenceResponse(
                    success=False,
                    message="You don't have any preferences set up yet. Please add your preferences to store and customize your settings."
                )

            return user_pb2.GetPreferenceResponse(
                success=True,
                message="Preference retrieved successfully.",
                preference=user_pb2.PreferenceInfo(
                    id=str(preference.id),
                    user_id=str(preference.user_id),
                    provider=preference.provider,
                    model=preference.model,
                    temperature=preference.temperature,
                    max_output_tokens=preference.max_output_tokens,
                    created_at=preference.created_at.isoformat(),
                    updated_at=preference.updated_at.isoformat(),
                ),
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.GetPreferenceResponse(
                success=False, message=f"Failed to retrieve preference: {e}"
            )
        finally:
            db.close()

    def update_preference(
        self, request: user_pb2.UpdatePreferenceRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdatePreferenceResponse:
        """Creates or updates a user's preference (upsert operation)."""
        db = self.get_db()

        try:
            # Find preference by user_id
            preference = db.query(Preference).filter(Preference.user_id == request.user_id).first()

            if not preference:
                # Create new preference if it doesn't exist
                preference = Preference(
                    user_id=request.user_id,
                    provider=request.provider if request.HasField("provider") else None,
                    model=request.model if request.HasField("model") else None,
                    temperature=request.temperature if request.HasField("temperature") else None,
                    max_output_tokens=request.max_output_tokens if request.HasField("max_output_tokens") else None,
                )
                db.add(preference)
                operation_message = "Preference updated successfully."
            else:
                # Update existing preference
                if request.HasField("provider"):
                    preference.provider = request.provider
                if request.HasField("model"):
                    preference.model = request.model
                if request.HasField("temperature"):
                    preference.temperature = request.temperature
                if request.HasField("max_output_tokens"):
                    preference.max_output_tokens = request.max_output_tokens
                operation_message = "Preference updated successfully."

            db.commit()
            db.refresh(preference)

            return user_pb2.UpdatePreferenceResponse(
                success=True,
                message=operation_message,
                preference=user_pb2.PreferenceInfo(
                    id=str(preference.id),
                    user_id=str(preference.user_id),
                    provider=preference.provider,
                    model=preference.model,
                    temperature=preference.temperature,
                    max_output_tokens=preference.max_output_tokens,
                    created_at=preference.created_at.isoformat(),
                    updated_at=preference.updated_at.isoformat(),
                ),
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.UpdatePreferenceResponse(
                success=False, message=f"Failed to update preference: {e}"
            )
        finally:
            db.close()

    def list_preferences(
        self, request: user_pb2.ListPreferencesRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListPreferencesResponse:
        """Lists a user's preferences."""
        db = self.get_db()

        try:
            # Find preference by user_id
            # Assuming a user has at most one preference entry based on the unique constraint
            preference = db.query(Preference).filter(Preference.user_id == request.user_id).first()

            preferences_list = []
            if preference:
                preferences_list.append(
                    user_pb2.PreferenceInfo(
                        id=str(preference.id),
                        user_id=str(preference.user_id),
                        provider=preference.provider,
                        model=preference.model,
                        temperature=preference.temperature,
                        max_output_tokens=preference.max_output_tokens,
                        created_at=preference.created_at.isoformat(),
                        updated_at=preference.updated_at.isoformat(),
                    )
                )

            return user_pb2.ListPreferencesResponse(
                success=True,
                message="Preferences retrieved successfully.",
                preferences=preferences_list,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.ListPreferencesResponse(
                success=False, message=f"Failed to retrieve preferences: {e}", preferences=[]
            )
        finally:
            db.close()
