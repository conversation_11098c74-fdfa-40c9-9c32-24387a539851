import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.db.session import Base
from app.core.config import settings

# Use a test database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={{"check_same_thread": False}}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Override the get_db dependency
def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[app.db.session.get_db] = override_get_db

client = TestClient(app)

@pytest.fixture(scope="module")
def setup_database():
    # Create the database tables before tests run
    Base.metadata.create_all(bind=engine)
    yield
    # Drop the database tables after tests run
    Base.metadata.drop_all(bind=engine)

def test_register_user_with_organization(setup_database):
    """
    Test user registration with an organization ID.
    """
    user_data = {{
        "email": "<EMAIL>",
        "password": "securepassword",
        "full_name": "Test User",
        "organization_id": "org123" # Include organization_id
    }}
    response = client.post("/api/v1/register", json=user_data)

    assert response.status_code == 200
    response_data = response.json()
    assert response_data["success"] is True
    assert "An email has been sent" in response_data["message"]

    # Verify the user is created in the database with the correct organization_id
    db = TestingSessionLocal()
    from app.models.user import User # Import User model here
    user = db.query(User).filter(User.email == user_data["email"]).first()
    db.close()

    assert user is not None
    assert user.email == user_data["email"]
    assert user.full_name == user_data["full_name"]
    assert user.default_organization == user_data["organization_id"]
    assert user.is_email_verified is False # Should be False initially

def test_login_includes_organization_in_jwt(setup_database):
    """
    Test that the organization ID is included in the JWT upon login.
    """
    # Register a user first
    register_data = {
        "email": "<EMAIL>",
        "password": "loginpassword",
        "full_name": "Login User",
        "organization_id": "org456"
    }
    register_response = client.post("/api/v1/register", json=register_data)
    assert register_response.status_code == 200

    # Simulate email verification (since login requires it)
    # In a real scenario, this would involve the verification email flow.
    # For this test, we'll directly update the user in the database.
    db = TestingSessionLocal()
    from app.models.user import User # Import User model here
    user = db.query(User).filter(User.email == register_data["email"]).first()
    user.is_email_verified = True
    db.commit()
    db.close()

    # Simulate login
    login_data = {
        "email": register_data["email"],
        "password": register_data["password"]
    }
    login_response = client.post("/api/v1/login", json=login_data)
    assert login_response.status_code == 200
    login_response_data = login_response.json()
    assert login_response_data["success"] is True
    assert "accessToken" in login_response_data
    assert "refreshToken" in login_response_data

    # Decode the access token and check for organization_id
    access_token = login_response_data["accessToken"]
    import jwt # Import jwt library
    # Assuming settings.JWT_SECRET_KEY and settings.JWT_ALGORITHM are accessible
    # You might need to import settings or pass it to the test function
    from app.core.config import settings
    decoded_token = jwt.decode(access_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])

    assert "organization_id" in decoded_token
    assert decoded_token["organization_id"] == register_data["organization_id"]

    # You might also want to check the refresh token payload if needed
    # refresh_token = login_response_data["refreshToken"]
    # decoded_refresh_token = jwt.decode(refresh_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
    # assert "organization_id" in decoded_refresh_token # Add if organization_id is included in refresh token

def test_generate_organisation_tokens_updates_default_organization(setup_database):
    """
    Test that generateOrganisationTokens updates the user's default_organization field.
    """
    # First, create a user without organization_id (default_organization will be None)
    user_data = {
        "email": "<EMAIL>",
        "password": "tokenpassword",
        "full_name": "Token User"
    }
    register_response = client.post("/api/v1/register", json=user_data)
    assert register_response.status_code == 200
    
    # Verify user and set email as verified
    db = TestingSessionLocal()
    from app.models.user import User
    user = db.query(User).filter(User.email == user_data["email"]).first()
    user.is_email_verified = True
    db.commit()
    
    # Verify initial default_organization is None
    assert user.default_organization is None
    
    # Now test the generateOrganisationTokens function
    from app.services.organization_service import OrganizationService
    from app.grpc import user_pb2
    import grpc
    
    # Create a mock context
    class MockContext:
        def set_code(self, code):
            pass
        def set_details(self, details):
            pass
    
    context = MockContext()
    org_service = OrganizationService()
    
    # Create request to generate tokens for an organization
    request = user_pb2.GenerateOrganisationTokensRequest(
        user_email="<EMAIL>",
        organisation_id="new_org_123"
    )
    
    # Call the function
    response = org_service.generateOrganisationTokens(request, context)
    
    # Verify the response is successful
    assert response.success is True
    assert response.accessToken is not None
    assert response.refreshToken is not None
    
    # Verify that the user's default_organization has been updated
    db.refresh(user)
    assert user.default_organization == "new_org_123"
    
    # Test updating to a different organization
    request2 = user_pb2.GenerateOrganisationTokensRequest(
        user_email="<EMAIL>",
        organisation_id="another_org_456"
    )
    
    response2 = org_service.generateOrganisationTokens(request2, context)
    assert response2.success is True
    
    # Verify that the user's default_organization has been updated again
    db.refresh(user)
    assert user.default_organization == "another_org_456"
    
    # Verify the tokens contain the correct organization_id
    import jwt
    from app.core.config import settings
    decoded_access_token = jwt.decode(response2.accessToken, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
    decoded_refresh_token = jwt.decode(response2.refreshToken, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
    
    assert decoded_access_token["organization_id"] == "another_org_456"
    assert decoded_refresh_token["organization_id"] == "another_org_456"
    
    db.close()