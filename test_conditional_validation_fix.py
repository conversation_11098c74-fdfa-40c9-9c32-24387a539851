#!/usr/bin/env python3
"""
Test script to validate the conditional component validation fix.

This script tests that:
1. The 'is_empty' operator works without expected_value
2. The 'exists' operator works without expected_value  
3. Other operators still require expected_value
4. The component processes requests correctly
"""

import sys
import os
import asyncio
import json

# Add the node-executor-service to the path
sys.path.append('/Users/<USER>/Desktop/ruh_ai/backend/node-executor-service')

from app.components.conditional_component import ConditionalComponent, ConditionSchema, ConditionalRequestSchema

async def test_schema_validation():
    """Test the fixed schema validation."""
    print("Testing Conditional Component Schema Validation Fix")
    print("=" * 60)
    
    # Test 1: is_empty operator without expected_value (should work now)
    print("Test 1: 'is_empty' operator without expected_value")
    try:
        condition = ConditionSchema(
            operator="is_empty",
            next_transition="empty_transition"
            # No expected_value provided
        )
        print("✅ SUCCESS: 'is_empty' operator accepts missing expected_value")
        print(f"   Validated condition: {condition.dict()}")
    except Exception as e:
        print(f"❌ FAILED: {e}")
    
    print()
    
    # Test 2: exists operator without expected_value (should work)
    print("Test 2: 'exists' operator without expected_value")
    try:
        condition = ConditionSchema(
            operator="exists",
            next_transition="exists_transition"
            # No expected_value provided
        )
        print("✅ SUCCESS: 'exists' operator accepts missing expected_value")
        print(f"   Validated condition: {condition.dict()}")
    except Exception as e:
        print(f"❌ FAILED: {e}")
    
    print()
    
    # Test 3: equals operator without expected_value (should fail)
    print("Test 3: 'equals' operator without expected_value (should fail)")
    try:
        condition = ConditionSchema(
            operator="equals",
            next_transition="equals_transition"
            # No expected_value provided
        )
        print(f"❌ UNEXPECTED SUCCESS: 'equals' should require expected_value")
        print(f"   Condition: {condition.dict()}")
    except Exception as e:
        print(f"✅ EXPECTED FAILURE: {e}")
    
    print()
    
    # Test 4: equals operator with expected_value (should work)
    print("Test 4: 'equals' operator with expected_value")
    try:
        condition = ConditionSchema(
            operator="equals",
            expected_value="test_value",
            next_transition="equals_transition"
        )
        print("✅ SUCCESS: 'equals' operator works with expected_value")
        print(f"   Validated condition: {condition.dict()}")
    except Exception as e:
        print(f"❌ FAILED: {e}")
    
    print()
    
    # Test 5: Full ConditionalRequestSchema with is_empty operator
    print("Test 5: Full ConditionalRequestSchema with 'is_empty' operator")
    try:
        request_data = {
            "request_id": "test-request-001",
            "conditions": [
                {
                    "operator": "is_empty",
                    "next_transition": "empty_branch"
                    # No expected_value
                }
            ],
            "input_data": "",
            "default_transition": "default_branch"
        }
        
        request_schema = ConditionalRequestSchema(**request_data)
        print("✅ SUCCESS: Full request schema validates with 'is_empty' operator")
        print(f"   Conditions: {request_schema.conditions}")
    except Exception as e:
        print(f"❌ FAILED: {e}")

async def test_component_execution():
    """Test the component execution with the fixed validation."""
    print("\n" + "=" * 60)
    print("Testing Component Execution")
    print("=" * 60)
    
    component = ConditionalComponent()
    
    # Test case that was failing before the fix
    test_payload = {
        "request_id": "test-execution-001",
        "conditions": [
            {
                "operator": "is_empty",
                "next_transition": "empty_branch"
                # No expected_value - this should work now
            }
        ],
        "input_data": "",  # Empty string to test is_empty
        "default_transition": "default_branch"
    }
    
    print("Test: Component execution with 'is_empty' operator")
    print(f"Payload: {json.dumps(test_payload, indent=2)}")
    
    try:
        result = await component.process(test_payload)
        print("✅ SUCCESS: Component executed without validation error")
        print(f"Result status: {result.get('status')}")
        print(f"Target transition: {result.get('routing_decision', {}).get('target_transition')}")
        print(f"Condition result: {result.get('routing_decision', {}).get('condition_result')}")
        
        # Check if it correctly identified the empty string
        if result.get('routing_decision', {}).get('condition_result') == True:
            print("✅ LOGIC SUCCESS: Correctly identified empty string")
        else:
            print("❌ LOGIC ISSUE: Did not correctly identify empty string")
            
    except Exception as e:
        print(f"❌ FAILED: {e}")

async def main():
    """Run all tests."""
    await test_schema_validation()
    await test_component_execution()
    
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print("The fix changes the validation schema to:")
    print("1. Make expected_value optional (None by default)")
    print("2. Add conditional validation that only requires expected_value")
    print("   for operators that actually need it")
    print("3. Allow 'is_empty' and 'exists' operators to work without expected_value")
    print("4. Maintain backward compatibility for other operators")

if __name__ == "__main__":
    asyncio.run(main())