// Test script to verify type conversion functionality
const { convertValueToType, processNodeConfigTypes } = require('./workflow-builder-app/src/utils/valueFormatting.ts');

// Test convertValueToType function
console.log('Testing convertValueToType function:');

// Test integer conversion
console.log('Integer conversion:');
console.log('  "1000" -> int:', convertValueToType("1000", "int"));
console.log('  "1000.5" -> int:', convertValueToType("1000.5", "int"));
console.log('  1000.5 -> int:', convertValueToType(1000.5, "int"));

// Test float conversion
console.log('Float conversion:');
console.log('  "1000.5" -> float:', convertValueToType("1000.5", "float"));
console.log('  "1000" -> float:', convertValueToType("1000", "float"));

// Test boolean conversion
console.log('Boolean conversion:');
console.log('  "true" -> bool:', convertValueToType("true", "bool"));
console.log('  "false" -> bool:', convertValueToType("false", "bool"));
console.log('  "1" -> bool:', convertValueToType("1", "bool"));

// Test node config processing
console.log('\nTesting processNodeConfigTypes function:');

const mockNode = {
  id: "test-node",
  data: {
    config: {
      daily_budget: "1000",
      name: "Test Campaign",
      status: "PAUSED",
      parallel_execution: "true",
      max_concurrent: "3"
    }
  }
};

const mockNodeDefinition = {
  inputs: [
    { name: "daily_budget", input_type: "int" },
    { name: "name", input_type: "string" },
    { name: "status", input_type: "string" },
    { name: "parallel_execution", input_type: "bool" },
    { name: "max_concurrent", input_type: "int" }
  ]
};

const processedConfig = processNodeConfigTypes(mockNode, mockNodeDefinition);
console.log('Original config:', mockNode.data.config);
console.log('Processed config:', processedConfig);
console.log('Types after processing:');
Object.entries(processedConfig).forEach(([key, value]) => {
  console.log(`  ${key}: ${typeof value} (${value})`);
});
