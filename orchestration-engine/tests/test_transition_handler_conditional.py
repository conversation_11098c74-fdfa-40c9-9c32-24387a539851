"""
Test suite for TransitionHandler conditional component integration.

This module tests the integration of conditional components with the transition handler:
- Detection of conditional component transitions
- Execution of conditional components through node executor
- Processing of conditional component results for routing decisions
- Integration with existing transition execution patterns

Following TDD methodology - Phase 2 Cycle 2: Transition Handler Integration
"""

import pytest
import sys
import os
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

# Add the app directory to the Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core_.transition_handler import TransitionHandler
from app.core_.conditional_routing_handler import ConditionalRoutingHandler


class TestTransitionHandlerConditionalIntegration:
    """Test suite for transition handler conditional component integration."""
    
    @pytest.fixture
    def mock_logger(self):
        """Fixture providing a mock logger for testing."""
        logger = Mock()
        logger.info = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.debug = Mock()
        logger.execute = Mock()
        return logger
    
    @pytest.fixture
    def mock_state_manager(self):
        """Fixture providing a mock state manager for testing."""
        state_manager = Mock()
        state_manager.mark_transition_completed = Mock()
        state_manager.get_global_context = Mock(return_value={})
        return state_manager
    
    @pytest.fixture
    def mock_workflow_utils(self):
        """Fixture providing a mock workflow utils for testing."""
        workflow_utils = Mock()
        workflow_utils._evaluate_switch_case = AsyncMock(return_value=[])
        return workflow_utils
    
    @pytest.fixture
    def mock_node_executor(self):
        """Fixture providing a mock node executor for testing."""
        node_executor = Mock()
        node_executor.execute_tool = AsyncMock()
        return node_executor
    
    @pytest.fixture
    def transition_handler(self, mock_logger, mock_state_manager, mock_workflow_utils):
        """Fixture providing TransitionHandler instance for testing."""
        handler = TransitionHandler(
            state_manager=mock_state_manager,
            transitions_by_id={},
            nodes={},
            dependency_map={},
            workflow_utils=mock_workflow_utils,
            tool_executor=Mock(),
            node_executor=Mock(),
            agent_executor=Mock(),
            result_callback=AsyncMock()
        )
        # Add conditional routing handler
        handler.conditional_routing_handler = ConditionalRoutingHandler(logger=mock_logger)
        return handler
    
    def test_detects_conditional_component_transition(self, transition_handler):
        """
        Test detection of conditional component transitions.
        
        Expected to FAIL initially until detection logic is implemented.
        """
        conditional_transition = {
            "id": "transition-conditional-1",
            "execution_type": "Component",
            "node_info": {
                "tools_to_use": [
                    {
                        "tool_name": "conditional",
                        "server_id": "node-executor-service"
                    }
                ]
            }
        }
        
        is_conditional = transition_handler._is_conditional_component_transition(conditional_transition)
        assert is_conditional is True, \
            "Should detect conditional component transition by tool_name"
    
    def test_does_not_detect_regular_component_transition(self, transition_handler):
        """
        Test that regular component transitions are not detected as conditional.
        
        Expected to FAIL initially until detection logic is implemented.
        """
        regular_transition = {
            "id": "transition-regular-1",
            "execution_type": "Component",
            "node_info": {
                "tools_to_use": [
                    {
                        "tool_name": "data_processor",
                        "server_id": "node-executor-service"
                    }
                ]
            }
        }
        
        is_conditional = transition_handler._is_conditional_component_transition(regular_transition)
        assert is_conditional is False, \
            "Should not detect regular component transition as conditional"
    
    def test_detects_conditional_component_with_multiple_tools(self, transition_handler):
        """
        Test detection when conditional tool is among multiple tools.
        
        Expected to FAIL initially until detection logic is implemented.
        """
        multi_tool_transition = {
            "id": "transition-multi-tool",
            "execution_type": "Component",
            "node_info": {
                "tools_to_use": [
                    {
                        "tool_name": "data_processor",
                        "server_id": "node-executor-service"
                    },
                    {
                        "tool_name": "conditional",
                        "server_id": "node-executor-service"
                    }
                ]
            }
        }
        
        is_conditional = transition_handler._is_conditional_component_transition(multi_tool_transition)
        assert is_conditional is True, \
            "Should detect conditional component even when mixed with other tools"
    
    @pytest.mark.asyncio
    async def test_handles_conditional_component_result_successfully(self, transition_handler):
        """
        Test handling of successful conditional component execution result.
        
        Expected to FAIL initially until result handling is implemented.
        """
        conditional_result = {
            "status": "success",
            "routing_decision": {
                "target_transition": "transition-success",
                "matched_condition": 1,
                "condition_result": True,
                "execution_time_ms": 5.2
            },
            "metadata": {
                "total_conditions": 3,
                "evaluation_order": 1
            }
        }
        
        mock_transition = {
            "id": "transition-conditional-test",
            "execution_type": "Component"
        }
        
        next_transitions = await transition_handler._handle_conditional_component_result(
            conditional_result, mock_transition
        )
        
        assert next_transitions == ["transition-success"], \
            "Should return target transition from conditional component result"
    
    @pytest.mark.asyncio
    async def test_handles_conditional_component_error_result(self, transition_handler):
        """
        Test handling of conditional component execution error result.
        
        Expected to FAIL initially until error handling is implemented.
        """
        error_result = {
            "status": "error",
            "error": "Component execution failed",
            "routing_decision": {
                "target_transition": "transition-error-recovery",
                "matched_condition": None,
                "condition_result": False,
                "execution_time_ms": 2.1
            }
        }
        
        mock_transition = {
            "id": "transition-conditional-error",
            "execution_type": "Component"
        }
        
        next_transitions = await transition_handler._handle_conditional_component_result(
            error_result, mock_transition
        )
        
        assert next_transitions == ["transition-error-recovery"], \
            "Should return default transition from error result"
    
    @pytest.mark.asyncio
    async def test_handles_malformed_conditional_result_gracefully(self, transition_handler):
        """
        Test graceful handling of malformed conditional component results.
        
        Expected to FAIL initially until error handling is implemented.
        """
        malformed_result = {
            "status": "success",
            "result": "Some output but no routing decision"
        }
        
        mock_transition = {
            "id": "transition-malformed",
            "execution_type": "Component"
        }
        
        next_transitions = await transition_handler._handle_conditional_component_result(
            malformed_result, mock_transition
        )
        
        assert next_transitions == [], \
            "Should return empty list for malformed conditional results"
    
    @pytest.mark.asyncio
    @patch('app.core_.transition_handler.TransitionHandler._execute_standard_or_reflection_transition')
    async def test_integrates_conditional_routing_with_transition_execution(
        self, mock_execute_transition, transition_handler
    ):
        """
        Test integration of conditional routing with standard transition execution.
        
        Expected to FAIL initially until integration is implemented.
        """
        # Mock the execution to return a conditional component result
        conditional_result = {
            "status": "success",
            "routing_decision": {
                "target_transition": "transition-premium",
                "matched_condition": 2,
                "condition_result": True,
                "execution_time_ms": 3.7
            }
        }
        
        conditional_transition = {
            "id": "transition-conditional-integration",
            "execution_type": "Component",
            "node_info": {
                "tools_to_use": [
                    {
                        "tool_name": "conditional",
                        "server_id": "node-executor-service"
                    }
                ]
            }
        }
        
        # Mock the execution result
        mock_execute_transition.return_value = ["transition-premium"]
        
        # This should be updated to use the new conditional routing logic
        next_transitions = await transition_handler._execute_standard_or_reflection_transition(
            conditional_transition
        )
        
        assert next_transitions == ["transition-premium"], \
            "Should integrate conditional routing with transition execution"


class TestTransitionHandlerBackwardCompatibility:
    """Test suite for backward compatibility with embedded routing."""
    
    @pytest.fixture
    def mock_logger(self):
        """Fixture providing a mock logger for testing."""
        logger = Mock()
        logger.info = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.debug = Mock()
        logger.execute = Mock()
        return logger
    
    @pytest.fixture
    def mock_state_manager(self):
        """Fixture providing a mock state manager for testing."""
        state_manager = Mock()
        state_manager.mark_transition_completed = Mock()
        state_manager.get_global_context = Mock(return_value={})
        return state_manager
    
    @pytest.fixture
    def mock_workflow_utils(self):
        """Fixture providing a mock workflow utils for testing."""
        workflow_utils = Mock()
        workflow_utils._evaluate_switch_case = AsyncMock(return_value=["transition-legacy"])
        return workflow_utils
    
    @pytest.fixture
    def transition_handler(self, mock_logger, mock_state_manager, mock_workflow_utils):
        """Fixture providing TransitionHandler instance for testing."""
        handler = TransitionHandler(
            state_manager=mock_state_manager,
            transitions_by_id={},
            nodes={},
            dependency_map={},
            workflow_utils=mock_workflow_utils,
            tool_executor=Mock(),
            node_executor=Mock(),
            agent_executor=Mock(),
            result_callback=AsyncMock()
        )
        # Add conditional routing handler
        handler.conditional_routing_handler = ConditionalRoutingHandler(logger=mock_logger)
        return handler
    
    @pytest.mark.asyncio
    async def test_legacy_embedded_routing_still_works(self, transition_handler, mock_workflow_utils):
        """
        Test that legacy embedded routing continues to work.
        
        Expected to FAIL initially until backward compatibility is implemented.
        """
        legacy_transition = {
            "id": "transition-legacy",
            "conditional_routing": {
                "cases": [
                    {
                        "condition": {
                            "source": "node_output",
                            "operator": "equals",
                            "expected_value": "success"
                        },
                        "next_transition": "transition-success"
                    }
                ],
                "default_transition": "transition-default"
            }
        }
        
        execution_result = "success"
        
        # Should use legacy routing logic
        next_transitions = await transition_handler._handle_transition_routing(
            legacy_transition, execution_result
        )
        
        assert next_transitions == ["transition-legacy"], \
            "Should use legacy embedded routing for transitions with conditional_routing"
        
        # Verify legacy method was called
        mock_workflow_utils._evaluate_switch_case.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_component_based_routing_when_conditional_component_detected(self, transition_handler):
        """
        Test component-based routing when conditional component is detected.
        
        Expected to FAIL initially until component routing is implemented.
        """
        component_transition = {
            "id": "transition-component",
            "execution_type": "Component",
            "node_info": {
                "tools_to_use": [{"tool_name": "conditional"}]
            }
        }
        
        mock_component_result = {
            "status": "success",
            "routing_decision": {
                "target_transition": "transition-from-component",
                "matched_condition": 1,
                "condition_result": True
            }
        }
        
        # Should use component-based routing
        next_transitions = await transition_handler._handle_transition_routing(
            component_transition, mock_component_result
        )
        
        assert next_transitions == ["transition-from-component"], \
            "Should use component-based routing for conditional component transitions"


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
