#!/usr/bin/env python3
"""
Test script for source-based conditional parameter handling.

This script tests the new source-based parameter resolution for conditional components.
"""


def extract_source_from_tool_params(tool_params_config):
    """Extract the source field value from tool_params configuration."""
    if not tool_params_config or "items" not in tool_params_config:
        return "node_output"  # Default source

    items = tool_params_config.get("items", [])
    for item in items:
        if item.get("field_name") == "source":
            source = item.get("field_value", "node_output")
            return source

    return "node_output"  # Default source


def test_source_extraction_logic():
    """Test the source extraction logic without dependencies."""
    print("🧪 Testing Source Extraction Logic")
    print("=" * 50)

    # Test 1: node_output source
    tool_params_node_output = {
        "items": [
            {
                "field_name": "source",
                "data_type": "string",
                "field_value": "node_output",
            },
            {
                "field_name": "conditions",
                "data_type": "array",
                "field_value": [
                    {"operator": "is_empty", "next_transition": "transition-1"}
                ],
            },
        ]
    }

    source = extract_source_from_tool_params(tool_params_node_output)
    print(f"   ✅ node_output source extracted: {source}")
    assert source == "node_output", f"Expected 'node_output', got '{source}'"

    # Test 2: global_context source
    tool_params_global_context = {
        "items": [
            {
                "field_name": "source",
                "data_type": "string",
                "field_value": "global_context",
            },
            {
                "field_name": "conditions",
                "data_type": "array",
                "field_value": [
                    {
                        "operator": "equals",
                        "variable_name": "user_type",
                        "expected_value": "premium",
                        "next_transition": "transition-premium",
                    }
                ],
            },
        ]
    }

    source = extract_source_from_tool_params(tool_params_global_context)
    print(f"   ✅ global_context source extracted: {source}")
    assert source == "global_context", f"Expected 'global_context', got '{source}'"

    # Test 3: No source specified (default)
    tool_params_no_source = {
        "items": [{"field_name": "conditions", "data_type": "array", "field_value": []}]
    }

    source = extract_source_from_tool_params(tool_params_no_source)
    print(f"   ✅ Default source extracted: {source}")
    assert source == "node_output", f"Expected 'node_output' as default, got '{source}'"

    print("\n✅ All source extraction tests passed!")


def test_parameter_resolution_logic():
    """Test the parameter resolution logic for different sources."""
    print("\n🧪 Testing Parameter Resolution Logic")
    print("=" * 50)

    # Simulate the parameter resolution logic
    def resolve_conditional_parameters(source, input_data_for_flow):
        resolved_parameters = {}

        # Always set input_data for data flow (constant regardless of source)
        resolved_parameters["input_data"] = input_data_for_flow

        # Source-based parameter handling for evaluation
        if source == "node_output":
            # For node_output source: use input data for condition evaluation
            resolved_parameters["node_output"] = input_data_for_flow
            resolved_parameters["global_context"] = {}

        elif source == "global_context":
            # For global_context source: use global context for condition evaluation
            resolved_parameters["node_output"] = {}  # Empty for global_context source
            resolved_parameters["global_context"] = {
                "user_type": "premium"
            }  # Mock global context

        else:
            # Fallback for unknown source (backward compatibility)
            resolved_parameters["node_output"] = input_data_for_flow
            resolved_parameters["global_context"] = {}

        return resolved_parameters

    # Test data
    test_input_data = {"status": "success", "data": "test_data"}

    # Test 1: node_output source
    print("\n1. Testing node_output source...")

    resolved_params = resolve_conditional_parameters("node_output", test_input_data)

    print(f"   Resolved parameters: {resolved_params}")

    # Verify node_output source behavior
    assert "input_data" in resolved_params, "input_data should be present"
    assert "node_output" in resolved_params, "node_output should be present"
    assert "global_context" in resolved_params, "global_context should be present"

    # For node_output source, both input_data and node_output should have the same value
    assert (
        resolved_params["input_data"] == resolved_params["node_output"]
    ), "For node_output source, input_data and node_output should be the same"

    print("   ✅ node_output source parameter resolution passed!")

    # Test 2: global_context source
    print("\n2. Testing global_context source...")

    resolved_params_gc = resolve_conditional_parameters(
        "global_context", test_input_data
    )

    print(f"   Resolved parameters: {resolved_params_gc}")

    # Verify global_context source behavior
    assert "input_data" in resolved_params_gc, "input_data should be present"
    assert "node_output" in resolved_params_gc, "node_output should be present"
    assert "global_context" in resolved_params_gc, "global_context should be present"

    # For global_context source, node_output should be empty but input_data should have flow data
    assert (
        resolved_params_gc["node_output"] == {}
    ), "For global_context source, node_output should be empty"
    assert (
        resolved_params_gc["input_data"] is not None
    ), "For global_context source, input_data should still have flow data"

    print("   ✅ global_context source parameter resolution passed!")

    print("\n✅ All parameter resolution tests passed!")


def main():
    """Run all tests."""
    print("🚀 Starting Source-Based Conditional Parameter Tests")
    print("=" * 60)

    try:
        test_source_extraction_logic()
        test_parameter_resolution_logic()

        print("\n🎉 All tests passed successfully!")
        print(
            "✅ Source-based conditional parameter handling logic is working correctly!"
        )

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
