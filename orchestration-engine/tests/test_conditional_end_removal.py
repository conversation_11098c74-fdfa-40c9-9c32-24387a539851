#!/usr/bin/env python3
"""
Test script to validate the conditional end transition removal feature.
This script tests that unreachable end transitions are properly removed when conditional nodes execute.
"""

import sys
import os
import asyncio

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Mock the logger to avoid import issues
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")

# Mock the get_logger function
def get_logger(name=None):
    return MockLogger()

# Patch the import
sys.modules['app.utils.enhanced_logger'] = type('MockModule', (), {'get_logger': get_logger})()

# Import the classes we need to test
from core_.conditional_routing_handler import ConditionalRoutingHandler
from core_.state_manager import WorkflowStateManager

def test_extract_unreachable_end_transitions():
    """Test the extraction of unreachable end transitions from conditional results."""
    print("🧪 Testing Unreachable End Transitions Extraction")
    print("=" * 50)
    
    handler = ConditionalRoutingHandler()
    
    # Test case 1: Conditional with ends_at fields
    transition = {
        "id": "conditional_node",
        "node_info": {
            "tools_to_use": [{
                "tool_name": "conditional",
                "tool_parameters": {
                    "conditions": [
                        {
                            "condition": "input.value > 10",
                            "target_transition": "path_a",
                            "ends_at": ["end_path_b", "end_path_c"]  # These become unreachable if this condition matches
                        },
                        {
                            "condition": "input.value <= 10",
                            "target_transition": "path_b", 
                            "ends_at": ["end_path_a"]  # This becomes unreachable if this condition matches
                        },
                        {
                            "condition": "input.value < 0",
                            "target_transition": "path_c",
                            "ends_at": []  # No end transitions become unreachable
                        }
                    ]
                }
            }]
        }
    }
    
    # Execution result where condition 1 matched (input.value > 10)
    execution_result = {
        "status": "success",
        "routing_decision": {
            "target_transition": "path_a",
            "matched_condition": 1,  # First condition matched (1-indexed)
            "condition_result": True
        },
        "metadata": {
            "total_conditions": 3
        },
        "input_data": {"value": 15}
    }
    
    unreachable = handler._extract_unreachable_end_transitions(
        execution_result, transition, "conditional_node"
    )
    
    # Since condition 1 matched, conditions 2 and 3 didn't match
    # Condition 2 has ends_at: ["end_path_a"]
    # Condition 3 has ends_at: []
    # So unreachable should be ["end_path_a"]
    expected_unreachable = ["end_path_a"]
    assert set(unreachable) == set(expected_unreachable), f"Expected {expected_unreachable}, got {unreachable}"
    print(f"   ✅ Condition 1 matched, unreachable end transitions: {unreachable}")
    
    # Test case 2: Different condition matches
    execution_result_2 = {
        "status": "success",
        "routing_decision": {
            "target_transition": "path_b",
            "matched_condition": 2,  # Second condition matched
            "condition_result": True
        },
        "metadata": {
            "total_conditions": 3
        },
        "input_data": {"value": 5}
    }
    
    unreachable_2 = handler._extract_unreachable_end_transitions(
        execution_result_2, transition, "conditional_node"
    )
    
    # Since condition 2 matched, conditions 1 and 3 didn't match
    # Condition 1 has ends_at: ["end_path_b", "end_path_c"]
    # Condition 3 has ends_at: []
    # So unreachable should be ["end_path_b", "end_path_c"]
    expected_unreachable_2 = ["end_path_b", "end_path_c"]
    assert set(unreachable_2) == set(expected_unreachable_2), f"Expected {expected_unreachable_2}, got {unreachable_2}"
    print(f"   ✅ Condition 2 matched, unreachable end transitions: {unreachable_2}")
    
    print("   ✅ Unreachable end transitions extraction test passed!")
    return True

def test_state_manager_end_removal():
    """Test the state manager's end transition removal functionality."""
    print("\n🧪 Testing State Manager End Transition Removal")
    print("=" * 50)
    
    state_manager = WorkflowStateManager(workflow_id="test_removal")
    
    # Set up initial end transitions
    initial_end_transitions = {"end_a", "end_b", "end_c", "end_d"}
    state_manager.set_end_transitions(initial_end_transitions)
    
    print(f"   Initial end transitions: {state_manager.end_transitions}")
    assert state_manager.end_transitions == initial_end_transitions
    
    # Test removing some end transitions
    to_remove = ["end_b", "end_d"]
    state_manager.remove_end_transitions(to_remove)
    
    expected_remaining = {"end_a", "end_c"}
    print(f"   After removing {to_remove}: {state_manager.end_transitions}")
    assert state_manager.end_transitions == expected_remaining, f"Expected {expected_remaining}, got {state_manager.end_transitions}"
    
    # Test removing non-existent transitions (should be safe)
    state_manager.remove_end_transitions(["non_existent", "also_fake"])
    assert state_manager.end_transitions == expected_remaining, "Should not change when removing non-existent transitions"
    print("   ✅ Removing non-existent transitions is safe")
    
    # Test removing from completed end transitions
    state_manager.mark_end_transition_completed("end_a")
    assert "end_a" in state_manager.completed_end_transitions
    
    # Remove end_a (which was completed)
    state_manager.remove_end_transitions(["end_a"])
    assert "end_a" not in state_manager.end_transitions, "Should remove from end_transitions"
    assert "end_a" not in state_manager.completed_end_transitions, "Should remove from completed_end_transitions"
    print("   ✅ Removing completed end transitions works correctly")
    
    print("   ✅ State manager end removal test passed!")
    return True

async def test_conditional_routing_with_removal():
    """Test the full conditional routing with end transition removal."""
    print("\n🧪 Testing Conditional Routing with End Transition Removal")
    print("=" * 50)
    
    # Create handler and state manager
    handler = ConditionalRoutingHandler()
    state_manager = WorkflowStateManager(workflow_id="test_conditional_removal")
    
    # Set up initial end transitions
    initial_end_transitions = {"end_path_a", "end_path_b", "end_path_c"}
    state_manager.set_end_transitions(initial_end_transitions)
    
    # Create conditional transition configuration
    transition = {
        "id": "conditional_switch",
        "node_info": {
            "tools_to_use": [{
                "tool_name": "conditional",
                "tool_parameters": {
                    "conditions": [
                        {
                            "condition": "input.type == 'A'",
                            "target_transition": "process_a",
                            "ends_at": ["end_path_b", "end_path_c"]  # B and C become unreachable
                        },
                        {
                            "condition": "input.type == 'B'",
                            "target_transition": "process_b",
                            "ends_at": ["end_path_a", "end_path_c"]  # A and C become unreachable
                        },
                        {
                            "condition": "input.type == 'C'",
                            "target_transition": "process_c",
                            "ends_at": ["end_path_a", "end_path_b"]  # A and B become unreachable
                        }
                    ]
                }
            }]
        }
    }
    
    # Test case 1: Route to path A
    execution_result_a = {
        "status": "success",
        "routing_decision": {
            "target_transition": "process_a",
            "matched_condition": 1,
            "condition_result": True
        },
        "metadata": {
            "total_conditions": 3
        },
        "input_data": {"type": "A"}
    }
    
    print(f"   Before conditional routing: {state_manager.end_transitions}")
    
    # Handle conditional result (should remove unreachable end transitions)
    next_transitions = await handler.handle_conditional_result(
        execution_result_a, transition, state_manager
    )
    
    print(f"   After routing to path A: {state_manager.end_transitions}")
    print(f"   Next transitions: {next_transitions}")
    
    # Should route to process_a
    assert next_transitions == ["process_a"], f"Expected ['process_a'], got {next_transitions}"
    
    # Should remove end_path_b and end_path_c (from non-matching conditions 2 and 3)
    expected_remaining = {"end_path_a"}
    assert state_manager.end_transitions == expected_remaining, f"Expected {expected_remaining}, got {state_manager.end_transitions}"
    
    print("   ✅ Conditional routing to path A works correctly")
    
    # Test case 2: Reset and route to path B
    state_manager.set_end_transitions(initial_end_transitions)  # Reset
    
    execution_result_b = {
        "status": "success",
        "routing_decision": {
            "target_transition": "process_b",
            "matched_condition": 2,
            "condition_result": True
        },
        "metadata": {
            "total_conditions": 3
        },
        "input_data": {"type": "B"}
    }
    
    next_transitions_b = await handler.handle_conditional_result(
        execution_result_b, transition, state_manager
    )
    
    print(f"   After routing to path B: {state_manager.end_transitions}")
    
    # Should route to process_b
    assert next_transitions_b == ["process_b"], f"Expected ['process_b'], got {next_transitions_b}"
    
    # Should remove end_path_a and end_path_c (from non-matching conditions 1 and 3)
    expected_remaining_b = {"end_path_b"}
    assert state_manager.end_transitions == expected_remaining_b, f"Expected {expected_remaining_b}, got {state_manager.end_transitions}"
    
    print("   ✅ Conditional routing to path B works correctly")
    
    print("   ✅ Conditional routing with end transition removal test passed!")
    return True

def test_workflow_completion_with_removal():
    """Test that workflow completion works correctly after end transition removal."""
    print("\n🧪 Testing Workflow Completion with End Transition Removal")
    print("=" * 50)
    
    state_manager = WorkflowStateManager(workflow_id="test_completion")
    
    # Set up workflow with 3 end transitions
    initial_end_transitions = {"end_a", "end_b", "end_c"}
    state_manager.set_end_transitions(initial_end_transitions)
    
    # Initially, workflow should not be complete
    assert not state_manager.are_all_end_transitions_completed(), "Should not be complete initially"
    
    # Remove one end transition (simulating conditional routing)
    state_manager.remove_end_transitions(["end_c"])
    
    # Still should not be complete (end_a and end_b remain)
    assert not state_manager.are_all_end_transitions_completed(), "Should not be complete after removal"
    
    # Complete one remaining end transition
    state_manager.mark_end_transition_completed("end_a")
    assert not state_manager.are_all_end_transitions_completed(), "Should not be complete after 1/2"
    
    # Complete the last remaining end transition
    state_manager.mark_end_transition_completed("end_b")
    assert state_manager.are_all_end_transitions_completed(), "Should be complete after all remaining are done"
    
    print("   ✅ Workflow completion works correctly after end transition removal")
    
    # Test edge case: Remove all end transitions
    state_manager.set_end_transitions({"end_x", "end_y"})
    state_manager.remove_end_transitions(["end_x", "end_y"])
    
    # With no end transitions remaining, should use legacy behavior (return False)
    assert not state_manager.are_all_end_transitions_completed(), "Should use legacy behavior when no end transitions remain"
    
    print("   ✅ Edge case (all end transitions removed) handled correctly")
    print("   ✅ Workflow completion with removal test passed!")
    return True

async def main():
    """Run all tests."""
    print("🚀 Starting Conditional End Transition Removal Feature Tests")
    print("=" * 60)
    
    try:
        # Test unreachable end transitions extraction
        test_extract_unreachable_end_transitions()
        
        # Test state manager end removal
        test_state_manager_end_removal()
        
        # Test conditional routing with removal
        await test_conditional_routing_with_removal()
        
        # Test workflow completion with removal
        test_workflow_completion_with_removal()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED! Conditional end transition removal feature is working correctly.")
        print("\nKey features validated:")
        print("  ✓ Unreachable end transitions are extracted from conditional results")
        print("  ✓ State manager can remove end transitions dynamically")
        print("  ✓ Conditional routing removes unreachable end transitions automatically")
        print("  ✓ Workflow completion works correctly after end transition removal")
        print("  ✓ Edge cases (removing all end transitions) are handled properly")
        
        print("\n📋 Implementation Summary:")
        print("  • ConditionalRoutingHandler extracts ends_at from non-matching conditions")
        print("  • State manager removes unreachable end transitions directly")
        print("  • Workflow only waits for reachable end transitions")
        print("  • Conditional routing optimizes workflow completion")
        
        print("\n🎯 Feature Benefits:")
        print("  • Prevents workflows from waiting for unreachable transitions")
        print("  • Optimizes workflow completion time")
        print("  • Supports complex conditional routing scenarios")
        print("  • Maintains workflow correctness and efficiency")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
