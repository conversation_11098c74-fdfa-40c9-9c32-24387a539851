apiVersion: v1
kind: ServiceAccount
metadata:
  name: <SERVICE_NAME>-sa
  namespace: <NAMESPACE>
  labels:
    name: <SERVICE_NAME>-sa
    namespace: <NAMESPACE>
    app: <SERVICE_NAME>
    deployment: <SERVICE_NAME>-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: <SERVICE_NAME>-dp
  namespace: <NAMESPACE>
  labels:
    name: <SERVICE_NAME>-dp
    namespace: <NAMESPACE>
    app: <SERVICE_NAME>
    serviceaccount: <SERVICE_NAME>-sa
    deployment: <SERVICE_NAME>-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: <SERVICE_NAME>
      deployment: <SERVICE_NAME>-dp
  template:
    metadata:
      labels:
        namespace: <NAMESPACE>
        app: <SERVICE_NAME>
        deployment: <SERVICE_NAME>-dp
    spec:
      serviceAccountName: <SERVICE_NAME>-sa      
      containers:
      - name: <SERVICE_NAME>
        image: <GAR_HOSTNAME>/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: <MEMORY_REQUEST>
            cpu: <CPU_REQUEST>
          limits:
            memory: <MEMORY_LIMIT>
            cpu: <CPU_LIMIT>
        ports:
        - containerPort: <CONTAINER_PORT>

---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: <SERVICE_NAME>-svc
  namespace: <NAMESPACE>
spec:
  selector:
    app: <SERVICE_NAME>
    deployment: <SERVICE_NAME>-dp
  ports:
    - protocol: TCP
      port: <SERVICE_PORT>
      targetPort: <CONTAINER_PORT>
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000