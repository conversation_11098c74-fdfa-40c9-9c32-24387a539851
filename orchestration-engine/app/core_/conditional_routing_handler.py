"""
Conditional Routing Handler for Workflow Orchestration Engine.

This module handles routing decisions from conditional components, processing
their execution results to determine next transitions in the workflow.

The handler supports both single and multiple transition routing formats
and integrates with the existing transition handler architecture.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from app.utils.enhanced_logger import get_logger


class ConditionalRoutingHandler:
    """
    Handles routing decisions from conditional component execution results.

    This class processes the output from conditional components and extracts
    the target transitions for the orchestration engine to execute next.

    Supports both legacy single transition format and new multiple transition format.
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the conditional routing handler.

        Args:
            logger: Optional logger instance. If not provided, creates a new one.
        """
        self.logger = logger or get_logger("ConditionalRoutingHandler")

    def is_conditional_component_result(self, execution_result: Any) -> bool:
        """
        Check if the execution result is from a conditional component.

        A conditional component result is identified by:
        - Being a dictionary
        - Having a 'routing_decision' field (the key identifier)

        Note: The 'status' field is optional as it may be stripped by some executors.

        Args:
            execution_result: The execution result to check

        Returns:
            True if the result is from a conditional component, False otherwise
        """
        if not isinstance(execution_result, dict):
            return False

        # The presence of 'routing_decision' is the key identifier for conditional components
        return "routing_decision" in execution_result

    async def handle_conditional_result(
        self,
        execution_result: Dict[str, Any],
        transition: Dict[str, Any],
        state_manager=None,
    ) -> List[str]:
        """
        Process conditional component result and extract next transitions.

        This method handles both single and multiple transition formats:
        - Single: {"target_transition": "transition_id"}
        - Multiple: {"target_transitions": ["transition_1", "transition_2"]}

        Also removes unreachable end transitions directly from the state manager.

        Args:
            execution_result: The execution result from conditional component
            transition: The transition configuration that was executed
            state_manager: Optional state manager to remove unreachable end transitions from

        Returns:
            List of next transition IDs to execute
        """
        transition_id = transition.get("id", "unknown")

        # Validate the execution result
        if not self.is_conditional_component_result(execution_result):
            self.logger.warning(
                f"Invalid conditional component result for transition {transition_id}: "
                f"missing required field (routing_decision)"
            )
            return []

        routing_decision = execution_result.get("routing_decision", {})
        if not routing_decision:
            self.logger.warning(
                f"No routing decision found in conditional result for transition {transition_id}"
            )
            return []

        # Extract target transitions - support both single and multiple formats
        target_transitions = self._extract_target_transitions(
            routing_decision, transition_id
        )

        if not target_transitions:
            self.logger.warning(
                f"No valid target transitions found in routing decision for transition {transition_id}"
            )
            return []

        # Remove unreachable end transitions directly from state manager
        if state_manager:
            # Log current end transitions before removal
            current_end_transitions = state_manager.end_transitions.copy()
            self.logger.info(
                f"🔀 Current end transitions before conditional routing: {current_end_transitions}"
            )

            unreachable_end_transitions = self._extract_unreachable_end_transitions(
                execution_result, transition, transition_id
            )

            if unreachable_end_transitions:
                self.logger.info(
                    f"🔀 Conditional routing for {transition_id} makes {len(unreachable_end_transitions)} end transitions unreachable: {unreachable_end_transitions}"
                )
                state_manager.remove_end_transitions(unreachable_end_transitions)

                # Log end transitions after removal
                remaining_end_transitions = state_manager.end_transitions.copy()
                self.logger.info(
                    f"🔀 Remaining end transitions after conditional routing: {remaining_end_transitions}"
                )
            else:
                self.logger.debug(
                    f"🔀 No unreachable end transitions found for {transition_id}"
                )

        # Log the routing decision
        self._log_routing_decision(execution_result, transition_id, target_transitions)

        return target_transitions

    def _extract_target_transitions(
        self, routing_decision: Dict[str, Any], transition_id: str
    ) -> List[str]:
        """
        Extract target transitions from routing decision.

        Supports both formats:
        - Legacy single: {"target_transition": "transition_id"}
        - New multiple: {"target_transitions": ["transition_1", "transition_2"]}

        Args:
            routing_decision: The routing decision dictionary
            transition_id: Current transition ID for logging

        Returns:
            List of target transition IDs
        """
        # Check for multiple transitions format first (new format)
        if "target_transitions" in routing_decision:
            target_transitions = routing_decision["target_transitions"]
            if isinstance(target_transitions, list):
                # Filter out None/empty values
                valid_transitions = [t for t in target_transitions if t]
                self.logger.debug(
                    f"Found multiple target transitions for {transition_id}: {valid_transitions}"
                )
                return valid_transitions
            else:
                self.logger.warning(
                    f"target_transitions is not a list for transition {transition_id}: {target_transitions}"
                )

        # Check for single transition format (legacy format)
        if "target_transition" in routing_decision:
            target_transition = routing_decision["target_transition"]
            if target_transition:
                self.logger.debug(
                    f"Found single target transition for {transition_id}: {target_transition}"
                )
                return [target_transition]
            else:
                self.logger.warning(
                    f"target_transition is empty for transition {transition_id}"
                )

        # No valid transitions found
        self.logger.warning(
            f"No target_transition or target_transitions found in routing decision for {transition_id}"
        )
        return []

    def _extract_unreachable_end_transitions(
        self,
        execution_result: Dict[str, Any],
        transition: Dict[str, Any],
        transition_id: str,
    ) -> List[str]:
        """
        Extract end transitions that become unreachable due to conditional routing decisions.

        When a conditional node executes and routes to specific paths, any end transitions
        specified in the 'ends_at' fields of non-matching conditions become unreachable
        and should be removed from the workflow's end transition tracking.

        Args:
            execution_result: The execution result from conditional component
            transition: The transition configuration that was executed
            transition_id: Current transition ID for logging

        Returns:
            List of end transition IDs that are now unreachable
        """
        unreachable_end_transitions = []

        # Get the original conditions from the transition configuration
        node_info = transition.get("node_info", {})
        tools_to_use = node_info.get("tools_to_use", [])

        # Find the conditional tool configuration
        conditional_tool = None
        for tool in tools_to_use:
            if tool.get("tool_name") == "conditional":
                conditional_tool = tool
                break

        if not conditional_tool:
            self.logger.debug(
                f"No conditional tool found in transition {transition_id}"
            )
            return []

        # Get the conditions and routing decision
        tool_parameters = conditional_tool.get("tool_parameters", {})
        conditions = tool_parameters.get("conditions", [])
        routing_decision = execution_result.get("routing_decision", {})

        # Determine which conditions were matched
        matched_conditions = self._get_matched_conditions(routing_decision)

        # Extract ends_at from non-matching conditions
        for i, condition in enumerate(conditions):
            condition_index = i + 1  # Conditions are 1-indexed

            if condition_index not in matched_conditions:
                # This condition was not matched, check for ends_at field
                ends_at = condition.get("ends_at", [])
                if ends_at:
                    if isinstance(ends_at, str):
                        ends_at = [ends_at]  # Convert single string to list

                    unreachable_end_transitions.extend(ends_at)
                    self.logger.debug(
                        f"Condition {condition_index} not matched, marking end transitions as unreachable: {ends_at}"
                    )

        # Remove duplicates and return
        return list(set(unreachable_end_transitions))

    def _get_matched_conditions(self, routing_decision: Dict[str, Any]) -> List[int]:
        """
        Extract which conditions were matched from the routing decision.

        Args:
            routing_decision: The routing decision from conditional component result

        Returns:
            List of condition indices (1-based) that were matched
        """
        matched_conditions = []

        # Check for single condition match (legacy format)
        matched_condition = routing_decision.get("matched_condition")
        if matched_condition is not None:
            if isinstance(matched_condition, int):
                matched_conditions.append(matched_condition)
            elif isinstance(matched_condition, list):
                matched_conditions.extend(matched_condition)

        # Check for multiple condition matches (new format)
        matched_conditions_list = routing_decision.get("matched_conditions", [])
        if matched_conditions_list:
            matched_conditions.extend(matched_conditions_list)

        # Remove duplicates and return
        return list(set(matched_conditions))

    def _log_routing_decision(
        self,
        execution_result: Dict[str, Any],
        transition_id: str,
        target_transitions: List[str],
    ) -> None:
        """
        Log the routing decision details for debugging and monitoring.

        Args:
            execution_result: The full execution result
            transition_id: Current transition ID
            target_transitions: List of target transition IDs
        """
        routing_decision = execution_result.get("routing_decision", {})
        metadata = execution_result.get("metadata", {})

        # Extract key information for logging
        matched_condition = routing_decision.get("matched_condition")
        condition_result = routing_decision.get("condition_result", False)
        execution_time = routing_decision.get("execution_time_ms", 0)
        total_conditions = metadata.get("total_conditions", 0)

        # Log the routing decision
        if len(target_transitions) == 1:
            self.logger.info(
                f"🔀 Conditional routing for {transition_id}: "
                f"routing to {target_transitions[0]} "
                f"(condition: {matched_condition}, result: {condition_result}, "
                f"time: {execution_time:.1f}ms, total_conditions: {total_conditions})"
            )
        else:
            self.logger.info(
                f"🔀 Conditional routing for {transition_id}: "
                f"routing to {len(target_transitions)} transitions {target_transitions} "
                f"(conditions: {matched_condition}, result: {condition_result}, "
                f"time: {execution_time:.1f}ms, total_conditions: {total_conditions})"
            )

        # Log additional metadata if available
        if "evaluation_order" in metadata:
            self.logger.debug(
                f"🔀 Evaluation order for {transition_id}: {metadata['evaluation_order']}"
            )

        if "evaluation_strategy" in metadata:
            self.logger.debug(
                f"🔀 Evaluation strategy for {transition_id}: {metadata['evaluation_strategy']}"
            )
