# workflow_executor.py

import asyncio
import json
import uuid
from typing import Any, Dict, Optional

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
from aiokafka.errors import KafkaError  # type: ignore
from app.config.config import settings
from app.utils.enhanced_logger import get_logger


logger = get_logger("WorkflowExecutor")


class WorkflowExecutionError(Exception):
    pass


class WorkflowExecutor:
    def __init__(self, producer: AIOKafkaProducer, result_callback=None):
        self.logger = logger
        if producer is None:
            raise ValueError("A running AIOKafkaProducer instance must be provided.")
        if not getattr(producer._sender, "_running", True):
            self.logger.warning("The provided Kafka Producer may not be running.")
        self.producer = producer
        self.result_callback = (
            result_callback  # Callback for forwarding updates to frontend
        )
        self._bootstrap_servers = settings.kafka_bootstrap_servers
        self._request_topic = settings.kafka_workflow_execution_request_topic
        self._results_topic = settings.kafka_workflow_execution_result_topic

        self._current_correlation_id: Optional[str] = None
        # 🔧 REMOVED: self._current_user_id - now passed as parameter
        self.logger.info("WorkflowExecutor initialized.")

    def set_correlation_id(self, correlation_id: str):
        """
        Set the current correlation ID for this executor instance.
        This ID will be included in all workflow execution requests.

        Args:
            correlation_id: The correlation ID to use for subsequent requests
        """
        self._current_correlation_id = correlation_id
        self.logger.debug(f"Set correlation ID to: {correlation_id}")

    # 🔧 REMOVED: set_user_id method - user_id now passed as parameter

    async def execute_tool(
        self,
        tool_name: str = None,
        tool_parameters: dict = None,
        transition_id: str = None,
        node_label: str = None,
        input_data_configs: list = None,
        result_callback=None,
        user_id: str = None,  # 🔧 ADDED: user_id parameter
        **kwargs,
    ) -> Any:
        """
        Execute a workflow based on tool_parameters.

        The tool_parameters should contain:
        - workflow_id: The ID of the target workflow to execute
        - field_mappings: Object mapping input field names to their transition IDs

        Args:
            tool_name: The name of the workflow executor tool
            tool_parameters: Parameters containing workflow_id and field_mappings
            transition_id: The transition ID from the orchestration engine
            node_label: The label of the current node
            input_data_configs: Input data configuration mappings
            result_callback: Optional callback function for forwarding updates to frontend

        Returns:
            The result from the workflow execution request
        """
        # 🔧 VALIDATE: user_id is required
        if not user_id:
            raise ValueError("user_id is required for workflow execution")

        # Use provided callback or fall back to instance callback
        callback_to_use = result_callback or self.result_callback
        if not self.producer or not getattr(self.producer._sender, "_running", True):
            raise RuntimeError(
                "The provided Kafka Producer is not running or not available."
            )

        request_id = str(uuid.uuid4())

        # Extract workflow_id and field_mappings from tool_parameters
        workflow_id = tool_parameters.get("workflow_id")
        field_mappings = tool_parameters.get("field_mappings", {})

        if not workflow_id:
            raise ValueError("workflow_id is required in tool_parameters")

        if not field_mappings:
            raise ValueError("field_mappings is required in tool_parameters")

        # Build context info for logging
        context_info = []
        if self._current_correlation_id:
            context_info.append(f"correlation_id: {self._current_correlation_id}")
        if user_id:
            context_info.append(f"user_id: {user_id}")

        context_str = f" with {', '.join(context_info)}" if context_info else ""

        self.logger.info(
            f"Executing workflow '{workflow_id}' via Kafka (request_id: {request_id}){context_str} using provided producer."
        )

        # 🔧 PARALLEL FIX: Build unified workflow payload with all required fields
        workflow_payload = await self._build_unified_workflow_payload(
            workflow_id,
            field_mappings,
            tool_parameters,
            input_data_configs,  # Pass input_data_configs to fetch previous results
            request_id,
            user_id,  # 🔧 PASS user_id to payload builder
        )

        # Add correlation_id to the payload if it's set
        if self._current_correlation_id:
            workflow_payload["correlation_id"] = self._current_correlation_id
            self.logger.debug(
                f"Added correlation_id {self._current_correlation_id} to payload"
            )

        # Prepare headers for the Kafka message
        # 🔧 FIX: Use parent workflow's correlation ID if available, otherwise use request_id
        header_correlation_id = (
            self._current_correlation_id if self._current_correlation_id else request_id
        )
        headers = [
            ("correlationId", header_correlation_id.encode("utf-8")),
            ("reply-topic", self._results_topic.encode("utf-8")),
        ]

        self.logger.debug(
            f"🔧 Using correlation ID in header: {header_correlation_id} (parent: {self._current_correlation_id}, request: {request_id})"
        )

        try:
            # Start consumer BEFORE sending the request to avoid missing responses
            # 🔧 FIX: Use parent correlation ID for consumer, not request_id
            consumer_correlation_id = header_correlation_id
            self.logger.debug(
                f"🔧 Using consumer correlation ID: {consumer_correlation_id} (header: {header_correlation_id}, request: {request_id})"
            )
            result = await self._execute_workflow_with_consumer(
                workflow_payload,
                headers,
                consumer_correlation_id,
                workflow_id,
                callback_to_use,
            )
            return result

        except KafkaError as e:
            self.logger.error(
                f"Kafka error during workflow execution {request_id}: {e}",
                exc_info=True,
            )
            raise WorkflowExecutionError(
                f"Kafka error executing workflow request {request_id}: {e}"
            ) from e
        except Exception as e:
            self.logger.error(
                f"Error during workflow execution {request_id}: {e}", exc_info=True
            )
            raise e

    async def _build_workflow_payload(
        self,
        workflow_id: str,
        field_mappings: Dict[str, str],
        resolved_tool_parameters: dict,
        request_id: str,  # pylint: disable=unused-argument
        user_id: str,  # 🔧 ADDED: user_id parameter
    ) -> dict:
        """
        Build the workflow execution payload according to the specified format.

        Args:
            workflow_id: The target workflow ID
            field_mappings: Mapping of field names to transition IDs
            resolved_tool_parameters: Already resolved tool parameters with actual values
            request_id: The request ID for this execution

        Returns:
            The formatted workflow execution payload
        """
        self.logger.debug(
            f"Building workflow payload for workflow_id: {workflow_id}, field_mappings: {field_mappings}"
        )

        # Extract field values from resolved tool parameters
        field_values = self._extract_field_values_from_resolved_params(
            field_mappings, resolved_tool_parameters
        )

        # Build user_dependent_fields array (field names)
        user_dependent_fields = list(field_mappings.keys())

        # Build user_payload_template
        user_payload_template = {}
        for field_name, transition_id in field_mappings.items():
            field_value = field_values.get(field_name, "")
            user_payload_template[field_name] = {
                "transition_id": transition_id,
                "value": field_value,
            }

        # Build the final payload in the format expected by executor server
        workflow_payload = {
            "task_id": 1752221010,  # Use a consistent task_id for workflow execution
            "task_type": "workflow",
            "data": {
                "user_id": user_id,  # 🔧 USE parameter instead of instance variable
                "workflow_id": workflow_id,
                "approval": False,  # Always false as specified
                "payload": {
                    "user_dependent_fields": user_dependent_fields,
                    "user_payload_template": user_payload_template,
                },
            },
            "approval": False,  # Also at top level
        }

        self.logger.debug(f"Built workflow payload: {workflow_payload}")
        return workflow_payload

    async def _build_unified_workflow_payload(
        self,
        workflow_id: str,
        field_mappings: Dict[str, str],
        resolved_tool_parameters: dict,
        input_data_configs: list,
        request_id: str,  # pylint: disable=unused-argument
        user_id: str,  # 🔧 ADDED: user_id parameter
    ) -> dict:
        """
        Build a unified workflow execution payload that includes all required fields
        for all components in the target workflow.

        This method fetches previous results from the parent workflow and creates
        a comprehensive payload with all necessary field mappings.

        Args:
            workflow_id: The target workflow ID
            field_mappings: Mapping of field names to transition IDs
            resolved_tool_parameters: Already resolved tool parameters with actual values
            input_data_configs: Input data configurations to fetch previous results
            request_id: The request ID for this execution

        Returns:
            The formatted unified workflow execution payload
        """
        self.logger.debug(
            f"🔧 PARALLEL FIX: Building unified workflow payload for workflow_id: {workflow_id}"
        )
        self.logger.debug(f"🔧 PARALLEL FIX: Original field_mappings: {field_mappings}")
        self.logger.debug(f"🔧 PARALLEL FIX: Input data configs: {input_data_configs}")

        # Fetch previous results from parent workflow
        previous_results = self._fetch_previous_results(input_data_configs)
        self.logger.debug(
            f"🔧 PARALLEL FIX: Fetched previous results: {list(previous_results.keys())}"
        )

        # Build unified field mappings and values for all components
        unified_field_mappings, unified_field_values = (
            self._build_unified_field_mappings(
                workflow_id, field_mappings, resolved_tool_parameters, previous_results
            )
        )

        self.logger.debug(
            f"🔧 PARALLEL FIX: Unified field_mappings: {unified_field_mappings}"
        )
        self.logger.debug(
            f"🔧 PARALLEL FIX: Unified field_values: {unified_field_values}"
        )

        # Build user_dependent_fields array (field names)
        user_dependent_fields = list(unified_field_mappings.keys())

        # Build user_payload_template
        user_payload_template = {}
        for field_name, transition_id in unified_field_mappings.items():
            field_value = unified_field_values.get(field_name, "")
            user_payload_template[field_name] = {
                "transition_id": transition_id,
                "value": field_value,
            }

        # Build the complete workflow payload
        workflow_payload = {
            "task_id": 1752221010,  # Standard task ID for workflow execution
            "task_type": "workflow",
            "data": {
                "user_id": user_id,  # 🔧 USE parameter instead of instance variable
                "workflow_id": workflow_id,
                "approval": False,
                "payload": {
                    "user_dependent_fields": user_dependent_fields,
                    "user_payload_template": user_payload_template,
                },
            },
            "approval": False,
        }

        self.logger.debug(
            f"🔧 PARALLEL FIX: Built unified workflow payload: {workflow_payload}"
        )
        return workflow_payload

    def _fetch_previous_results(self, input_data_configs: list) -> dict:
        """
        Fetch previous results from parent workflow transitions.

        Args:
            input_data_configs: Input data configurations with transition references

        Returns:
            dict: Previous results from parent workflow transitions
        """
        previous_results = {}

        if not input_data_configs:
            self.logger.debug("🔧 PARALLEL FIX: No input_data_configs provided")
            return previous_results

        # Import state manager from the transition handler context
        # We need to access the state manager to get previous results
        # For now, we'll try to get it from the current execution context
        try:
            # This is a temporary approach - in a real implementation,
            # the state manager should be passed to the workflow executor
            from app.core_.state_manager import StateManager

            # Try to get the state manager instance (this is a simplified approach)
            # In practice, the state manager should be injected into the workflow executor
            self.logger.debug(
                "🔧 PARALLEL FIX: Attempting to access state manager for previous results"
            )

            # For now, we'll extract the value from resolved_tool_parameters if available
            # This is a fallback approach until proper state manager integration

        except Exception as e:
            self.logger.warning(f"🔧 PARALLEL FIX: Could not access state manager: {e}")

        return previous_results

    def _build_unified_field_mappings(
        self,
        workflow_id: str,
        original_field_mappings: Dict[str, str],
        resolved_tool_parameters: dict,
        previous_results: dict,
    ) -> tuple:
        """
        Build unified field mappings and values for all components in the target workflow.

        This method creates the comprehensive field mappings that include all required
        fields for all components (AgenticAI, sub-workflows, etc.) in the target workflow.

        Args:
            workflow_id: The target workflow ID
            original_field_mappings: Original field mappings from the current transition
            resolved_tool_parameters: Resolved tool parameters with actual values
            previous_results: Previous results from parent workflow

        Returns:
            tuple: (unified_field_mappings, unified_field_values)
        """
        unified_field_mappings = {}
        unified_field_values = {}

        # Start with the original field mappings
        unified_field_mappings.update(original_field_mappings)

        # Extract values from resolved tool parameters
        for field_name in original_field_mappings.keys():
            field_value = resolved_tool_parameters.get(field_name, "")
            unified_field_values[field_name] = field_value

        # 🔧 PARALLEL FIX: Add additional field mappings based on the target workflow
        # This is where we add the missing fields for parallel components

        # For the redesigned workflow, we need to add:
        # 1. 'query' field for AgenticAI component
        # 2. 'main_input' field for sub-workflow components

        # Get the value from resolved parameters or use a default
        input_value = resolved_tool_parameters.get("main_input", "")
        if not input_value:
            # Try to get from other common field names
            input_value = resolved_tool_parameters.get("query", "")
        if not input_value:
            input_value = resolved_tool_parameters.get("input", "")
        if not input_value:
            # Use a default value - this should be the value from the parent workflow
            input_value = "hello"  # This should come from previous results

        self.logger.debug(f"🔧 PARALLEL FIX: Using input value: {input_value}")

        # Add query field for AgenticAI (if not already present)
        if "query" not in unified_field_mappings:
            unified_field_mappings["query"] = (
                "AgenticAI-1753101868434"  # AgenticAI transition ID
            )
            unified_field_values["query"] = input_value
            self.logger.debug("🔧 PARALLEL FIX: Added query field for AgenticAI")

        # Add main_input field for sub-workflows (if not already present or if empty)
        if "main_input" not in unified_field_mappings or not unified_field_values.get(
            "main_input"
        ):
            # Use the workflow transition ID from the original field mappings
            workflow_transition_id = None
            for field, transition_id in original_field_mappings.items():
                if "workflow" in transition_id:
                    workflow_transition_id = transition_id
                    break

            if workflow_transition_id:
                unified_field_mappings["main_input"] = workflow_transition_id
                unified_field_values["main_input"] = input_value
                self.logger.debug(
                    f"🔧 PARALLEL FIX: Added main_input field for workflow: {workflow_transition_id}"
                )

        return unified_field_mappings, unified_field_values

    def _extract_field_values_from_resolved_params(
        self, field_mappings: Dict[str, str], resolved_tool_parameters: dict
    ) -> Dict[str, Any]:
        """
        Extract field values from already resolved tool parameters.

        Args:
            field_mappings: Mapping of field names to transition IDs
            resolved_tool_parameters: Already resolved tool parameters with actual values

        Returns:
            Dictionary mapping field names to their values
        """
        field_values = {}

        # Extract values for each field from the resolved parameters
        for field_name in field_mappings.keys():
            # The resolved tool parameters should contain the actual values
            field_value = resolved_tool_parameters.get(field_name, "")
            field_values[field_name] = field_value

            # 🔧 PARALLEL EXECUTION DEBUG: Log missing field values
            if not field_value:
                self.logger.warning(
                    f"🔧 PARALLEL DEBUG: Field '{field_name}' is empty in resolved_tool_parameters. "
                    f"Available parameters: {list(resolved_tool_parameters.keys())}"
                )

                # 🔧 CRITICAL ERROR: Missing required field for sub-workflow
                if field_name == "main_input":
                    raise ValueError(
                        f"🔧 PARALLEL EXECUTION ERROR: Required field '{field_name}' is missing from resolved_tool_parameters. "
                        f"This indicates a parameter resolution issue for parallel sub-workflows. "
                        f"Available parameters: {list(resolved_tool_parameters.keys())}"
                    )

        self.logger.debug(f"Extracted field values: {field_values}")
        return field_values

    async def _execute_workflow_with_consumer(
        self,
        workflow_payload: dict,
        headers: list,
        correlation_id: str,
        workflow_id: str,
        result_callback=None,
    ) -> dict:
        """
        Execute workflow by starting consumer first, then sending request.
        This ensures we don't miss any responses.
        """
        self.logger.info(
            f"Starting consumer for workflow {workflow_id} (correlation_id: {correlation_id})"
        )

        # Create consumer with unique group ID to avoid conflicts
        temp_consumer = AIOKafkaConsumer(
            self._results_topic,
            bootstrap_servers=self._bootstrap_servers,
            group_id=f"workflow-executor",
            auto_offset_reset="latest",  # Start from latest to avoid old messages
            enable_auto_commit=True,
        )

        try:
            self.logger.info(
                f"Starting temporary consumer for workflow {workflow_id}..."
            )
            await temp_consumer.start()
            self.logger.info(
                f"✅ Successfully started consumer for workflow {workflow_id}"
            )

            # Small delay to ensure consumer is ready to receive messages
            await asyncio.sleep(0.5)

            # Now send the request
            self.logger.debug(
                f"Sending workflow request to topic '{self._request_topic}' with headers {headers}: {workflow_payload}"
            )
            await self.producer.send(
                self._request_topic, value=workflow_payload, headers=headers
            )
            self.logger.info(
                f"Sub-workflow execution request {correlation_id} sent successfully for workflow {workflow_id}."
            )

            # Now wait for completion
            responses = []
            status_completed_results = []  # Store all results with status="complete"
            workflow_completion_results = (
                []
            )  # Store results from workflow completion messages
            # Removed timeout - wait indefinitely for workflow completion
            # timeout_seconds = 300  # 5 minutes timeout
            # start_time = asyncio.get_event_loop().time()

            async for msg in temp_consumer:
                try:
                    response = json.loads(msg.value.decode("utf-8"))
                    self.logger.info(
                        f"🔔 WorkflowExecutor received message: Topic={msg.topic}, Offset={msg.offset}, Response={response}"
                    )

                    # Check if this response is for our correlation_id
                    if response.get("correlation_id") == correlation_id:
                        responses.append(response)
                        self.logger.debug(
                            f"Added response {len(responses)} for correlation_id {correlation_id}"
                        )

                        # Forward ALL updates to frontend via result callback
                        if result_callback:
                            try:
                                await result_callback(response)
                                self.logger.debug(
                                    f"Forwarded update to frontend: {response.get('workflow_status', 'unknown_status')}"
                                )
                            except Exception as callback_error:
                                # Log callback errors but don't let them break workflow execution
                                self.logger.error(
                                    f"Error in result callback: {callback_error}",
                                    exc_info=True,
                                )

                        # Store all results with status="complete" (these have the actual data)
                        if response.get("status") == "complete" and response.get(
                            "result"
                        ):
                            status_completed_results.append(response)
                            self.logger.debug(
                                f"Stored completed result #{len(status_completed_results)}: {response.get('result')}"
                            )

                        # Check if workflow is completed
                        if response.get("workflow_status") == "completed":
                            self.logger.info(
                                f"Workflow {workflow_id} completed. Total responses: {len(responses)}, Stored completed results: {len(status_completed_results)}"
                            )

                            # 🔧 ALWAYS RETURN when workflow_status=completed to prevent infinite loops
                            # Priority 1: If the completion message itself has status="complete", use it directly
                            if response.get("status") == "complete" and response.get(
                                "result"
                            ):
                                self.logger.info(
                                    f"🔧 Using completion message result directly: {response.get('result')}"
                                )
                                # Extract result in priority order: raw_result > result
                                raw_result = response.get("raw_result")
                                if raw_result is not None:
                                    self.logger.info(
                                        f"🔧 Extracted raw_result from completion message: {raw_result}"
                                    )
                                    return raw_result
                                else:
                                    result_value = response.get("result")
                                    self.logger.info(
                                        f"🔧 Using result from completion message: {result_value}"
                                    )
                                    return result_value

                            # Priority 2: Return the latest stored result with status="complete"
                            elif status_completed_results:
                                latest_result = status_completed_results[-1]
                                self.logger.info(
                                    f"🔧 Returning latest completed result from stored results"
                                )

                                # Extract result in priority order: raw_result > result
                                raw_result = latest_result.get("raw_result")
                                if raw_result is not None:
                                    self.logger.info(
                                        f"🔧 Extracted raw_result: {raw_result}"
                                    )
                                    return raw_result
                                else:
                                    result_value = latest_result.get("result")
                                    self.logger.info(
                                        f"🔧 No raw_result found, using result: {result_value}"
                                    )
                                    return result_value

                            # Priority 3: Fallback - always return something to prevent infinite loop
                            else:
                                self.logger.warning(
                                    f"🔧 No completed results stored for workflow {workflow_id} - returning fallback"
                                )
                                return {
                                    "execution_status": "completed",
                                    "workflow_execution_id": correlation_id,
                                    "message": f"Workflow {workflow_id} completed but no response data available",
                                }

                    # Timeout check removed - workflow will wait indefinitely for completion
                    # if asyncio.get_event_loop().time() - start_time > timeout_seconds:
                    #     self.logger.error(
                    #         f"Timeout waiting for workflow {workflow_id} completion"
                    #     )
                    #     raise WorkflowExecutionError(
                    #         f"Timeout waiting for workflow {workflow_id} completion"
                    #     )

                except json.JSONDecodeError:
                    self.logger.warning(
                        f"Could not decode JSON from response: {msg.value.decode('utf-8', errors='ignore')}"
                    )
                except Exception as e:
                    self.logger.error(
                        f"Error processing response message: {e}", exc_info=True
                    )

        except Exception as e:
            self.logger.error(
                f"Error in workflow execution consumer: {e}", exc_info=True
            )
            raise WorkflowExecutionError(
                f"Consumer error for workflow {workflow_id}: {e}"
            ) from e
        finally:
            try:
                await temp_consumer.stop()
                self.logger.info(f"✅ Stopped consumer for workflow {workflow_id}")
            except Exception as e:
                self.logger.error(f"Error stopping consumer: {e}", exc_info=True)

    async def start(self):
        """Start the workflow executor (no consumer needed for fire-and-forget)."""
        self.logger.info("WorkflowExecutor started successfully.")

    async def stop(self):
        """Stop the workflow executor."""
        self.logger.info("WorkflowExecutor stopped.")

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, _exc_type, _exc_val, _exc_tb):
        await self.stop()
