#!/usr/bin/env python3
"""
Debug script to trace the complete flow of dropdown rendering
from backend schema conversion to frontend input generation.
"""

import json
import sys
import os

# Add the workflow-service directory to Python path
sys.path.append('/Users/<USER>/Desktop/ruh_ai/backend/workflow-service')

from typing import Dict, Any, Type
from typing import Literal
from pydantic import BaseModel, create_model, Field

# Import the schema conversion function we just modified
from app.utils.workflow_builder.schema_conversion import create_input_schema_from_json_schema, pydantic_to_node_inputs

def test_schema_conversion():
    """Test the backend schema conversion with the actual Google Docs format field."""
    
    print("=== Testing Backend Schema Conversion ===")
    
    # Extract the create_document schema from saved_config.json
    create_document_schema = {
        "type": "object",
        "properties": {
            "title": {
                "anyOf": [
                    {"type": "string"},
                    {"type": "null"}
                ],
                "default": None,
                "title": "Title"
            },
            "content": {
                "anyOf": [
                    {"type": "string"},
                    {"type": "null"}
                ],
                "default": None,
                "title": "Content"
            },
            "format": {
                "anyOf": [
                    {
                        "enum": ["plain", "html", "markdown"],
                        "type": "string"
                    },
                    {"type": "null"}
                ],
                "default": "plain",
                "title": "Format"
            }
        },
        "title": "CreateDocument"
    }
    
    print("Input schema:")
    print(json.dumps(create_document_schema, indent=2))
    
    try:
        # Test the schema conversion
        print("\n--- Step 1: Converting JSON Schema to Pydantic Model ---")
        pydantic_model = create_input_schema_from_json_schema(create_document_schema, "CreateDocumentModel")
        
        print(f"Created Pydantic model: {pydantic_model.__name__}")
        print("Model fields:")
        for field_name, field_info in pydantic_model.__fields__.items():
            field_type = field_info.annotation
            field_default = field_info.default if field_info.default is not ... else "No default"
            print(f"  - {field_name}: {field_type} (default: {field_default})")
            
            # Check if it's a Literal type (enum)
            if hasattr(field_type, "__origin__") and field_type.__origin__ is Literal:
                print(f"    -> This is a Literal type with values: {field_type.__args__}")
        
        print("\n--- Step 2: Converting Pydantic Model to Node Inputs ---")
        node_inputs = pydantic_to_node_inputs(pydantic_model, make_handles=False)
        
        print("Generated node inputs:")
        for input_def in node_inputs:
            print(f"  - {input_def.name} ({input_def.__class__.__name__}):")
            print(f"    display_name: {input_def.display_name}")
            print(f"    value: {input_def.value}")
            if hasattr(input_def, 'options'):
                print(f"    options: {getattr(input_def, 'options', 'None')}")
            print()
        
        return node_inputs
        
    except Exception as e:
        print(f"ERROR in backend conversion: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_frontend_conversion(node_inputs):
    """Test how the frontend would process these inputs."""
    
    print("\n=== Testing Frontend Input Processing ===")
    
    if not node_inputs:
        print("No inputs to test (backend conversion failed)")
        return
    
    # Simulate what the frontend MCPMarketplaceInputGenerator would receive
    print("Simulating frontend input processing...")
    
    # Find the format input
    format_input = None
    for inp in node_inputs:
        if inp.name == "format":
            format_input = inp
            break
    
    if format_input:
        print(f"Found format input:")
        print(f"  Type: {type(format_input).__name__}")
        print(f"  Value: {format_input.value}")
        if hasattr(format_input, 'options'):
            print(f"  Options: {format_input.options}")
        
        # Check what input_type would be assigned
        if hasattr(format_input, 'input_type'):
            print(f"  Input Type: {format_input.input_type}")
        
        # Test if it would be recognized as a dropdown
        if type(format_input).__name__ == "DropdownInput":
            print("  ✅ Correctly identified as DropdownInput")
        else:
            print(f"  ❌ Incorrectly identified as {type(format_input).__name__}")
    else:
        print("❌ Format input not found!")

def test_schema_property_directly():
    """Test the schema conversion logic directly on the format property."""
    
    print("\n=== Testing Schema Property Conversion Directly ===")
    
    format_schema = {
        "anyOf": [
            {
                "enum": ["plain", "html", "markdown"],
                "type": "string"
            },
            {"type": "null"}
        ],
        "default": "plain",
        "title": "Format"
    }
    
    print("Format schema:")
    print(json.dumps(format_schema, indent=2))
    
    # Test the logic we added to create_input_schema_from_json_schema
    print("\nTesting anyOf logic:")
    
    if "anyOf" in format_schema:
        print("Found anyOf structure")
        
        # Look for the first non-null option that has an enum
        for i, option in enumerate(format_schema["anyOf"]):
            print(f"  Option {i}: {option}")
            if option.get("type") != "null" and "enum" in option:
                enum_values = option["enum"]
                print(f"    -> Found enum with values: {enum_values}")
                print(f"    -> Would create Literal[{tuple(enum_values)}]")
                break
        else:
            print("    -> No enum found in anyOf options")

if __name__ == "__main__":
    print("Debugging Dropdown Flow for Google Docs Format Field")
    print("=" * 60)
    
    # Test the schema conversion directly
    test_schema_property_directly()
    
    # Test the complete backend conversion
    node_inputs = test_schema_conversion()
    
    # Test the frontend processing
    test_frontend_conversion(node_inputs)
    
    print("\n" + "=" * 60)
    print("Debug complete!")