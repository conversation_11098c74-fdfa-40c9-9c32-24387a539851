{"nodes": [{"id": "CombineTextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "CombineTextComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "string", "description": "The main text or list to combine. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional text inputs to show (1-10)."}, "required": false}, {"field_name": "separator", "data_type": {"type": "string", "description": "The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "string", "description": "Text for input 1. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "string", "description": "Text for input 2. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "string", "description": "Text for input 3. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "string", "description": "Text for input 4. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "string", "description": "Text for input 5. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "string", "description": "Text for input 6. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "string", "description": "Text for input 7. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "string", "description": "Text for input 8. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "string", "description": "Text for input 9. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "string", "description": "Text for input 10. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "DelayComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "DelayComponent", "input_schema": {"predefined_fields": [{"field_name": "input_data", "data_type": {"type": "string", "description": "The input data to be passed through."}, "required": true}, {"field_name": "delay_seconds", "data_type": {"type": "string", "description": "The number of seconds to pause the workflow."}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "output", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "Message", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "MergeDataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "MergeDataComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "object", "description": "The main data structure to merge. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional inputs to show (1-10)."}, "required": false}, {"field_name": "merge_strategy", "data_type": {"type": "string", "description": "How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs."}, "required": false}, {"field_name": "output_key_1", "data_type": {"type": "string", "description": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_2", "data_type": {"type": "string", "description": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_3", "data_type": {"type": "string", "description": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_4", "data_type": {"type": "string", "description": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_5", "data_type": {"type": "string", "description": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_6", "data_type": {"type": "string", "description": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_7", "data_type": {"type": "string", "description": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_8", "data_type": {"type": "string", "description": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_9", "data_type": {"type": "string", "description": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_10", "data_type": {"type": "string", "description": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_11", "data_type": {"type": "string", "description": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "object", "description": "Data structure 1 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "object", "description": "Data structure 2 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "object", "description": "Data structure 3 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "object", "description": "Data structure 4 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "object", "description": "Data structure 5 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "object", "description": "Data structure 6 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "object", "description": "Data structure 7 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "object", "description": "Data structure 8 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "object", "description": "Data structure 9 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "object", "description": "Data structure 10 to merge. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_data", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}], "transitions": [{"id": "transition-CombineTextComponent-1753181128777", "sequence": 1, "transition_type": "initial", "execution_type": "Components", "node_label": "branch 1", "node_info": {"node_id": "CombineTextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "CombineTextComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "string", "field_value": ""}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": 1}, {"field_name": "separator", "data_type": "string", "field_value": ""}, {"field_name": "input_1", "data_type": "string", "field_value": "hello1"}, {"field_name": "input_2", "data_type": "string", "field_value": ""}, {"field_name": "input_3", "data_type": "string", "field_value": ""}, {"field_name": "input_4", "data_type": "string", "field_value": ""}, {"field_name": "input_5", "data_type": "string", "field_value": ""}, {"field_name": "input_6", "data_type": "string", "field_value": ""}, {"field_name": "input_7", "data_type": "string", "field_value": ""}, {"field_name": "input_8", "data_type": "string", "field_value": ""}, {"field_name": "input_9", "data_type": "string", "field_value": ""}, {"field_name": "input_10", "data_type": "string", "field_value": ""}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-DelayComponent-1753181382571", "target_node_id": "Wait / Delay", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "result", "result_path": "result", "edge_id": "reactflow__edge-CombineTextComponent-1753181128777result-DelayComponent-1753181382571input_data"}]}}]}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "main_input", "handle_name": "Main Input", "data_type": "string", "required": false, "description": "The main text or list to combine. Can be connected from another node or entered directly."}, {"handle_id": "input_1", "handle_name": "Input 1", "data_type": "string", "required": false, "description": "Text for input 1. Can be connected from another node or entered directly."}, {"handle_id": "input_2", "handle_name": "Input 2", "data_type": "string", "required": false, "description": "Text for input 2. Can be connected from another node or entered directly."}, {"handle_id": "input_3", "handle_name": "Input 3", "data_type": "string", "required": false, "description": "Text for input 3. Can be connected from another node or entered directly."}, {"handle_id": "input_4", "handle_name": "Input 4", "data_type": "string", "required": false, "description": "Text for input 4. Can be connected from another node or entered directly."}, {"handle_id": "input_5", "handle_name": "Input 5", "data_type": "string", "required": false, "description": "Text for input 5. Can be connected from another node or entered directly."}, {"handle_id": "input_6", "handle_name": "Input 6", "data_type": "string", "required": false, "description": "Text for input 6. Can be connected from another node or entered directly."}, {"handle_id": "input_7", "handle_name": "Input 7", "data_type": "string", "required": false, "description": "Text for input 7. Can be connected from another node or entered directly."}, {"handle_id": "input_8", "handle_name": "Input 8", "data_type": "string", "required": false, "description": "Text for input 8. Can be connected from another node or entered directly."}, {"handle_id": "input_9", "handle_name": "Input 9", "data_type": "string", "required": false, "description": "Text for input 9. Can be connected from another node or entered directly."}, {"handle_id": "input_10", "handle_name": "Input 10", "data_type": "string", "required": false, "description": "Text for input 10. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "result", "handle_name": "Combined Text", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"result": "result", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.result", "output_data.result", "response.result", "data.result", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "result"}}, "approval_required": false, "end": false}, {"id": "transition-DelayComponent-1753181382571", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_label": "Wait / Delay", "node_info": {"node_id": "DelayComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "DelayComponent", "tool_params": {"items": [{"field_name": "input_data", "data_type": "string", "field_value": null}, {"field_name": "delay_seconds", "data_type": "string", "field_value": "10"}]}}], "input_data": [{"from_transition_id": "transition-CombineTextComponent-1753181128777", "source_node_id": "Combine Text", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-CombineTextComponent-1753181128777", "source_handle_id": "result", "target_handle_id": "input_data", "edge_id": "reactflow__edge-CombineTextComponent-1753181128777result-DelayComponent-1753181382571input_data"}]}], "output_data": [{"to_transition_id": "transition-MergeDataComponent-1753182534767", "target_node_id": "Merge Data", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "output", "result_path": "output", "edge_id": "reactflow__edge-DelayComponent-1753181382571output-MergeDataComponent-1753182534767main_input"}]}}]}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "input_data", "handle_name": "Input Data", "data_type": "string", "required": true, "description": "The input data to be passed through."}, {"handle_id": "delay_seconds", "handle_name": "Delay (seconds)", "data_type": "string", "required": true, "description": "The number of seconds to pause the workflow."}], "output_handles": [{"handle_id": "output", "handle_name": "Output", "data_type": "string", "description": ""}, {"handle_id": "Message", "handle_name": "Message", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"output": "output", "Message": "Message", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.output", "output_data.output", "response.output", "data.output", "result.Message", "output_data.Message", "response.Message", "data.Message", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "output"}}, "approval_required": false, "end": false}, {"id": "transition-MergeDataComponent-1753182534767", "sequence": 3, "transition_type": "initial", "execution_type": "Components", "node_label": "Merge Data", "node_info": {"node_id": "MergeDataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "MergeDataComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "object", "field_value": null}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": 0}, {"field_name": "merge_strategy", "data_type": "string", "field_value": "Structured Compose"}, {"field_name": "output_key_1", "data_type": "string", "field_value": "hello"}, {"field_name": "output_key_2", "data_type": "string", "field_value": ""}, {"field_name": "output_key_3", "data_type": "string", "field_value": ""}, {"field_name": "output_key_4", "data_type": "string", "field_value": ""}, {"field_name": "output_key_5", "data_type": "string", "field_value": ""}, {"field_name": "output_key_6", "data_type": "string", "field_value": ""}, {"field_name": "output_key_7", "data_type": "string", "field_value": ""}, {"field_name": "output_key_8", "data_type": "string", "field_value": ""}, {"field_name": "output_key_9", "data_type": "string", "field_value": ""}, {"field_name": "output_key_10", "data_type": "string", "field_value": ""}, {"field_name": "output_key_11", "data_type": "string", "field_value": ""}, {"field_name": "input_1", "data_type": "object", "field_value": {}}, {"field_name": "input_2", "data_type": "object", "field_value": {}}, {"field_name": "input_3", "data_type": "object", "field_value": {}}, {"field_name": "input_4", "data_type": "object", "field_value": {}}, {"field_name": "input_5", "data_type": "object", "field_value": {}}, {"field_name": "input_6", "data_type": "object", "field_value": {}}, {"field_name": "input_7", "data_type": "object", "field_value": {}}, {"field_name": "input_8", "data_type": "object", "field_value": {}}, {"field_name": "input_9", "data_type": "object", "field_value": {}}, {"field_name": "input_10", "data_type": "object", "field_value": {}}]}}], "input_data": [{"from_transition_id": "transition-DelayComponent-1753181382571", "source_node_id": "Wait / Delay", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-DelayComponent-1753181382571", "source_handle_id": "output", "target_handle_id": "main_input", "edge_id": "reactflow__edge-DelayComponent-1753181382571output-MergeDataComponent-1753182534767main_input"}]}], "output_data": []}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "main_input", "handle_name": "Main Input", "data_type": "object", "required": true, "description": "The main data structure to merge. Can be connected from another node or entered directly."}, {"handle_id": "output_key_1", "handle_name": "Output Key 1", "data_type": "string", "required": false, "description": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_2", "handle_name": "Output Key 2", "data_type": "string", "required": false, "description": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_3", "handle_name": "Output Key 3", "data_type": "string", "required": false, "description": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_4", "handle_name": "Output Key 4", "data_type": "string", "required": false, "description": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_5", "handle_name": "Output Key 5", "data_type": "string", "required": false, "description": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_6", "handle_name": "Output Key 6", "data_type": "string", "required": false, "description": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_7", "handle_name": "Output Key 7", "data_type": "string", "required": false, "description": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_8", "handle_name": "Output Key 8", "data_type": "string", "required": false, "description": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_9", "handle_name": "Output Key 9", "data_type": "string", "required": false, "description": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_10", "handle_name": "Output Key 10", "data_type": "string", "required": false, "description": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_11", "handle_name": "Output Key 11", "data_type": "string", "required": false, "description": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy."}, {"handle_id": "input_1", "handle_name": "Input 1", "data_type": "object", "required": false, "description": "Data structure 1 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_2", "handle_name": "Input 2", "data_type": "object", "required": false, "description": "Data structure 2 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_3", "handle_name": "Input 3", "data_type": "object", "required": false, "description": "Data structure 3 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_4", "handle_name": "Input 4", "data_type": "object", "required": false, "description": "Data structure 4 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_5", "handle_name": "Input 5", "data_type": "object", "required": false, "description": "Data structure 5 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_6", "handle_name": "Input 6", "data_type": "object", "required": false, "description": "Data structure 6 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_7", "handle_name": "Input 7", "data_type": "object", "required": false, "description": "Data structure 7 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_8", "handle_name": "Input 8", "data_type": "object", "required": false, "description": "Data structure 8 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_9", "handle_name": "Input 9", "data_type": "object", "required": false, "description": "Data structure 9 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_10", "handle_name": "Input 10", "data_type": "object", "required": false, "description": "Data structure 10 to merge. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "output_data", "handle_name": "Merged Data", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"output_data": "output_data", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.output_data", "output_data.output_data", "response.output_data", "data.output_data", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "output_data"}}, "approval_required": false, "end": true}]}