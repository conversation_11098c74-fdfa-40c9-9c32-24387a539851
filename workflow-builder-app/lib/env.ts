/**
 * Environment variable validation
 * This module validates that all required environment variables are set
 */

// Required environment variables
const REQUIRED_ENV_VARS = [
  'NEXT_PUBLIC_API_URL',
  'NEXT_PUBLIC_WORKFLOW_EXECUTION_URL',
];

// Optional environment variables
const OPTIONAL_ENV_VARS = [
  'NEXT_PUBLIC_EXPLORE_WORKFLOWS_URL', // URL for the "Explore Workflows" button in sidebar
];

/**
 * Validates that all required environment variables are set
 * @throws Error if any required environment variable is missing
 */
export function validateEnv(): void {
  const missingVars = REQUIRED_ENV_VARS.filter(
    (envVar) => !process.env[envVar]
  );

  if (missingVars.length > 0) {
    // In development, provide a helpful error message
    if (process.env.NODE_ENV === 'development') {
      console.error(`
        ❌ Missing required environment variables:
        ${missingVars.map((v) => `   - ${v}`).join('\n')}

        Please create or update your .env file with these variables.
      `);
    }

    // Don't throw in development to allow the app to start with warnings
    if (process.env.NODE_ENV !== 'development') {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
  }
}

/**
 * Gets an environment variable with a fallback value
 * @param key The environment variable key
 * @param fallback The fallback value if the environment variable is not set
 * @returns The environment variable value or the fallback
 */
export function getEnvVar(key: string, fallback: string = ''): string {
  return process.env[key] || fallback;
}

/**
 * Gets the explore workflows URL from environment variables
 * @returns The explore workflows URL or a fallback
 */
export function getExploreWorkflowsUrl(): string {
  return process.env.NEXT_PUBLIC_MARKETPLACE_URL!;
}
