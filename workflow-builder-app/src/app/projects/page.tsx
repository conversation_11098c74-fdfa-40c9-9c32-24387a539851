'use client';
import React from 'react';
import { MainLayout } from "@/components/layout/MainLayout";

export default function ProjectSettingsPage() {
  return (
    <MainLayout>
      <div className="bg-background min-h-screen">
        {/* Main content */}
        <div className="container px-4 sm:px-8 lg:px-[100px] py-6 sm:py-8" style={{ maxWidth: "100%" }}>
          <div className="flex flex-1 flex-col items-center justify-center w-full min-h-[60vh]">
      {/* Custom Icon */}
      <div className="relative mb-8">
        <div className="w-32 h-32 flex items-center justify-center opacity-60">
          <img
            src="/project-empty.svg"
            alt="Project Icon"
            className="w-full h-full"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = 'flex';
            }}
          />
          {/* Fallback Icon */}
          <div className="w-32 h-32 flex items-center justify-center hidden">
            <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      {/* Title */}
      <h2 className="font-[Satoshi] text-gray-900 dark:text-white text-2xl font-bold mb-2">Create your first project</h2>
      
      {/* Description */}
      <p className="font-[Satoshi] text-gray-600 dark:text-[#A1A1AA] text-base mb-8 text-center max-w-md">
        Organize your workflows into projects to better manage and collaborate on your automation tasks.
      </p>
      
      {/* Add Project Button */}
      <button 
        className="bg-[#AE00D0] hover:bg-[#8B1FE0] text-white font-[Satoshi] font-medium rounded-lg px-8 py-3 text-base flex items-center gap-2 transition"
      >
        <span className="text-xl font-bold">+</span> Create project
      </button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
} 