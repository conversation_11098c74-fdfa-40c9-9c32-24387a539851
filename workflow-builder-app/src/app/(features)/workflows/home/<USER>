"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter } from "next/navigation";
import { getClientAccessToken } from "@/lib/clientCookies";
import Image from 'next/image';
import {
  fetchWorkflowsByUser,
  createEmptyWorkflow,
  WorkflowSummary,
  WorkflowDetails,
  duplicateWorkflow,
} from "../api";
import { useUserStore } from "@/store/userStore";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Workflow,
  Plus,
  Search,
  AlertCircle,
  Loader2,
  ChevronDown,
  FileEdit,
  File,
  FileUp,
} from "lucide-react";
import WorkflowCard from "../components/WorkflowCard";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";

export default function WorkflowsListPage() {
  const router = useRouter();
  const user = useUserStore((state) => state.user);
  const [mounted, setMounted] = useState(false);

  const [isRedirecting, setIsRedirecting] = useState(false);
  const [workflows, setWorkflows] = useState<WorkflowDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState<string>("updated_desc");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isCreatingWorkflow, setIsCreatingWorkflow] = useState(false);
  const [isDuplicatingId, setIsDuplicatingId] = useState<string | null>(null);
  const [duplicateDialogOpen, setDuplicateDialogOpen] = useState(false);
  const [duplicateWorkflowId, setDuplicateWorkflowId] = useState<string | null>(null);
  const [duplicateFormData, setDuplicateFormData] = useState({
    name: "",
    description: ""
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(9);

  // Add debounced search state
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>("");

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);



  // Check authentication immediately on mount
  useEffect(() => {
    const checkAuth = () => {
      const cookieToken = getClientAccessToken();
      const tokenFromStore = user?.accessToken;

      if (!cookieToken && !tokenFromStore) {
        console.error("No access token available - redirecting to login");
        setIsRedirecting(true);
        // Use direct window.location for more reliable redirect
        window.location.href = "/login";
      }
    };

    checkAuth();
  }, [user]);

  // Fetch workflows when dependencies change
  useEffect(() => {
    if (isRedirecting) return;

    const fetchWorkflows = async () => {
      const cookieToken = getClientAccessToken();
      const tokenFromStore = user?.accessToken;
      const accessToken = tokenFromStore || cookieToken;

      // Double-check token availability
      if (!accessToken) {
        console.error("No access token available");
        setIsRedirecting(true);
        window.location.href = "/login";
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await fetchWorkflowsByUser(currentPage, pageSize, accessToken, debouncedSearchTerm);

        setWorkflows(response?.data);

        // Update pagination state
        if (response.metadata) {
          setTotalPages(response.metadata.totalPages || 1);
          setCurrentPage(response.metadata.currentPage || 1);
        }
      } catch (err) {
        console.error("Failed to fetch workflows:", err);

        // Redirect to login for authentication errors
        if (err instanceof Error && (err.message.includes("401") || err.message.includes("403"))) {
          console.error("Authentication failed:", err);
          setIsRedirecting(true);
          window.location.href = "/login";
          return;
        }

        // Handle other errors
        if (err instanceof Error) {
          if (err.message.includes("404")) {
            setError("No workflows found for this user.");
          } else if (err.message.includes("500")) {
            setError("Server error. Please try again later.");
          } else {
            setError(`Failed to load workflows: ${err.message}`);
          }
        } else {
          setError("Failed to load workflows. Please try again later.");
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkflows();
  }, [user, currentPage, pageSize, debouncedSearchTerm, isRedirecting]);

  // Define all hooks unconditionally

  // Helper function to get workflow data regardless of structure
  const getWorkflowData = (data: any): WorkflowDetails => {
    // If it's a WorkflowSummary (has workflow property), return the nested workflow
    if ("workflow" in data && data.workflow) {
      return data.workflow as WorkflowDetails;
    }
    // Otherwise, it's already a WorkflowDetails
    return data as WorkflowDetails;
  };

  // Sort workflows based on selected option (no more frontend filtering)
  const sortedWorkflows = useMemo(() => {
    if (!workflows) return [];

    // Sort based on selected option
    return [...workflows].sort((a, b) => {
      const aData = getWorkflowData(a);
      const bData = getWorkflowData(b);

      switch (sortOption) {
        case "updated_desc":
          return (
            new Date(bData.updated_at || "").getTime() - new Date(aData.updated_at || "").getTime()
          );
        case "updated_asc":
          return (
            new Date(aData.updated_at || "").getTime() - new Date(bData.updated_at || "").getTime()
          );
        case "name_asc":
          return (aData.name || "").localeCompare(bData.name || "");
        case "name_desc":
          return (bData.name || "").localeCompare(aData.name || "");
        default:
          return (
            new Date(bData.updated_at || "").getTime() - new Date(aData.updated_at || "").getTime()
          );
      }
    });
  }, [workflows, sortOption]);

  // Handle creating a new workflow
  const handleCreateWorkflow = async () => {
    try {
      setIsCreatingWorkflow(true);
      const newWorkflow = await createEmptyWorkflow();
      console.log("Created new workflow:", newWorkflow);
      // Redirect to canvas page with the new workflow ID
      router.push(`/workflows/${newWorkflow.workflow_id}/edit`);
    } catch (err: any) {
      console.error("Failed to create workflow:", err);
      
      // Check if it's a duplicate name error
      if (err.message && err.message.includes("already exists")) {
        toast.error(err.message);
      } else {
        setError("Failed to create a new workflow. Please try again.");
        toast.error("Failed to create a new workflow. Please try again.");
      }
      setIsCreatingWorkflow(false);
    }
  };

  // Handle creating workflow from template (placeholder)
  const handleCreateFromTemplate = () => {
    // TODO: Implement template selection functionality
    console.log("Create from template clicked - functionality to be implemented");
  };

  // Handle importing workflow file (placeholder)
  const handleImportFile = () => {
    // TODO: Implement file import functionality
    console.log("Import file clicked - functionality to be implemented");
  };

  // Handler for opening duplicate dialog
  const handleOpenDuplicateDialog = (workflowId: string) => {
    const original = workflows.find(wf => {
      if ("workflow" in wf && wf.workflow && typeof wf.workflow === "object" && "id" in wf.workflow) {
        return (wf.workflow as WorkflowDetails).id === workflowId;
      } else if ("id" in wf) {
        return (wf as WorkflowDetails).id === workflowId;
      }
      return false;
    });

    let originalName = "";
    if (original) {
      if ("workflow" in original && original.workflow && typeof original.workflow === "object" && "name" in original.workflow) {
        originalName = (original.workflow as WorkflowDetails).name || "";
      } else if ("name" in original) {
        originalName = (original as WorkflowDetails).name || "";
      }
    }

    setDuplicateWorkflowId(workflowId);
    setDuplicateFormData({
      name: `${originalName} (Copy)`,
      description: "Duplicated workflow"
    });
    setDuplicateDialogOpen(true);
  };

  // Handler for duplicating a workflow with custom data
  const handleDuplicateWorkflow = async () => {
    if (!duplicateWorkflowId) return;

    setIsDuplicatingId(duplicateWorkflowId);
    try {
      const result = await duplicateWorkflow(duplicateWorkflowId, {
        name: duplicateFormData.name,
        description: duplicateFormData.description
      });

      toast.success("Workflow duplicated successfully!");
      setDuplicateDialogOpen(false);
      setDuplicateWorkflowId(null);
      setDuplicateFormData({ name: "", description: "" });
      // Refresh the workflows list by triggering a re-fetch
      window.location.reload();
    } catch (error: any) {
      console.error("Failed to duplicate workflow:", error);
      
      // Check if it's a duplicate name error
      if (error.message && error.message.includes("already exists")) {
        toast.error(error.message);
      } else {
        toast.error("Failed to duplicate workflow. Please try again.");
      }
    } finally {
      setIsDuplicatingId(null);
    }
  };

  // Handler for canceling duplicate dialog
  const handleCancelDuplicate = () => {
    setDuplicateDialogOpen(false);
    setDuplicateWorkflowId(null);
    setDuplicateFormData({ name: "", description: "" });
  };

  // Memoize the handleSelectWorkflow function to prevent unnecessary re-renders
  const handleSelectWorkflow = useCallback(
    (workflow: WorkflowSummary | WorkflowDetails) => {
      // Check if the workflow is a WorkflowSummary (has workflow property) or a direct WorkflowDetails
      const workflowId =
        "workflow" in workflow && workflow.workflow
          ? workflow.workflow.id
          : (workflow as WorkflowDetails).id;

      if (workflowId) {
        router.push(`/workflows/${workflowId}/edit`);
      }
    },
    [router],
  );

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setSearchTerm(e.target.value);
    // Reset to first page when searching
    setCurrentPage(1);
  };

  // Handle pagination change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="bg-background min-h-screen">

      {/* Main content */}
      <div className="px-4 md:px-6">
        {/* Filters and search - responsive - Only show when there are workflows */}
        {!isLoading && workflows.length > 0 && (
          <div className="mb-6 flex flex-col items-center justify-between gap-4 sm:flex-row sm:gap-6 max-w-full mx-auto">
            <div className="relative w-full max-w-sm mx-auto sm:mx-0 sm:max-w-xs md:max-w-sm lg:w-72">
              <Search
                className="absolute top-1/2 left-3 h-4 w-4 text-muted-foreground transform -translate-y-1/2"
                aria-hidden="true"
              />
              <Input
                placeholder="Search workflows...."
                className="pl-10 rounded-sm text-sm font-[Satoshi] w-full"
                style={{
                  height: '44px',
                  paddingTop: '0',
                  paddingBottom: '0',
                  lineHeight: '44px',
                  display: 'flex',
                  alignItems: 'center',
                  background: 'var(--Input-black, #F9FAFB)',
                  border: '1px solid var(--Border, #E5E7EB)',
                  color: 'var(--search-text, #A1A1AA)'
                }}
                value={searchTerm}
                onChange={handleSearchChange}
                aria-label="Search workflows"
                id="workflow-search"
                name="workflow-search"
              />
            </div>
            <div className="flex items-center gap-2 flex-shrink-0 self-center">
              <span className="text-sm text-muted-foreground font-[Satoshi] whitespace-nowrap">Sort by:</span>
              <Select value={sortOption} onValueChange={setSortOption}>
                <SelectTrigger 
                  className="w-[120px] sm:w-[140px] h-9 rounded-sm font-[Satoshi]"
                  style={{
                    background: 'var(--Input-black, #F9FAFB)',
                    border: '1px solid var(--Border, #E5E7EB)',
                    color: 'var(--White, #000000)'
                  }}
                >
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent 
                  className="font-[Satoshi]"
                  style={{
                    background: 'var(--Input-black, #F9FAFB)',
                    border: '1px solid var(--Border, #E5E7EB)',
                    color: 'var(--White, #000000)'
                  }}
                >
                  <SelectItem value="updated_desc">Latest update</SelectItem>
                  <SelectItem value="updated_asc">Oldest update</SelectItem>
                  <SelectItem value="name_asc">Name (A-Z)</SelectItem>
                  <SelectItem value="name_desc">Name (Z-A)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Search and Sort Skeleton Loaders */}
        {isLoading && (
          <div className="mb-6 flex flex-col items-center justify-between gap-4 sm:flex-row sm:gap-6 max-w-full mx-auto">
            <div className="relative w-full max-w-sm mx-auto sm:mx-0 sm:max-w-xs md:max-w-sm lg:w-72">
              <Skeleton className="h-11 w-full rounded-sm" />
            </div>
            <div className="flex items-center gap-2 flex-shrink-0 self-center">
              <Skeleton className="h-4 w-12" />
              <Skeleton className="w-[120px] sm:w-[140px] h-9 rounded-sm" />
            </div>
          </div>
        )}

        {/* Workflows grid */}
        {isLoading ? (
          <div className="space-y-5 max-w-5xl mx-auto">
            {Array.from({ length: 5 }).map((_, index) => (
              <div
                key={index}
                className="group relative w-full rounded overflow-hidden shadow-sm"
                style={{
                  background: 'var(--container-Background, #FFFFFF)',
                  border: '1px solid var(--Border_color, #E5E7EB)'
                }}
              >
                <div className="relative py-2 px-5 pr-4">
                  <div className="flex flex-col sm:flex-row sm:items-center lg:items-center sm:justify-between gap-3 sm:gap-5 w-full">
                    {/* Left side: Icon and Name/Description */}
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      {/* Workflow Icon Skeleton */}
                      <div 
                        className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded"
                        style={{ backgroundColor: 'var(--icon-background, #F4F4F5)' }}
                      >
                        <Skeleton className="h-5 w-6" />
                      </div>

                      {/* Name and Description Skeleton */}
                      <div className="min-w-0 flex-1">
                        <Skeleton className="h-5 w-48 mb-1.5" />
                        <Skeleton className="h-4 w-80" />
                      </div>
                    </div>

                    {/* Right side: Add trigger button and menu skeleton */}
                    <div className="flex items-center justify-between sm:justify-end sm:items-center lg:items-center gap-2 flex-shrink-0">
                      {/* Add trigger button skeleton */}
                      <Skeleton className="h-6 w-20" />
                      
                      {/* Kebab menu skeleton */}
                      <Skeleton className="h-8 w-8 rounded" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="flex h-64 flex-col items-center justify-center text-center">
            <AlertCircle className="text-brand-unpublish mb-4 h-12 w-12" />
            <h3 className="font-[Satoshi] mb-2 text-xl font-semibold">Failed to Load Workflows</h3>
            <p className="text-brand-secondary-font mb-4 font-[Satoshi]">{error}</p>
            <div className="flex gap-4">
              {/* Only show Try Again button for non-authentication errors */}
              {!error.includes("authenticated") && !error.includes("Authentication") && (
                <Button
                  onClick={() => window.location.reload()}
                  className="bg-brand-primary hover:bg-brand-primary/90 text-white"
                >
                  Try Again
                </Button>
              )}
              {/* Always show Go to Login button for authentication errors */}
              {(error.includes("authenticated") || error.includes("Authentication")) && (
                <Button
                  variant="outline"
                  onClick={() => (window.location.href = "/login")}
                  className="border-brand-border-color text-brand-primary hover:bg-brand-clicked"
                >
                  Go to Login
                </Button>
              )}
            </div>
          </div>
        ) : sortedWorkflows.length === 0 ? (
          <div className="flex flex-1 flex-col items-center justify-center w-full min-h-[60vh]">
            {/* Custom Icon */}
            <div className="relative mb-8">
              <div className="w-32 h-32 flex items-center justify-center opacity-60">
                <img
                  src="/workflow-empty.svg"
                  alt="Workflow Icon"
                  className="w-full h-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
                {/* Fallback Icon */}
                <div className="w-32 h-32 flex items-center justify-center hidden">
                  <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Title */}
            <h2 className="font-[Satoshi] text-gray-900 dark:text-white text-2xl font-bold mb-2">Create your first workflow</h2>
            
            {/* Description */}
            <p className="font-[Satoshi] text-gray-600 dark:text-[#A1A1AA] text-base mb-8 text-center max-w-md">
              Build automated workflows to streamline your processes. Connect tools, automate tasks, and create powerful integrations.
            </p>
            
            {/* Add Workflow Button */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button 
                  disabled={isCreatingWorkflow}
                  className="bg-[#AE00D0] hover:bg-[#8B1FE0] text-white font-[Satoshi] font-medium rounded-lg px-8 py-3 text-base flex items-center gap-2 transition"
                >
                  {isCreatingWorkflow ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <span className="text-xl font-bold">+</span> Create workflow
                      <ChevronDown className="ml-2 h-4 w-4" />
                    </>
                  )}
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-56">
                <DropdownMenuItem
                  onClick={handleCreateWorkflow}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <FileEdit className="h-4 w-4" />
                  <span>Create from blank</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleCreateFromTemplate}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <File className="h-4 w-4" />
                  <span>Create from template</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleImportFile}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <FileUp className="h-4 w-4" />
                  <span>Import file</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ) : (
          <div className="space-y-5 max-w-full mx-auto">
            {sortedWorkflows.map((workflow) => {
              // Generate a key that works for both data structures
              const workflowData = getWorkflowData(workflow);
              const key = workflowData.id || Math.random().toString();

              return (
                <WorkflowCard
                  key={key}
                  workflow={workflow}
                  onClick={handleSelectWorkflow}
                  onDuplicate={handleOpenDuplicateDialog}
                />
              );
            })}

            {/* Pagination */}
            {totalPages > 1 && (
              <Pagination className="mt-6" aria-label="Workflow pagination">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      size="default"
                      onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                      className={`${currentPage <= 1 ? "pointer-events-none opacity-50" : ""} text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke`}
                      aria-disabled={currentPage <= 1}
                    />
                  </PaginationItem>

                  {/* First page */}
                  {currentPage > 2 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => handlePageChange(1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis if needed */}
                  {currentPage > 3 && (
                    <PaginationItem>
                      <PaginationEllipsis className="text-brand-secondary-font" />
                    </PaginationItem>
                  )}

                  {/* Previous page if not first */}
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => handlePageChange(currentPage - 1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {currentPage - 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Current page */}
                  <PaginationItem>
                    <PaginationLink
                      size="default"
                      isActive
                      className="brand-gradient-indicator text-brand-white-text border-none"
                    >
                      {currentPage}
                    </PaginationLink>
                  </PaginationItem>

                  {/* Next page if not last */}
                  {currentPage < totalPages && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => handlePageChange(currentPage + 1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {currentPage + 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis if needed */}
                  {currentPage < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationEllipsis className="text-brand-secondary-font" />
                    </PaginationItem>
                  )}

                  {/* Last page */}
                  {currentPage < totalPages - 1 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => handlePageChange(totalPages)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {totalPages}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      size="default"
                      onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                      className={`${currentPage >= totalPages ? "pointer-events-none opacity-50" : ""} text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke`}
                      aria-disabled={currentPage >= totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </div>
        )}
      </div>

      {/* Duplicate Workflow Dialog */}
      <Dialog open={duplicateDialogOpen} onOpenChange={setDuplicateDialogOpen}>
        <DialogContent className="font-[Satoshi]">
          <DialogHeader>
            <DialogTitle className="font-[Satoshi]">Duplicate Workflow</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="duplicate-name" className="text-right font-[Satoshi]">
                Name
              </Label>
              <Input
                id="duplicate-name"
                value={duplicateFormData.name}
                onChange={(e) => setDuplicateFormData({ ...duplicateFormData, name: e.target.value })}
                className="col-span-3 font-[Satoshi]"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="duplicate-description" className="text-right font-[Satoshi]">
                Description
              </Label>
              <Textarea
                id="duplicate-description"
                value={duplicateFormData.description}
                onChange={(e) => setDuplicateFormData({ ...duplicateFormData, description: e.target.value })}
                className="col-span-3 font-[Satoshi]"
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleCancelDuplicate} className="font-[Satoshi]">
              Cancel
            </Button>
            <Button onClick={handleDuplicateWorkflow} className="font-[Satoshi]">Duplicate</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

