"use client";

import React, { memo, useState } from "react";
import { format } from "date-fns";
import { Calendar, Trash2, MoreHorizontal, Pencil, Workflow, Copy } from "lucide-react";
import { WorkflowDetails, WorkflowSummary, deleteWorkflow, updateWorkflowMetadata } from "../api";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

interface WorkflowCardProps {
  workflow: WorkflowDetails | WorkflowSummary;
  onClick: (workflow: WorkflowDetails | WorkflowSummary) => void;
  onDelete?: (workflowId: string) => void;
  onDuplicate?: (workflowId: string) => void;
}

// Format date for display in "Month DD, YYYY" format
const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), "MMMM dd, yyyy");
  } catch (e) {
    return "Unknown date";
  }
};

// Get status display properties
const getStatusProperties = (status: string | undefined) => {
  if (!status) return { label: "Inactive", active: false };

  const isActive = status.toLowerCase() === "active";
  return {
    label: isActive ? "Active" : "Inactive",
    active: isActive,
  };
};

const WorkflowCard: React.FC<WorkflowCardProps> = ({ workflow, onClick, onDelete, onDuplicate }) => {
  // Check if the workflow is a WorkflowSummary (has workflow property) or a direct WorkflowDetails
  const workflowData = "workflow" in workflow ? workflow.workflow : workflow;
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editTitle, setEditTitle] = useState(workflowData?.name || "");
  const [editDescription, setEditDescription] = useState(workflowData?.description || "");
  const [isDuplicating, setIsDuplicating] = useState(false);

  // Handle delete menu item click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event
    setIsDeleteDialogOpen(true);
  };

  // Handle edit menu item click
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event
    setEditTitle(workflowData?.name || "");
    setEditDescription(workflowData?.description || "");
    setIsEditDialogOpen(true);
  };

  // Handle duplicate menu item click
  const handleDuplicateClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!workflowData?.id || !onDuplicate) return;
    setIsDuplicating(true);
    await onDuplicate(workflowData.id);
    setIsDuplicating(false);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!workflowData?.id) return;

    setIsDeleting(true);
    try {
      const result = await deleteWorkflow(workflowData.id);

      if (result.success) {
        toast.success(result.message || "Workflow deleted successfully");
        // Refresh the page after a short delay to show the updated list
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        toast.error("Failed to delete workflow");
      }
    } catch (error) {
      console.error("Error deleting workflow:", error);
      toast.error(
        error instanceof Error ? error.message : "An error occurred while deleting the workflow",
      );
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // Handle update workflow
  const handleUpdateWorkflow = async () => {
    if (!workflowData?.id) return;

    setIsUpdating(true);
    try {
      await updateWorkflowMetadata(workflowData.id, {
        name: editTitle,
        description: editDescription,
      });

      toast.success("Workflow updated successfully");

      // Refresh the page after a short delay to show the updated list
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Error updating workflow:", error);
      toast.error(
        error instanceof Error ? error.message : "An error occurred while updating the workflow",
      );
    } finally {
      setIsUpdating(false);
      setIsEditDialogOpen(false);
    }
  };

  return (
    <>
      <Card
        className="group relative w-full cursor-pointer transition-all hover:shadow-lg rounded-sm overflow-hidden shadow-sm"
        style={{
          background: 'var(--container-Background, #FFFFFF)',
          border: '1px solid var(--Border_color, #E5E7EB)'
        }}
        onClick={() => onClick(workflow)}
        onKeyDown={(e) => {
          // Handle keyboard navigation - Enter or Space to select
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            onClick(workflow);
          }
        }}
        tabIndex={0}
        role="button"
        aria-label={`Select workflow: ${workflowData?.name || "Untitled Workflow"}`}
      >
        <CardHeader className="relative py-2 px-5 pr-4">
          {/* Main content layout - responsive */}
          <div className="flex flex-col sm:flex-row sm:items-center lg:items-center sm:justify-between gap-3 sm:gap-5 w-full">
            {/* Left side: Icon and Name/Description */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
              {/* Workflow Icon */}
              <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded" style={{ backgroundColor: 'var(--icon-background, #F4F4F5)' }}>
                <img
                  src="/RuhIcon.png"
                  alt="Workflow Icon"
                  className="h-5 w-6"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
                {/* Fallback Icon */}
                <Workflow className="h-5 w-5 hidden" style={{ color: '#8b5cf6' }} />
              </div>

              {/* Name and Description */}
              <div className="min-w-0 flex-1">
                <CardTitle className="text-base font-semibold text-foreground mb-1.5 font-[Satoshi] truncate">
                  {workflowData?.name || "My workflow"}
                </CardTitle>
                <CardDescription className="text-sm font-[Satoshi] break-words leading-tight pb-0.5" style={{ color: 'var(--metadata-text, #6F6F6F)' }}>
                  Workflow | Last update 2 hours ago | created {workflowData?.created_at ? formatDate(workflowData.created_at) : "July 07, 2025"}
                </CardDescription>
              </div>
            </div>

            {/* Right side: Add trigger button and menu - responsive */}
            <div className="flex items-center justify-between sm:justify-end sm:items-center lg:items-center gap-2 flex-shrink-0">
              {/* Add trigger button */}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto px-2 py-1.5 hover:bg-transparent font-medium font-[Satoshi] text-xs sm:text-sm"
                style={{ color: '#C73CF3' }}
                onClick={(e) => {
                  e.stopPropagation();
                  // Add trigger functionality would go here
                }}
              >
                + Add trigger
              </Button>

              {/* Kebab menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8 p-0 hover:bg-accent flex-shrink-0">
                    <div className="flex flex-col items-center justify-center gap-0.5">
                      <div className="w-1 h-1 bg-current rounded-full"></div>
                      <div className="w-1 h-1 bg-current rounded-full"></div>
                      <div className="w-1 h-1 bg-current rounded-full"></div>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-popover border-border font-[Satoshi]">
                  <DropdownMenuItem onClick={handleEditClick} className="hover:bg-accent">
                    <Pencil className="mr-2 h-4 w-4" /> Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDuplicateClick} disabled={isDuplicating} className="hover:bg-accent">
                    <Copy className="mr-2 h-4 w-4" />
                    {isDuplicating ? "Duplicating..." : "Duplicate"}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDeleteClick} className="hover:bg-accent text-destructive">
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="border-brand-stroke bg-brand-card font-[Satoshi]">
          <AlertDialogHeader>
            <AlertDialogTitle className="font-[Satoshi] text-brand-primary-font">
              Delete Workflow
            </AlertDialogTitle>
            <AlertDialogDescription className="font-[Satoshi] text-brand-secondary-font">
              Are you sure you want to delete this workflow? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isDeleting}
              className="border-brand-stroke text-brand-primary-font hover:bg-brand-clicked font-[Satoshi]"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-brand-unpublish text-brand-white-text hover:bg-brand-unpublish/90 font-[Satoshi]"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit workflow dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="border-brand-stroke bg-brand-card sm:max-w-[500px] font-[Satoshi]">
          <DialogHeader>
            <DialogTitle className="font-[Satoshi] text-brand-primary-font">
              Edit Workflow
            </DialogTitle>
            <DialogDescription className="font-[Satoshi] text-brand-secondary-font">
              Update the title and description of your workflow.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title" className="text-brand-primary-font font-[Satoshi]">
                Title
              </Label>
              <Input
                id="title"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                className="border-brand-stroke bg-brand-card-hover text-brand-primary-font font-[Satoshi]"
                placeholder="Enter workflow title"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description" className="text-brand-primary-font font-[Satoshi]">
                Description
              </Label>
              <Textarea
                id="description"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                className="border-brand-stroke bg-brand-card-hover text-brand-primary-font min-h-[100px] font-[Satoshi]"
                placeholder="Enter workflow description"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="border-brand-stroke text-brand-primary-font hover:bg-brand-clicked font-[Satoshi]"
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateWorkflow}
              className="bg-brand-primary text-brand-white-text hover:bg-brand-primary/90 font-[Satoshi]"
              disabled={isUpdating}
            >
              {isUpdating ? "Saving..." : "Save changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(WorkflowCard);
