"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import {
  <PERSON>ap,
  Bo<PERSON>,
  ArrowRight,
  Sparkles,
  Network
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { authRoute } from "@/shared/routes";

// Custom icon components using SVG files with styled containers
const WorkflowIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center w-[34px] h-[34px] p-[5px] bg-[rgba(123,90,255,0.1)] rounded-[8px] ${className || ''}`}>
    <Image
      src="/logo/workflow.svg"
      alt="Workflow"
      width={24}
      height={24}
      className="w-6 h-6 flex-none"
      style={{ filter: 'brightness(0) saturate(100%) invert(47%) sepia(89%) saturate(2347%) hue-rotate(244deg) brightness(102%) contrast(101%)' }}
    />
  </div>
);

const BotIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center w-[34px] h-[34px] p-[5px] bg-[rgba(123,90,255,0.1)] rounded-[8px] ${className || ''}`}>
    <Image
      src="/logo/bot.svg"
      alt="Bot"
      width={24}
      height={24}
      className="w-6 h-6 flex-none"
      style={{ filter: 'brightness(0) saturate(100%) invert(47%) sepia(89%) saturate(2347%) hue-rotate(244deg) brightness(102%) contrast(101%)' }}
    />
  </div>
);

const NetworkIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center w-[34px] h-[34px] p-[5px] bg-[rgba(123,90,255,0.1)] rounded-[8px] ${className || ''}`}>
    <Image
      src="/logo/git-compare.svg"
      alt="Network"
      width={24}
      height={24}
      className="w-6 h-6 flex-none"
      style={{ filter: 'brightness(0) saturate(100%) invert(47%) sepia(89%) saturate(2347%) hue-rotate(244deg) brightness(102%) contrast(101%)' }}
    />
  </div>
);

const ZapIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center w-[34px] h-[34px] p-[5px] bg-[rgba(123,90,255,0.1)] rounded-[8px] ${className || ''}`}>
    <Image
      src="/logo/sparkles.svg"
      alt="Zap"
      width={24}
      height={24}
      className="w-6 h-6 flex-none"
      style={{ filter: 'brightness(0) saturate(100%) invert(47%) sepia(89%) saturate(2347%) hue-rotate(244deg) brightness(102%) contrast(101%)' }}
    />
  </div>
);

const SparklesIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center w-[34px] h-[34px] p-[5px] bg-[rgba(123,90,255,0.1)] rounded-[8px] ${className || ''}`}>
    <Image
      src="/logo/sparkles.svg"
      alt="Sparkles"
      width={24}
      height={24}
      className="w-6 h-6 flex-none"
      style={{ filter: 'brightness(0) saturate(100%) invert(47%) sepia(89%) saturate(2347%) hue-rotate(244deg) brightness(102%) contrast(101%)' }}
    />
  </div>
);

const DatabaseIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center w-[34px] h-[34px] p-[5px] bg-[rgba(123,90,255,0.1)] rounded-[8px] ${className || ''}`}>
    <Image
      src="/logo/database.svg"
      alt="Database"
      width={24}
      height={24}
      className="w-6 h-6 flex-none"
      style={{ filter: 'brightness(0) saturate(100%) invert(47%) sepia(89%) saturate(2347%) hue-rotate(244deg) brightness(102%) contrast(101%)' }}
    />
  </div>
);

const CogIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center w-[34px] h-[34px] p-[5px] bg-[rgba(123,90,255,0.1)] rounded-[8px] ${className || ''}`}>
    <Image
      src="/logo/cog.svg"
      alt="Cog"
      width={24}
      height={24}
      className="w-6 h-6 flex-none"
      style={{ filter: 'brightness(0) saturate(100%) invert(47%) sepia(89%) saturate(2347%) hue-rotate(244deg) brightness(102%) contrast(101%)' }}
    />
  </div>
);

const BrainCircuitIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center w-[34px] h-[34px] p-[5px] bg-[rgba(123,90,255,0.1)] rounded-[8px] ${className || ''}`}>
    <Image
      src="/logo/brain-circuit.svg"
      alt="Brain Circuit"
      width={24}
      height={24}
      className="w-6 h-6 flex-none"
      style={{ filter: 'brightness(0) saturate(100%) invert(47%) sepia(89%) saturate(2347%) hue-rotate(244deg) brightness(102%) contrast(101%)' }}
    />
  </div>
);

// Custom icon components for stats
const GrowingIcon = ({ className }: { className?: string }) => (
  <Image
    src="/logo/chart-no-axes-combined.svg"
    alt="Growing"
    width={24}
    height={24}
    className={className}
    style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
  />
);

const UnlimitedIcon = ({ className }: { className?: string }) => (
  <Image
    src="/logo/infinity.svg"
    alt="Unlimited"
    width={24}
    height={24}
    className={className}
    style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
  />
);

const TimerIcon = ({ className }: { className?: string }) => (
  <Image
    src="/logo/timer.svg"
    alt="24/7"
    width={24}
    height={24}
    className={className}
    style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
  />
);

const SecureIcon = ({ className }: { className?: string }) => (
  <Image
    src="/logo/shield-check.svg"
    alt="Secure"
    width={24}
    height={24}
    className={className}
    style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
  />
);

// Custom social media icons
const InstagramIcon = ({ className }: { className?: string }) => (
  <svg
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
  >
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
);

const XIcon = ({ className }: { className?: string }) => (
  <svg
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
  >
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);
const CompanyLogo = ({ className }: { className?: string }) => (
  <div className={`flex items-center gap-2 ${className || ''}`}>
    <Image
      src="/wflogo.svg"
      alt="Ruh Workflow"
      width={120}
      height={32}
      className="h-8 w-auto dark:hidden"
    />
    <Image
      src="/wflogo_white.svg"
      alt="Ruh Workflow"
      width={120}
      height={32}
      className="h-8 w-auto hidden dark:block"
    />
  </div>
);

export default function LoginPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle direct login without redirecting to /login page
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    window.location.href = authRoute;
  };

  const features = [
    {
      icon: WorkflowIcon,
      title: "Visual Workflow Builder",
      description: "Create workflows with drag-and-drop nodes including API calls, data transformations, and conditional logic.",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: BotIcon,
      title: "AI & Agent Nodes",
      description: "Integrate AI capabilities with chat completion, embeddings, and agentic AI nodes for intelligent automation.",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: NetworkIcon,
      title: "MCP & OAuth Integrations",
      description: "Connect to external services through MCP (Model Context Protocol) and OAuth-enabled integrations.",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: ZapIcon,
      title: "Real-time Execution",
      description: "Execute workflows with real-time monitoring, logging, and approval workflows for complex processes.",
      color: "from-yellow-500 to-orange-500"
    }
  ];

  const stats = [
    { icon: GrowingIcon, value: "Growing", label: "Community" },
    { icon: UnlimitedIcon, value: "Unlimited", label: "Possibilities" },
    { icon: TimerIcon, value: "24/7", label: "Available" },
    { icon: SecureIcon, value: "Secure", label: "Platform" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-brand-background via-brand-background to-brand-card-hover dark:from-brand-background dark:via-brand-background dark:to-brand-card">
      {/* Navigation */}
      <nav className="relative z-50 border-b border-brand-stroke bg-brand-card/90 backdrop-blur-xl dark:bg-brand-card/90 shadow-sm">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <CompanyLogo />
            <div className="flex items-center gap-4">
              {/* <Button variant="ghost" asChild className="text-brand-primary-font dark:text-brand-white-text hover:bg-brand-card-hover dark:hover:bg-brand-card-hover">
                <Link href="/workflows">Dashboard</Link>
              </Button> */}
              <ThemeToggle />
              <Button
                onClick={handleLogin}
                className="bg-brand-primary hover:bg-brand-primary/90 text-white shadow-md"
              >
                Get Started
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden px-4 py-20 sm:px-6 lg:px-8 grid-pattern">
        {/* Background Effects */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute left-1/2 top-0 -translate-x-1/2 transform">
            <div className="h-[800px] w-[800px] rounded-full bg-gradient-to-r from-brand-primary/15 to-brand-secondary/15 dark:from-brand-primary/25 dark:to-brand-secondary/25 blur-3xl animate-pulse"></div>
          </div>
          <div className="absolute right-0 top-1/2 -translate-y-1/2 transform">
            <div className="h-[600px] w-[600px] rounded-full bg-gradient-to-l from-brand-secondary/8 to-transparent dark:from-brand-secondary/15 dark:to-transparent blur-2xl animate-float"></div>
          </div>
          {/* Floating particles */}
          <div className="absolute left-1/4 top-1/4 h-2 w-2 rounded-full bg-brand-primary/20 dark:bg-brand-primary/40 animate-float"></div>
          <div className="absolute right-1/4 top-1/3 h-1 w-1 rounded-full bg-brand-secondary/30 dark:bg-brand-secondary/50 animate-pulse"></div>
          <div className="absolute left-3/4 bottom-1/4 h-3 w-3 rounded-full bg-brand-primary/15 dark:bg-brand-primary/30 animate-float" style={{ animationDelay: '2s' }}></div>
          <div className="absolute right-1/3 bottom-1/3 h-1.5 w-1.5 rounded-full bg-brand-secondary/20 dark:bg-brand-secondary/40 animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="mx-auto max-w-7xl text-center">
          <div className="mx-auto max-w-4xl">
            <Badge className="mb-6 bg-brand-primary/10 text-brand-primary border-brand-primary/20 animate-glow">
              <Sparkles className="mr-2 h-4 w-4 animate-spin" />
              Next-Generation Workflow Automation
            </Badge>

            <h1 className="mb-6 text-5xl font-bold tracking-tight text-brand-primary-font dark:text-brand-white-text sm:text-6xl lg:text-7xl animate-slide-up">
              Build Workflows That
              <span className="block text-gradient-brand animate-float">
                Think & Adapt
              </span>
            </h1>

            <p className="mb-8 text-xl text-brand-secondary-font dark:text-brand-secondary-font sm:text-2xl">
              Build powerful automation workflows using visual drag-and-drop nodes, AI integrations,
              and MCP protocol connections. Create, execute, and monitor complex processes with ease.
            </p>

            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button
                size="lg"
                onClick={handleLogin}
                className="bg-brand-primary hover:bg-brand-primary/90 text-white px-8 py-4 text-lg group"
              >
                Start Building Now
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="border-y border-brand-stroke bg-brand-card/60 py-16 backdrop-blur-sm dark:bg-brand-card/40 shadow-inner">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            {stats.map((stat, index) => (
              <div
                key={stat.label}
                className="text-center group hover:scale-105 transition-transform duration-300 animate-scale-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-brand-primary to-brand-secondary animate-glow shadow-lg">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-3xl font-bold text-brand-primary-font dark:text-brand-white-text">
                  {stat.value}
                </div>
                <div className="text-sm text-brand-secondary-font dark:text-brand-secondary-font">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-brand-primary-font dark:text-brand-white-text mb-4">
              Core Workflow Capabilities
            </h2>
            <p className="text-xl text-brand-secondary-font max-w-3xl mx-auto">
              Build, execute, and monitor workflows with visual nodes and powerful integrations
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {features.map((feature, index) => (
              <Card
                key={feature.title}
                className="group relative overflow-hidden border-brand-stroke bg-brand-card/90 backdrop-blur-sm hover:bg-brand-card-hover dark:bg-brand-card/80 dark:hover:bg-brand-card-hover transition-all duration-300 hover:scale-105 hover:shadow-xl animate-fade-in shadow-sm"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="p-6">
                  <div className="mb-4">
                    <feature.icon />
                  </div>
                  <h3 className="mb-3 text-xl font-semibold text-brand-primary-font dark:text-brand-white-text">
                    {feature.title}
                  </h3>
                  <p className="text-brand-secondary-font dark:text-brand-secondary-font">
                    {feature.description}
                  </p>
                </div>
                <div className="absolute inset-0 -z-10 bg-gradient-to-r from-brand-primary/3 to-brand-secondary/3 dark:from-brand-primary/8 dark:to-brand-secondary/8 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Workflow Showcase */}
      <section className="relative overflow-hidden bg-gradient-to-r from-brand-primary/3 to-brand-secondary/3 dark:from-brand-primary/8 dark:to-brand-secondary/8 py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-brand-primary-font dark:text-brand-white-text mb-4">
              See Workflows in Action
            </h2>
            <p className="text-xl text-brand-secondary-font max-w-3xl mx-auto">
              From simple automations to complex AI-driven processes
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            <Card className="group border-brand-stroke bg-brand-card/95 backdrop-blur-sm hover:bg-brand-card-hover dark:bg-brand-card/90 dark:hover:bg-brand-card-hover transition-all duration-300 hover:scale-105 shadow-sm">
              <div className="p-6">
                <div className="mb-4 flex items-center gap-3">
                  <DatabaseIcon />
                  <h3 className="text-lg font-semibold text-brand-primary-font dark:text-brand-white-text">
                    Data Processing
                  </h3>
                </div>
                <p className="text-brand-secondary-font dark:text-brand-secondary-font">
                  Transform, filter, merge, and manipulate data using built-in data processing nodes.
                </p>
              </div>
            </Card>

            <Card className="group border-brand-stroke bg-brand-card/95 backdrop-blur-sm hover:bg-brand-card-hover dark:bg-brand-card/90 dark:hover:bg-brand-card-hover transition-all duration-300 hover:scale-105 shadow-sm">
              <div className="p-6">
                <div className="mb-4 flex items-center gap-3">
                  <CogIcon />
                  <h3 className="text-lg font-semibold text-brand-primary-font dark:text-brand-white-text">
                    API Integration
                  </h3>
                </div>
                <p className="text-brand-secondary-font dark:text-brand-secondary-font">
                  Connect to external services using OAuth authentication and MCP protocol integrations.
                </p>
              </div>
            </Card>

            <Card className="group border-brand-stroke bg-brand-card/95 backdrop-blur-sm hover:bg-brand-card-hover dark:bg-brand-card/90 dark:hover:bg-brand-card-hover transition-all duration-300 hover:scale-105 shadow-sm">
              <div className="p-6">
                <div className="mb-4 flex items-center gap-3">
                  <BrainCircuitIcon />
                  <h3 className="text-lg font-semibold text-brand-primary-font dark:text-brand-white-text">
                    Smart Automation
                  </h3>
                </div>
                <p className="text-brand-secondary-font dark:text-brand-secondary-font">
                  Create conditional logic, loops, and approval workflows with real-time execution monitoring.
                </p>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-primary/5 to-brand-secondary/5 dark:from-brand-primary/15 dark:to-brand-secondary/15"></div>
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
            <div className="h-[600px] w-[600px] rounded-full bg-gradient-to-r from-brand-primary/10 to-brand-secondary/10 dark:from-brand-primary/25 dark:to-brand-secondary/25 blur-3xl"></div>
          </div>
        </div>

        <div className="mx-auto max-w-4xl text-center">
          <h2 className="mb-6 text-4xl font-bold text-brand-primary-font dark:text-brand-white-text sm:text-5xl">
            Ready to Transform Your Workflows?
          </h2>
          <p className="mb-8 text-xl text-brand-secondary-font">
            Join thousands of teams already building the future with intelligent automation.
            Start your journey today.
          </p>
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
            <Button
              size="lg"
              onClick={handleLogin}
              className="bg-brand-primary hover:bg-brand-primary/90 text-white px-8 py-4 text-lg group"
            >
              Get Started Free
              <Sparkles className="ml-2 h-5 w-5 transition-transform group-hover:rotate-12" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-brand-primary/20 text-brand-primary hover:bg-brand-primary/10 px-8 py-4 text-lg"
              asChild
            >
              <Link href="/workflows">
                Explore Workflows
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-brand-stroke bg-brand-card/60 py-16 backdrop-blur-sm dark:bg-brand-card/40 shadow-inner">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col items-center justify-between gap-8 sm:flex-row sm:items-center">
            <div className="flex flex-col items-center gap-6">
              <CompanyLogo />
              <div className="flex items-center gap-6">
                <a
                  href="https://www.instagram.com/ruhdotai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative flex items-center justify-center w-12 h-12 rounded-full bg-brand-card border border-brand-stroke hover:bg-gradient-to-r hover:from-pink-500 hover:to-orange-500 hover:border-transparent transition-all duration-500 hover:scale-125 hover:shadow-2xl hover:shadow-pink-500/25"
                  aria-label="Follow us on Instagram"
                >
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-pink-500 to-orange-500 opacity-0 group-hover:opacity-20 blur-md transition-all duration-500 group-hover:scale-150"></div>
                  <InstagramIcon className="relative z-10 w-6 h-6 text-brand-secondary-font group-hover:text-white transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-lg" />
                  <div className="absolute inset-0 rounded-full border-2 border-transparent group-hover:border-white/30 transition-all duration-300 animate-pulse group-hover:animate-spin"></div>
                </a>
                <a
                  href="https://x.com/ruhdotai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative flex items-center justify-center w-12 h-12 rounded-full bg-brand-card border border-brand-stroke hover:bg-gradient-to-r hover:from-gray-800 hover:to-gray-900 dark:hover:from-gray-100 dark:hover:to-white hover:border-transparent transition-all duration-500 hover:scale-125 hover:shadow-2xl hover:shadow-gray-500/25"
                  aria-label="Follow us on X"
                >
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-gray-800 to-black dark:from-gray-100 dark:to-white opacity-0 group-hover:opacity-20 blur-md transition-all duration-500 group-hover:scale-150"></div>
                  <XIcon className="relative z-10 w-6 h-6 text-brand-secondary-font group-hover:text-white dark:group-hover:text-black transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-lg" />
                  <div className="absolute inset-0 rounded-full border-2 border-transparent group-hover:border-white/30 dark:group-hover:border-black/30 transition-all duration-300 animate-pulse group-hover:animate-spin"></div>
                </a>
              </div>
            </div>
            <div className="text-sm text-brand-secondary-font dark:text-brand-secondary-font text-center sm:text-right">
              <div className="font-medium">© 2025 Ruh Workflow. All rights reserved.</div>
              <div className="mt-2 text-xs opacity-75">
                Follow us for updates and workflow inspiration
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
