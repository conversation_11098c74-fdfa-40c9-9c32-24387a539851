/**
 * Tests for useToolConnections hook
 */

import { renderHook } from "@testing-library/react";
import { Edge, Node } from "reactflow";
import { useToolConnections, useAgenticAIToolConnections, useIsConnectedAsTool } from "../useToolConnections";
import { WorkflowNodeData } from "@/types";

// Mock the utility functions
jest.mock("@/utils/toolConnectionUtils", () => ({
  calculateToolConnectionState: jest.fn(),
  isNodeConnectedAsTool: jest.fn(),
  getNodesConnectedAsTools: jest.fn(),
}));

import {
  calculateToolConnectionState,
  isNodeConnectedAsTool,
  getNodesConnectedAsTools,
} from "@/utils/toolConnectionUtils";

const mockCalculateToolConnectionState = calculateToolConnectionState as jest.MockedFunction<
  typeof calculateToolConnectionState
>;
const mockIsNodeConnectedAsTool = isNodeConnectedAsTool as jest.MockedFunction<
  typeof isNodeConnectedAsTool
>;
const mockGetNodesConnectedAsTools = getNodesConnectedAsTools as jest.MockedFunction<
  typeof getNodesConnectedAsTools
>;

// Mock data helpers
const createMockNode = (
  id: string,
  originalType: string,
  label: string
): Node<WorkflowNodeData> => ({
  id,
  type: "custom",
  position: { x: 0, y: 0 },
  data: {
    label,
    type: "component",
    originalType,
    definition: {
      name: originalType,
      display_name: label,
      description: `Mock ${originalType}`,
      category: "test",
      icon: "test",
      beta: false,
      inputs: [],
      outputs: [],
      is_valid: true,
      path: `test.${originalType}`,
    },
  },
});

describe("useToolConnections", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("returns null state when no node is selected", () => {
    mockGetNodesConnectedAsTools.mockReturnValue(new Set());

    const { result } = renderHook(() =>
      useToolConnections(null, [], [])
    );

    expect(result.current.toolConnectionState).toBe(null);
    expect(result.current.isConnectedAsTool).toBe(false);
    expect(result.current.connectedAsToolNodes.size).toBe(0);
  });

  test("calculates tool connection state for AgenticAI node", () => {
    const agenticNode = createMockNode("agentic1", "AgenticAI", "AI Agent");
    const mockState = {
      hasConnectedTools: true,
      connectedToolCount: 2,
      toolConnections: [],
      availableToolSlots: [],
    };

    mockCalculateToolConnectionState.mockReturnValue(mockState);
    mockIsNodeConnectedAsTool.mockReturnValue(false);
    mockGetNodesConnectedAsTools.mockReturnValue(new Set(["node1", "node2"]));

    const { result } = renderHook(() =>
      useToolConnections(agenticNode, [], [])
    );

    expect(result.current.toolConnectionState).toEqual(mockState);
    expect(mockCalculateToolConnectionState).toHaveBeenCalledWith("agentic1", [], []);
  });

  test("does not calculate tool state for non-AgenticAI nodes", () => {
    const regularNode = createMockNode("node1", "SelectData", "Select Data");

    mockIsNodeConnectedAsTool.mockReturnValue(true);
    mockGetNodesConnectedAsTools.mockReturnValue(new Set(["node1"]));

    const { result } = renderHook(() =>
      useToolConnections(regularNode, [], [])
    );

    expect(result.current.toolConnectionState).toBe(null);
    expect(result.current.isConnectedAsTool).toBe(true);
    expect(mockCalculateToolConnectionState).not.toHaveBeenCalled();
  });

  test("updates when dependencies change", () => {
    const agenticNode = createMockNode("agentic1", "AgenticAI", "AI Agent");
    const mockState1 = {
      hasConnectedTools: false,
      connectedToolCount: 0,
      toolConnections: [],
      availableToolSlots: [],
    };
    const mockState2 = {
      hasConnectedTools: true,
      connectedToolCount: 1,
      toolConnections: [],
      availableToolSlots: [],
    };

    mockCalculateToolConnectionState
      .mockReturnValueOnce(mockState1)
      .mockReturnValueOnce(mockState2);
    mockIsNodeConnectedAsTool.mockReturnValue(false);
    mockGetNodesConnectedAsTools.mockReturnValue(new Set());

    const { result, rerender } = renderHook(
      ({ edges }) => useToolConnections(agenticNode, edges, []),
      { initialProps: { edges: [] as Edge[] } }
    );

    expect(result.current.toolConnectionState).toEqual(mockState1);

    // Simulate edge change
    const newEdges = [{ id: "edge1" } as Edge];
    rerender({ edges: newEdges });

    expect(result.current.toolConnectionState).toEqual(mockState2);
    expect(mockCalculateToolConnectionState).toHaveBeenCalledTimes(2);
  });
});

describe("useAgenticAIToolConnections", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("returns tool connection state for specific node", () => {
    const mockState = {
      hasConnectedTools: true,
      connectedToolCount: 3,
      toolConnections: [],
      availableToolSlots: [],
    };

    mockCalculateToolConnectionState.mockReturnValue(mockState);

    const { result } = renderHook(() =>
      useAgenticAIToolConnections("agentic1", [], [])
    );

    expect(result.current).toEqual(mockState);
    expect(mockCalculateToolConnectionState).toHaveBeenCalledWith("agentic1", [], []);
  });

  test("updates when node ID changes", () => {
    const mockState1 = {
      hasConnectedTools: false,
      connectedToolCount: 0,
      toolConnections: [],
      availableToolSlots: [],
    };
    const mockState2 = {
      hasConnectedTools: true,
      connectedToolCount: 2,
      toolConnections: [],
      availableToolSlots: [],
    };

    mockCalculateToolConnectionState
      .mockReturnValueOnce(mockState1)
      .mockReturnValueOnce(mockState2);

    const { result, rerender } = renderHook(
      ({ nodeId }) => useAgenticAIToolConnections(nodeId, [], []),
      { initialProps: { nodeId: "agentic1" } }
    );

    expect(result.current).toEqual(mockState1);

    rerender({ nodeId: "agentic2" });

    expect(result.current).toEqual(mockState2);
    expect(mockCalculateToolConnectionState).toHaveBeenCalledTimes(2);
  });
});

describe("useIsConnectedAsTool", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("returns correct connection status", () => {
    mockIsNodeConnectedAsTool.mockReturnValue(true);

    const { result } = renderHook(() =>
      useIsConnectedAsTool("node1", [])
    );

    expect(result.current).toBe(true);
    expect(mockIsNodeConnectedAsTool).toHaveBeenCalledWith("node1", []);
  });

  test("updates when edges change", () => {
    mockIsNodeConnectedAsTool
      .mockReturnValueOnce(false)
      .mockReturnValueOnce(true);

    const { result, rerender } = renderHook(
      ({ edges }) => useIsConnectedAsTool("node1", edges),
      { initialProps: { edges: [] as Edge[] } }
    );

    expect(result.current).toBe(false);

    const newEdges = [{ id: "edge1" } as Edge];
    rerender({ edges: newEdges });

    expect(result.current).toBe(true);
    expect(mockIsNodeConnectedAsTool).toHaveBeenCalledTimes(2);
  });
});
