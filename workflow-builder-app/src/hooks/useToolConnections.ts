/**
 * Custom hook for managing tool connections in AgenticAI workflow components
 */

import { useMemo } from "react";
import { Edge, Node } from "reactflow";
import { WorkflowNodeData, ToolConnectionState } from "@/types";
import {
  calculateToolConnectionState,
  isNodeConnectedAsTool,
  getNodesConnectedAsTools,
} from "@/utils/toolConnectionUtils";

interface UseToolConnectionsResult {
  toolConnectionState: ToolConnectionState | null;
  isConnectedAsTool: boolean;
  connectedAsToolNodes: Set<string>;
}

/**
 * Hook to manage tool connection state for workflow nodes
 * @param selectedNode - The currently selected node (can be null)
 * @param edges - All edges in the workflow
 * @param nodes - All nodes in the workflow
 * @returns Tool connection state and helper functions
 */
export function useToolConnections(
  selectedNode: Node<WorkflowNodeData> | null,
  edges: Edge[],
  nodes: Node<WorkflowNodeData>[]
): UseToolConnectionsResult {
  // Calculate tool connection state for AgenticAI nodes
  const toolConnectionState = useMemo(() => {
    if (!selectedNode || selectedNode.data.originalType !== "AgenticAI") {
      return null;
    }
    return calculateToolConnectionState(selectedNode.id, edges, nodes);
  }, [selectedNode, edges, nodes]);

  // Check if current node is connected as a tool
  const isConnectedAsTool = useMemo(() => {
    if (!selectedNode) return false;
    return isNodeConnectedAsTool(selectedNode.id, edges);
  }, [selectedNode, edges]);

  // Get all nodes connected as tools
  const connectedAsToolNodes = useMemo(() => {
    return getNodesConnectedAsTools(edges);
  }, [edges]);

  return {
    toolConnectionState,
    isConnectedAsTool,
    connectedAsToolNodes,
  };
}

/**
 * Hook specifically for AgenticAI nodes to get tool connection information
 * @param nodeId - The ID of the AgenticAI node
 * @param edges - All edges in the workflow
 * @param nodes - All nodes in the workflow
 * @returns Tool connection state for the specific node
 */
export function useAgenticAIToolConnections(
  nodeId: string,
  edges: Edge[],
  nodes: Node<WorkflowNodeData>[]
): ToolConnectionState {
  return useMemo(() => {
    return calculateToolConnectionState(nodeId, edges, nodes);
  }, [nodeId, edges, nodes]);
}

/**
 * Hook to check if a specific node is connected as a tool
 * @param nodeId - The ID of the node to check
 * @param edges - All edges in the workflow
 * @returns Boolean indicating if the node is connected as a tool
 */
export function useIsConnectedAsTool(nodeId: string, edges: Edge[]): boolean {
  return useMemo(() => {
    return isNodeConnectedAsTool(nodeId, edges);
  }, [nodeId, edges]);
}
