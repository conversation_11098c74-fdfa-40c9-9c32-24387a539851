/**
 * Credential Transformation Utilities
 * 
 * This module provides utilities to transform credential data between
 * frontend and backend formats, handling schema differences.
 */

import {
  CredentialCreate,
  CredentialUpdate,
  Credential,
  CredentialListResponse,
  BackendCredentialCreate,
  BackendCredentialUpdate,
  BackendCredentialInfo,
  BackendCredentialListResponse,
} from '../types/credentials';

/**
 * Transform frontend CredentialCreate to backend format
 * Maps 'name' to 'key_name' for backend compatibility
 */
export const transformCredentialForBackend = (
  frontendCredential: CredentialCreate
): BackendCredentialCreate => {
  return {
    key_name: frontendCredential.name,
    value: frontendCredential.value,
    description: frontendCredential.description,
  };
};

/**
 * Transform frontend CredentialUpdate to backend format
 * Maps 'name' to 'key_name' for backend compatibility
 */
export const transformCredentialUpdateForBackend = (
  frontendUpdate: CredentialUpdate
): BackendCredentialUpdate => {
  return {
    key_name: frontendUpdate.name,
    value: frontendUpdate.value,
    description: frontendUpdate.description,
  };
};

/**
 * Transform backend CredentialInfo to frontend format
 * Maps 'key_name' to 'name' and timestamp fields
 * Excludes 'value' field for security
 */
export const transformCredentialFromBackend = (
  backendCredential: BackendCredentialInfo
): Credential => {
  return {
    id: backendCredential.id,
    name: backendCredential.key_name,
    description: backendCredential.description,
    createdAt: backendCredential.created_at,
    updatedAt: backendCredential.updated_at,
    lastUsedAt: backendCredential.last_used_at,
  };
};

/**
 * Transform backend credential list response to frontend format
 * Transforms each credential and excludes values for security
 */
export const transformCredentialListFromBackend = (
  backendResponse: BackendCredentialListResponse
): CredentialListResponse => {
  return {
    credentials: backendResponse.credentials.map(transformCredentialFromBackend),
  };
};

/**
 * Sanitize credential data for logging
 * Removes sensitive information like values
 */
export const sanitizeCredentialForLogging = (credential: any): any => {
  const sanitized = { ...credential };
  
  // Remove sensitive fields
  delete sanitized.value;
  delete sanitized.credential_value;
  
  // Mask partial information if present
  if (sanitized.key_name && sanitized.key_name.length > 4) {
    sanitized.key_name = sanitized.key_name.substring(0, 4) + '***';
  }
  
  if (sanitized.name && sanitized.name.length > 4) {
    sanitized.name = sanitized.name.substring(0, 4) + '***';
  }
  
  return sanitized;
};

/**
 * Validate credential data before transformation
 * Ensures required fields are present and valid
 */
export const validateCredentialCreate = (credential: CredentialCreate): boolean => {
  if (!credential) return false;
  if (!credential.name || credential.name.trim().length === 0) return false;
  if (!credential.value || credential.value.trim().length === 0) return false;
  
  // Validate name length (backend constraint: max 20 characters)
  if (credential.name.length > 20) return false;
  
  return true;
};

/**
 * Validate credential update data
 * At least one field must be provided for update
 */
export const validateCredentialUpdate = (update: CredentialUpdate): boolean => {
  if (!update) return false;
  
  const hasName = update.name && update.name.trim().length > 0;
  const hasValue = update.value && update.value.trim().length > 0;
  const hasDescription = update.description !== undefined;
  
  // At least one field must be provided
  if (!hasName && !hasValue && !hasDescription) return false;
  
  // Validate name length if provided
  if (hasName && update.name!.length > 20) return false;
  
  return true;
};

/**
 * Transform error response from backend to frontend format
 * Handles different error response structures
 */
export const transformErrorFromBackend = (error: any): string => {
  // Handle axios error structure
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  // Handle direct backend error
  if (error.message) {
    return error.message;
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return error;
  }
  
  // Fallback for unknown error structures
  return 'An unexpected error occurred';
};

/**
 * Create a safe credential object for UI display
 * Ensures all required fields are present with defaults
 */
export const createSafeCredential = (credential: Partial<Credential>): Credential => {
  return {
    id: credential.id || '',
    name: credential.name || 'Unknown Credential',
    description: credential.description,
    createdAt: credential.createdAt || new Date().toISOString(),
    updatedAt: credential.updatedAt || new Date().toISOString(),
    lastUsedAt: credential.lastUsedAt || new Date().toISOString(),
  };
};

/**
 * Check if a credential object has all required fields
 * Used for validation before API calls
 */
export const isValidCredential = (credential: any): credential is Credential => {
  return (
    credential &&
    typeof credential === 'object' &&
    typeof credential.id === 'string' &&
    typeof credential.name === 'string' &&
    typeof credential.createdAt === 'string' &&
    typeof credential.updatedAt === 'string' &&
    typeof credential.lastUsedAt === 'string'
  );
};

/**
 * Extract credential ID from various input formats
 * Handles string IDs or credential objects
 */
export const extractCredentialId = (input: string | Credential | { id: string }): string => {
  if (typeof input === 'string') {
    return input;
  }
  
  if (input && typeof input === 'object' && 'id' in input) {
    return input.id;
  }
  
  throw new Error('Invalid credential input: unable to extract ID');
};

/**
 * Format credential for display in UI components
 * Creates a user-friendly display string
 */
export const formatCredentialForDisplay = (credential: Credential): string => {
  const name = credential.name || 'Unnamed Credential';
  const description = credential.description;
  
  if (description && description.trim().length > 0) {
    return `${name} - ${description}`;
  }
  
  return name;
};

/**
 * Sort credentials by various criteria
 * Used for consistent ordering in UI components
 */
export const sortCredentials = (
  credentials: Credential[],
  sortBy: 'name' | 'createdAt' | 'updatedAt' | 'lastUsedAt' = 'name',
  order: 'asc' | 'desc' = 'asc'
): Credential[] => {
  return [...credentials].sort((a, b) => {
    let aValue: string;
    let bValue: string;
    
    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'createdAt':
        aValue = a.createdAt;
        bValue = b.createdAt;
        break;
      case 'updatedAt':
        aValue = a.updatedAt;
        bValue = b.updatedAt;
        break;
      case 'lastUsedAt':
        aValue = a.lastUsedAt;
        bValue = b.lastUsedAt;
        break;
      default:
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
    }
    
    const comparison = aValue.localeCompare(bValue);
    return order === 'asc' ? comparison : -comparison;
  });
};
