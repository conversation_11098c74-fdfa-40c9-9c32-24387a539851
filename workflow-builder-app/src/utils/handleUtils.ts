/**
 * Utilities for handle management and positioning
 */

export interface HandlePosition {
  name: string;
  position: {
    top?: number;
    left?: number;
    right?: number;
  };
  side?: "left" | "right";
}

export interface HandleLayoutConfig {
  nodeHeight?: number;
  nodeWidth?: number;
  spacing?: number;
  side?: "left" | "right";
  autoSpacing?: boolean;
  enableScrolling?: boolean;
  maxVisibleHandles?: number;
  groupByType?: boolean;
  sortByPriority?: boolean;
  balanceSides?: boolean;
  maxPerSide?: number;
  groupToolHandles?: boolean;
  minSpacing?: number;
  maxSpacing?: number;
  minNodeHeight?: number;
  minNodeWidth?: number;
}

// DynamicToolConfig interface removed - no longer needed with single handle approach

export interface HandleValidationConfig {
  maxHandles?: number;
  maxToolHandles?: number;
  spacing?: number;
  nodeHeight?: number;
  nodeWidth?: number;
  minNodeHeight?: number;
  minNodeWidth?: number;
}

export interface SpacingConfig {
  numHandles: number;
  nodeHeight: number;
  minSpacing: number;
  maxSpacing: number;
}

/**
 * Calculates optimal positions for handles based on configuration
 */
export function calculateHandlePositions(
  handles: any[],
  config: HandleLayoutConfig = {}
): HandlePosition[] {
  const {
    nodeHeight = 120,
    nodeWidth = 200,
    spacing = 30,
    side = "left",
    autoSpacing = false,
    enableScrolling = false,
    maxVisibleHandles = 10,
  } = config;

  let actualSpacing = spacing;
  let visibleHandles = handles;

  // Auto-adjust spacing if enabled
  if (autoSpacing) {
    const maxHeight = nodeHeight - 40; // Leave margin
    const requiredHeight = handles.length * spacing;
    if (requiredHeight > maxHeight) {
      actualSpacing = Math.max(15, maxHeight / handles.length);
    }
  }

  // Handle scrolling if enabled
  if (enableScrolling && handles.length > maxVisibleHandles) {
    visibleHandles = handles.slice(0, maxVisibleHandles);
  }

  return visibleHandles.map((handle, index) => {
    const top = actualSpacing * (index + 1);
    const position = side === "left" 
      ? { top, left: -5 }
      : { top, right: -5 };

    return {
      name: handle.name,
      position,
      side,
    };
  });
}

// Legacy dynamic tool handle functions removed - no longer needed with single handle approach
// getAvailableToolSlots and generateDynamicToolHandles functions removed

/**
 * Optimizes handle layout for better visual distribution
 */
export function optimizeHandleLayout(handles: any[], config: HandleLayoutConfig = {}): any[] {
  const {
    groupByType = false,
    sortByPriority = false,
    balanceSides = false,
    maxPerSide = 10,
    groupToolHandles = false,
  } = config;

  let optimized = [...handles];

  // Sort by priority if enabled
  if (sortByPriority) {
    optimized = sortHandlesByPriority(optimized);
  }

  // Group by type if enabled
  if (groupByType || groupToolHandles) {
    const regularHandles = optimized.filter(h => h.name !== "tools");
    const toolHandles = optimized.filter(h => h.name === "tools");
    optimized = [...regularHandles, ...toolHandles];
  }

  // Balance across sides if enabled
  if (balanceSides) {
    const leftSide = optimized.slice(0, maxPerSide).map(h => ({ ...h, side: "left" }));
    const rightSide = optimized.slice(maxPerSide, maxPerSide * 2).map(h => ({ ...h, side: "right" }));
    optimized = [...leftSide, ...rightSide];
  }

  return optimized;
}

/**
 * Validates handle configuration
 */
export function validateHandleConfiguration(config: HandleValidationConfig): void {
  const {
    maxHandles,
    maxToolHandles,
    spacing,
    nodeHeight,
    nodeWidth,
    minNodeHeight = 100,
    minNodeWidth = 150,
  } = config;

  if (maxHandles !== undefined && maxHandles < 0) {
    throw new Error("maxHandles must be positive");
  }

  if (maxToolHandles !== undefined && maxHandles !== undefined && maxToolHandles > maxHandles) {
    throw new Error("maxToolHandles cannot exceed maxHandles");
  }

  if (spacing !== undefined && spacing <= 0) {
    throw new Error("spacing must be positive");
  }

  if (nodeHeight !== undefined && nodeHeight < minNodeHeight) {
    throw new Error("nodeHeight is too small");
  }

  if (nodeWidth !== undefined && nodeWidth < minNodeWidth) {
    throw new Error("nodeWidth is too small");
  }
}

/**
 * Calculates optimal spacing for handles
 */
export function getHandleSpacing(config: SpacingConfig): number {
  const { numHandles, nodeHeight, minSpacing, maxSpacing } = config;

  const availableHeight = nodeHeight - 40; // Leave margin
  const idealSpacing = availableHeight / numHandles;

  if (idealSpacing < minSpacing) {
    return minSpacing;
  }

  if (idealSpacing > maxSpacing) {
    return maxSpacing;
  }

  return idealSpacing;
}

/**
 * Sorts handles by priority
 */
export function sortHandlesByPriority(handles: any[]): any[] {
  return [...handles].sort((a, b) => {
    const aPriority = a.priority ?? Number.MAX_SAFE_INTEGER;
    const bPriority = b.priority ?? Number.MAX_SAFE_INTEGER;
    return aPriority - bPriority;
  });
}
