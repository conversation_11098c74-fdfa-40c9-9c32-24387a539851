/**
 * Tests for tool connection utilities
 */

import { Edge, Node } from "reactflow";
import {
  isToolHandle,
  extractToolSlotNumber,
  getToolConnections,
  calculateToolConnectionState,
  isNodeConnectedAsTool,
  getNodesConnectedAsTools,
  generateToolStyleClasses,
} from "../toolConnectionUtils";
import { WorkflowNodeData } from "@/types";

// Mock data helpers
const createMockNode = (
  id: string,
  originalType: string,
  label: string,
  type: string = "component"
): Node<WorkflowNodeData> => ({
  id,
  type: "custom",
  position: { x: 0, y: 0 },
  data: {
    label,
    type,
    originalType,
    definition: {
      name: originalType,
      display_name: label,
      description: `Mock ${originalType}`,
      category: "test",
      icon: "test",
      beta: false,
      inputs: [],
      outputs: [],
      is_valid: true,
      path: `test.${originalType}`,
      type: type === "mcp" ? "mcp" : undefined,
    },
  },
});

const createMockEdge = (
  id: string,
  source: string,
  target: string,
  sourceHandle: string,
  targetHandle: string
): Edge => ({
  id,
  source,
  target,
  sourceHandle,
  targetHandle,
});

describe("toolConnectionUtils", () => {
  describe("isToolHandle", () => {
    test("returns true for valid tool handles", () => {
      expect(isToolHandle("tool_1")).toBe(true);
      expect(isToolHandle("tool_2")).toBe(true);
      expect(isToolHandle("tool_10")).toBe(true);
    });

    test("returns false for invalid tool handles", () => {
      expect(isToolHandle("input_data")).toBe(false);
      expect(isToolHandle("output_data")).toBe(false);
      expect(isToolHandle("tool_")).toBe(false);
      expect(isToolHandle("tool_abc")).toBe(false);
      expect(isToolHandle("tool")).toBe(false);
      expect(isToolHandle("")).toBe(false);
    });
  });

  describe("extractToolSlotNumber", () => {
    test("extracts correct slot numbers", () => {
      expect(extractToolSlotNumber("tool_1")).toBe(1);
      expect(extractToolSlotNumber("tool_5")).toBe(5);
      expect(extractToolSlotNumber("tool_10")).toBe(10);
    });

    test("returns null for invalid tool handles", () => {
      expect(extractToolSlotNumber("input_data")).toBe(null);
      expect(extractToolSlotNumber("tool_")).toBe(null);
      expect(extractToolSlotNumber("tool_abc")).toBe(null);
    });
  });

  describe("getToolConnections", () => {
    test("returns empty array when no tool connections exist", () => {
      const nodes = [
        createMockNode("node1", "AgenticAI", "AI Agent"),
        createMockNode("node2", "SelectData", "Select Data"),
      ];
      const edges = [
        createMockEdge("edge1", "node2", "node1", "output_data", "input_data"),
      ];

      const result = getToolConnections("node1", edges, nodes);
      expect(result).toEqual([]);
    });

    test("returns tool connections for AgenticAI node", () => {
      const nodes = [
        createMockNode("agentic1", "AgenticAI", "AI Agent"),
        createMockNode("select1", "SelectData", "Select Data"),
        createMockNode("weather1", "WeatherTool", "Weather Tool", "mcp"),
      ];
      const edges = [
        createMockEdge("edge1", "select1", "agentic1", "output_data", "tool_1"),
        createMockEdge("edge2", "weather1", "agentic1", "weather_data", "tool_2"),
      ];

      const result = getToolConnections("agentic1", edges, nodes);
      
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        toolSlot: "tool_1",
        connectedNodeId: "select1",
        connectedNodeType: "SelectData",
        connectedNodeLabel: "Select Data",
        componentType: "regular",
      });
      expect(result[1]).toEqual({
        toolSlot: "tool_2",
        connectedNodeId: "weather1",
        connectedNodeType: "WeatherTool",
        connectedNodeLabel: "Weather Tool",
        componentType: "mcp",
      });
    });

    test("sorts tool connections by slot number", () => {
      const nodes = [
        createMockNode("agentic1", "AgenticAI", "AI Agent"),
        createMockNode("tool1", "Tool1", "Tool 1"),
        createMockNode("tool2", "Tool2", "Tool 2"),
        createMockNode("tool3", "Tool3", "Tool 3"),
      ];
      const edges = [
        createMockEdge("edge3", "tool3", "agentic1", "output", "tool_3"),
        createMockEdge("edge1", "tool1", "agentic1", "output", "tool_1"),
        createMockEdge("edge2", "tool2", "agentic1", "output", "tool_2"),
      ];

      const result = getToolConnections("agentic1", edges, nodes);
      
      expect(result).toHaveLength(3);
      expect(result[0].toolSlot).toBe("tool_1");
      expect(result[1].toolSlot).toBe("tool_2");
      expect(result[2].toolSlot).toBe("tool_3");
    });
  });

  describe("calculateToolConnectionState", () => {
    test("calculates correct state with no connections", () => {
      const nodes = [createMockNode("agentic1", "AgenticAI", "AI Agent")];
      const edges: Edge[] = [];

      const result = calculateToolConnectionState("agentic1", edges, nodes);
      
      expect(result.hasConnectedTools).toBe(false);
      expect(result.connectedToolCount).toBe(0);
      expect(result.toolConnections).toEqual([]);
      expect(result.availableToolSlots).toHaveLength(10);
      expect(result.availableToolSlots[0]).toBe("tool_1");
    });

    test("calculates correct state with connections", () => {
      const nodes = [
        createMockNode("agentic1", "AgenticAI", "AI Agent"),
        createMockNode("tool1", "Tool1", "Tool 1"),
        createMockNode("tool2", "Tool2", "Tool 2"),
      ];
      const edges = [
        createMockEdge("edge1", "tool1", "agentic1", "output", "tool_1"),
        createMockEdge("edge2", "tool2", "agentic1", "output", "tool_3"),
      ];

      const result = calculateToolConnectionState("agentic1", edges, nodes);
      
      expect(result.hasConnectedTools).toBe(true);
      expect(result.connectedToolCount).toBe(2);
      expect(result.toolConnections).toHaveLength(2);
      expect(result.availableToolSlots).toHaveLength(8);
      expect(result.availableToolSlots).toContain("tool_2");
      expect(result.availableToolSlots).not.toContain("tool_1");
      expect(result.availableToolSlots).not.toContain("tool_3");
    });
  });

  describe("isNodeConnectedAsTool", () => {
    test("returns true when node is connected as tool", () => {
      const edges = [
        createMockEdge("edge1", "node1", "agentic1", "output", "tool_1"),
      ];

      expect(isNodeConnectedAsTool("node1", edges)).toBe(true);
    });

    test("returns false when node is not connected as tool", () => {
      const edges = [
        createMockEdge("edge1", "node1", "agentic1", "output", "input_data"),
      ];

      expect(isNodeConnectedAsTool("node1", edges)).toBe(false);
    });
  });

  describe("getNodesConnectedAsTools", () => {
    test("returns correct set of nodes connected as tools", () => {
      const edges = [
        createMockEdge("edge1", "node1", "agentic1", "output", "tool_1"),
        createMockEdge("edge2", "node2", "agentic1", "output", "tool_2"),
        createMockEdge("edge3", "node3", "agentic1", "output", "input_data"),
      ];

      const result = getNodesConnectedAsTools(edges);
      
      expect(result.size).toBe(2);
      expect(result.has("node1")).toBe(true);
      expect(result.has("node2")).toBe(true);
      expect(result.has("node3")).toBe(false);
    });
  });

  describe("generateToolStyleClasses", () => {
    test("generates correct classes for AgenticAI with tools", () => {
      const result = generateToolStyleClasses(true, 3, false);
      
      expect(result.agenticAIWithTools).toBe("agentic-ai-has-tools");
      expect(result.connectedAsTool).toBe("");
      expect(result.toolCountBadge).toBe("tool-count-badge");
    });

    test("generates correct classes for node connected as tool", () => {
      const result = generateToolStyleClasses(false, 0, true);
      
      expect(result.agenticAIWithTools).toBe("");
      expect(result.connectedAsTool).toBe("connected-as-tool");
      expect(result.toolCountBadge).toBe("");
    });

    test("generates empty classes when no tool connections", () => {
      const result = generateToolStyleClasses(false, 0, false);
      
      expect(result.agenticAIWithTools).toBe("");
      expect(result.connectedAsTool).toBe("");
      expect(result.toolCountBadge).toBe("");
    });
  });
});
