/**
 * Tests for credential migration utility
 * Following TDD methodology
 */

import {
  getLocalStorageCredentials,
  isMigrationNeeded,
  migrateCredentials,
  clearLocalStorageCredentials,
  backupLocalStorageCredentials,
  restoreFromBackup,
  getMigrationStats,
  markMigrationCompleted,
} from '../credentialMigration';
import { createCredential, fetchCredentials } from '../../lib/api';

// Mock the API functions
jest.mock('../../lib/api');
const mockCreateCredential = createCredential as jest.MockedFunction<typeof createCredential>;
const mockFetchCredentials = fetchCredentials as jest.MockedFunction<typeof fetchCredentials>;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

describe('credentialMigration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    mockLocalStorage.setItem.mockImplementation(() => {});
    mockLocalStorage.removeItem.mockImplementation(() => {});
  });

  describe('getLocalStorageCredentials', () => {
    it('should return empty array when no credentials in localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      
      const result = getLocalStorageCredentials();
      
      expect(result).toEqual([]);
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('credentials');
    });

    it('should return parsed credentials from localStorage', () => {
      const mockCredentials = [
        { name: 'API Key 1', value: 'secret1' },
        { name: 'API Key 2', value: 'secret2' },
      ];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockCredentials));
      
      const result = getLocalStorageCredentials();
      
      expect(result).toEqual(mockCredentials);
    });

    it('should handle invalid JSON gracefully', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid json');
      
      const result = getLocalStorageCredentials();
      
      expect(result).toEqual([]);
    });

    it('should handle non-array data gracefully', () => {
      mockLocalStorage.getItem.mockReturnValue('{"not": "array"}');
      
      const result = getLocalStorageCredentials();
      
      expect(result).toEqual([]);
    });
  });

  describe('isMigrationNeeded', () => {
    it('should return false when no local credentials exist', async () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      
      const result = await isMigrationNeeded();
      
      expect(result.needed).toBe(false);
      expect(result.localCount).toBe(0);
      expect(result.reason).toContain('No credentials in localStorage');
    });

    it('should return false when API already has credentials', async () => {
      const localCredentials = [{ name: 'Local Key', value: 'secret' }];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(localCredentials));
      mockFetchCredentials.mockResolvedValue({
        credentials: [{ id: 'api-1', name: 'API Key', createdAt: '', updatedAt: '', lastUsedAt: '' }],
      });
      
      const result = await isMigrationNeeded();
      
      expect(result.needed).toBe(false);
      expect(result.localCount).toBe(1);
      expect(result.apiCount).toBe(1);
      expect(result.reason).toContain('API already has credentials');
    });

    it('should return true when local credentials exist and API is empty', async () => {
      const localCredentials = [{ name: 'Local Key', value: 'secret' }];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(localCredentials));
      mockFetchCredentials.mockResolvedValue({ credentials: [] });
      
      const result = await isMigrationNeeded();
      
      expect(result.needed).toBe(true);
      expect(result.localCount).toBe(1);
      expect(result.apiCount).toBe(0);
      expect(result.reason).toContain('Found 1 credentials in localStorage');
    });

    it('should handle API errors gracefully', async () => {
      const localCredentials = [{ name: 'Local Key', value: 'secret' }];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(localCredentials));
      mockFetchCredentials.mockRejectedValue(new Error('API Error'));
      
      const result = await isMigrationNeeded();
      
      expect(result.needed).toBe(false);
      expect(result.reason).toContain('Unable to check migration status');
    });
  });

  describe('migrateCredentials', () => {
    it('should migrate valid credentials successfully', async () => {
      const localCredentials = [
        { name: 'API Key 1', value: 'secret1', description: 'First key' },
        { name: 'API Key 2', value: 'secret2', description: 'Second key' },
      ];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(localCredentials));
      mockCreateCredential.mockResolvedValue({
        id: 'new-id',
        name: 'API Key 1',
        description: 'First key',
        createdAt: '',
        updatedAt: '',
        lastUsedAt: '',
      });

      const progressCallback = jest.fn();
      const result = await migrateCredentials(progressCallback);

      expect(result.success).toBe(true);
      expect(result.migratedCount).toBe(2);
      expect(result.skippedCount).toBe(0);
      expect(result.errorCount).toBe(0);
      expect(mockCreateCredential).toHaveBeenCalledTimes(2);
      expect(progressCallback).toHaveBeenCalled();
    });

    it('should skip invalid credentials', async () => {
      const localCredentials = [
        { name: 'Valid Key', value: 'secret1' },
        { name: 'Invalid Key' }, // No value
        null, // Invalid credential
      ];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(localCredentials));
      mockCreateCredential.mockResolvedValue({
        id: 'new-id',
        name: 'Valid Key',
        description: '',
        createdAt: '',
        updatedAt: '',
        lastUsedAt: '',
      });

      const result = await migrateCredentials();

      expect(result.migratedCount).toBe(1);
      expect(result.skippedCount).toBe(2);
      expect(result.errorCount).toBe(0);
    });

    it('should handle API errors during migration', async () => {
      const localCredentials = [
        { name: 'API Key 1', value: 'secret1' },
        { name: 'API Key 2', value: 'secret2' },
      ];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(localCredentials));
      mockCreateCredential
        .mockResolvedValueOnce({
          id: 'new-id',
          name: 'API Key 1',
          description: '',
          createdAt: '',
          updatedAt: '',
          lastUsedAt: '',
        })
        .mockRejectedValueOnce(new Error('API Error'));

      const result = await migrateCredentials();

      expect(result.migratedCount).toBe(1);
      expect(result.errorCount).toBe(1);
      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
    });

    it('should handle empty localStorage gracefully', async () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const result = await migrateCredentials();

      expect(result.success).toBe(true);
      expect(result.migratedCount).toBe(0);
      expect(result.details).toContain('No credentials found in localStorage');
    });

    it('should call progress callback with correct data', async () => {
      const localCredentials = [{ name: 'API Key', value: 'secret' }];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(localCredentials));
      mockCreateCredential.mockResolvedValue({
        id: 'new-id',
        name: 'API Key',
        description: '',
        createdAt: '',
        updatedAt: '',
        lastUsedAt: '',
      });

      const progressCallback = jest.fn();
      await migrateCredentials(progressCallback);

      expect(progressCallback).toHaveBeenCalledWith({
        total: 1,
        processed: 0,
        current: 'Preparing migration...',
        status: 'preparing',
      });

      expect(progressCallback).toHaveBeenCalledWith({
        total: 1,
        processed: 1,
        current: 'Migration completed',
        status: 'completed',
      });
    });
  });

  describe('clearLocalStorageCredentials', () => {
    it('should remove credentials from localStorage', () => {
      const result = clearLocalStorageCredentials();

      expect(result).toBe(true);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('credentials');
    });

    it('should handle localStorage errors gracefully', () => {
      mockLocalStorage.removeItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const result = clearLocalStorageCredentials();

      expect(result).toBe(false);
    });
  });

  describe('backupLocalStorageCredentials', () => {
    it('should create backup of credentials', () => {
      const credentials = [{ name: 'API Key', value: 'secret' }];
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(credentials));

      const backup = backupLocalStorageCredentials();

      expect(backup).toBeTruthy();
      const parsed = JSON.parse(backup!);
      expect(parsed.credentials).toEqual(credentials);
      expect(parsed.timestamp).toBeTruthy();
      expect(parsed.version).toBe('1.0');
    });

    it('should return null when no credentials exist', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const backup = backupLocalStorageCredentials();

      expect(backup).toBeNull();
    });
  });

  describe('restoreFromBackup', () => {
    it('should restore credentials from valid backup', () => {
      const credentials = [{ name: 'API Key', value: 'secret' }];
      const backup = JSON.stringify({
        timestamp: '2024-01-01T00:00:00Z',
        credentials,
        version: '1.0',
      });

      const result = restoreFromBackup(backup);

      expect(result).toBe(true);
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'credentials',
        JSON.stringify(credentials)
      );
    });

    it('should handle invalid backup format', () => {
      const result = restoreFromBackup('invalid json');

      expect(result).toBe(false);
    });

    it('should handle backup without credentials array', () => {
      const backup = JSON.stringify({ timestamp: '2024-01-01T00:00:00Z' });

      const result = restoreFromBackup(backup);

      expect(result).toBe(false);
    });
  });

  describe('getMigrationStats', () => {
    it('should return correct statistics', async () => {
      const localCredentials = [{ name: 'Local Key', value: 'secret' }];
      mockLocalStorage.getItem.mockReturnValueOnce(JSON.stringify(localCredentials));
      mockFetchCredentials.mockResolvedValue({
        credentials: [{ id: 'api-1', name: 'API Key', createdAt: '', updatedAt: '', lastUsedAt: '' }],
      });

      const stats = await getMigrationStats();

      expect(stats.localCredentials).toBe(1);
      expect(stats.apiCredentials).toBe(1);
      expect(stats.migrationRecommended).toBe(false);
    });

    it('should recommend migration when local exists and API is empty', async () => {
      const localCredentials = [{ name: 'Local Key', value: 'secret' }];
      mockLocalStorage.getItem.mockReturnValueOnce(JSON.stringify(localCredentials));
      mockFetchCredentials.mockResolvedValue({ credentials: [] });

      const stats = await getMigrationStats();

      expect(stats.migrationRecommended).toBe(true);
    });
  });

  describe('markMigrationCompleted', () => {
    it('should mark migration as completed', () => {
      markMigrationCompleted();

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'credential_migration_date',
        expect.any(String)
      );
    });
  });
});
