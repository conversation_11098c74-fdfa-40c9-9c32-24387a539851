/**
 * Tests for handle utilities
 */

import {
  calculateHandlePositions,
  optimizeHandleLayout,
  validateHandleConfiguration,
  getHandleSpacing,
  sortHandlesByPriority,
} from "../handleUtils";

describe("Handle Utilities", () => {
  describe("calculateHandlePositions", () => {
    test("calculates positions for basic handles", () => {
      const handles = [
        { name: "input1", display_name: "Input 1" },
        { name: "input2", display_name: "Input 2" },
        { name: "tool_1", display_name: "Tool 1" },
      ];

      const positions = calculateHandlePositions(handles, {
        nodeHeight: 120,
        nodeWidth: 200,
        spacing: 30,
      });

      expect(positions).toHaveLength(3);
      expect(positions[0]).toEqual({
        name: "input1",
        position: { top: 30, left: -5 },
        side: "left",
      });
      expect(positions[1]).toEqual({
        name: "input2",
        position: { top: 60, left: -5 },
        side: "left",
      });
      expect(positions[2]).toEqual({
        name: "tool_1",
        position: { top: 90, left: -5 },
        side: "left",
      });
    });

    test("handles right-side positioning", () => {
      const handles = [
        { name: "output1", display_name: "Output 1" },
        { name: "output2", display_name: "Output 2" },
      ];

      const positions = calculateHandlePositions(handles, {
        nodeHeight: 100,
        nodeWidth: 200,
        spacing: 25,
        side: "right",
      });

      expect(positions[0].position).toEqual({ top: 25, right: -5 });
      expect(positions[0].side).toBe("right");
      expect(positions[1].position).toEqual({ top: 50, right: -5 });
      expect(positions[1].side).toBe("right");
    });

    test("auto-adjusts spacing for many handles", () => {
      const handles = Array.from({ length: 10 }, (_, i) => ({
        name: `input${i}`,
        display_name: `Input ${i}`,
      }));

      const positions = calculateHandlePositions(handles, {
        nodeHeight: 200,
        nodeWidth: 200,
        autoSpacing: true,
      });

      expect(positions).toHaveLength(10);
      
      // Should auto-adjust spacing to fit within node height
      const maxTop = Math.max(...positions.map(p => p.position.top));
      expect(maxTop).toBeLessThanOrEqual(180); // Leave some margin
    });

    test("handles overflow with scrolling", () => {
      const handles = Array.from({ length: 20 }, (_, i) => ({
        name: `input${i}`,
        display_name: `Input ${i}`,
      }));

      const positions = calculateHandlePositions(handles, {
        nodeHeight: 150,
        nodeWidth: 200,
        enableScrolling: true,
        maxVisibleHandles: 8,
      });

      expect(positions).toHaveLength(8); // Only visible handles
      expect(positions.every(p => p.position.top && p.position.top <= 240)).toBe(true); // 8 * 30 = 240
    });
  });

  // Legacy dynamic tool handle tests removed - no longer needed with single handle approach

  describe("optimizeHandleLayout", () => {
    test("optimizes layout for better visual distribution", () => {
      const handles = [
        { name: "input1", display_name: "Input 1", priority: 1 },
        { name: "tool_1", display_name: "Tool 1", priority: 3 },
        { name: "input2", display_name: "Input 2", priority: 2 },
      ];

      const optimized = optimizeHandleLayout(handles, {
        nodeHeight: 120,
        groupByType: true,
        sortByPriority: true,
      });

      // Should group regular inputs first, then tools
      expect(optimized[0].name).toBe("input1");
      expect(optimized[1].name).toBe("input2");
      expect(optimized[2].name).toBe("tool_1");
    });

    test("balances handles across both sides", () => {
      const handles = Array.from({ length: 8 }, (_, i) => ({
        name: `input${i}`,
        display_name: `Input ${i}`,
      }));

      const optimized = optimizeHandleLayout(handles, {
        nodeHeight: 120,
        balanceSides: true,
        maxPerSide: 4,
      });

      const leftSide = optimized.filter(h => h.side === "left");
      const rightSide = optimized.filter(h => h.side === "right");

      expect(leftSide).toHaveLength(4);
      expect(rightSide).toHaveLength(4);
    });

    test("maintains tool handle grouping", () => {
      const handles = [
        { name: "input1", display_name: "Input 1" },
        { name: "tool_1", display_name: "Tool 1" },
        { name: "tool_2", display_name: "Tool 2" },
        { name: "input2", display_name: "Input 2" },
      ];

      const optimized = optimizeHandleLayout(handles, {
        groupToolHandles: true,
      });

      const toolHandleIndices = optimized
        .map((h, i) => h.name.startsWith("tool_") ? i : -1)
        .filter(i => i !== -1);

      // Tool handles should be grouped together
      expect(toolHandleIndices).toEqual([2, 3]);
    });
  });

  describe("validateHandleConfiguration", () => {
    test("validates correct configuration", () => {
      const config = {
        maxHandles: 10,
        maxToolHandles: 5,
        spacing: 25,
        nodeHeight: 200,
      };

      expect(() => validateHandleConfiguration(config)).not.toThrow();
    });

    test("throws error for invalid configuration", () => {
      expect(() => validateHandleConfiguration({
        maxHandles: -1,
      })).toThrow("maxHandles must be positive");

      expect(() => validateHandleConfiguration({
        maxHandles: 5,
        maxToolHandles: 10,
      })).toThrow("maxToolHandles cannot exceed maxHandles");

      expect(() => validateHandleConfiguration({
        spacing: 0,
      })).toThrow("spacing must be positive");
    });

    test("validates node dimensions", () => {
      expect(() => validateHandleConfiguration({
        nodeHeight: 50,
        minNodeHeight: 100,
      })).toThrow("nodeHeight is too small");

      expect(() => validateHandleConfiguration({
        nodeWidth: 100,
        minNodeWidth: 150,
      })).toThrow("nodeWidth is too small");
    });
  });

  describe("getHandleSpacing", () => {
    test("calculates optimal spacing for given parameters", () => {
      const spacing = getHandleSpacing({
        numHandles: 5,
        nodeHeight: 150,
        minSpacing: 20,
        maxSpacing: 40,
      });

      expect(spacing).toBeGreaterThanOrEqual(20);
      expect(spacing).toBeLessThanOrEqual(40);
      expect(spacing * 5).toBeLessThanOrEqual(150); // Should fit in node
    });

    test("returns minimum spacing when handles don't fit", () => {
      const spacing = getHandleSpacing({
        numHandles: 10,
        nodeHeight: 100,
        minSpacing: 15,
        maxSpacing: 30,
      });

      expect(spacing).toBe(15); // Should use minimum
    });

    test("returns maximum spacing when there's plenty of room", () => {
      const spacing = getHandleSpacing({
        numHandles: 3,
        nodeHeight: 200,
        minSpacing: 20,
        maxSpacing: 50,
      });

      expect(spacing).toBe(50); // Should use maximum
    });
  });

  describe("sortHandlesByPriority", () => {
    test("sorts handles by priority correctly", () => {
      const handles = [
        { name: "low", priority: 3 },
        { name: "high", priority: 1 },
        { name: "medium", priority: 2 },
      ];

      const sorted = sortHandlesByPriority(handles);

      expect(sorted.map(h => h.name)).toEqual(["high", "medium", "low"]);
    });

    test("handles missing priority values", () => {
      const handles = [
        { name: "no_priority" },
        { name: "with_priority", priority: 1 },
      ];

      const sorted = sortHandlesByPriority(handles);

      expect(sorted[0].name).toBe("with_priority");
      expect(sorted[1].name).toBe("no_priority");
    });

    test("maintains stable sort for equal priorities", () => {
      const handles = [
        { name: "first", priority: 2 },
        { name: "second", priority: 2 },
        { name: "third", priority: 2 },
      ];

      const sorted = sortHandlesByPriority(handles);

      expect(sorted.map(h => h.name)).toEqual(["first", "second", "third"]);
    });
  });
});
