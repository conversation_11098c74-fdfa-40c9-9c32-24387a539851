import { ComponentDefinition } from "@/types";
import { toast } from "sonner";

export interface MCPValidationResult {
  isValid: boolean;
  requiresOutputSchema: boolean;
  requiresEnvKeys: boolean;
  toolName: string;
  definition: ComponentDefinition;
}

/**
 * Validates an MCP tool before it can be added to the canvas
 * @param definition The component definition to validate
 * @returns Validation result with details about what's required
 */
export function validateMCPTool(definition: ComponentDefinition): MCPValidationResult {
  // Check if this is an MCP tool
  if (definition.type !== "MCP" || !definition.mcp_info) {
    return {
      isValid: true,
      requiresOutputSchema: false,
      requiresEnvKeys: false,
      toolName: definition.display_name,
      definition,
    };
  }

  const toolName = definition.display_name.split(" - ")[1] || definition.display_name;
  const isMCPServer = definition.mcp_info.tool_name === "server";

  // Check if output schema is required
  const requiresOutputSchema = !definition.mcp_info.output_schema && !isMCPServer;

  // Check if environment keys are required
  const requiresEnvKeys = 
    !!(definition.env_keys && 
    definition.env_keys.length > 0 && 
    definition.env_credential_status !== "provided");

  const isValid = !requiresOutputSchema && !requiresEnvKeys;

  return {
    isValid,
    requiresOutputSchema,
    requiresEnvKeys,
    toolName,
    definition,
  };
}

/**
 * Shows appropriate validation message and returns validation result
 * @param definition The component definition to validate
 * @returns Validation result
 */
export function validateAndShowMessage(definition: ComponentDefinition): MCPValidationResult {
  const validation = validateMCPTool(definition);

  if (!validation.isValid) {
    if (validation.requiresOutputSchema) {
      toast.info(
        `Please define an output schema for ${validation.toolName} before adding it to the canvas.`
      );
    } else if (validation.requiresEnvKeys) {
      toast.info(
        `Please provide environment keys for ${validation.toolName} before adding it to the canvas.`
      );
    }
  }

  return validation;
} 