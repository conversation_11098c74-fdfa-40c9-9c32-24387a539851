/**
 * Integration tests for credential API functions
 * Tests the real API integration (mock system has been removed)
 */

import {
  fetchCredentials,
  createCredential,
  deleteCredential,
  getCredentialValueForExecution,
} from '../api';
import { credentialService } from '../../services/credentialService';

// Mock the credential service
jest.mock('../../services/credentialService');
const mockCredentialService = credentialService as jest.Mocked<typeof credentialService>;

describe('API Credential Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchCredentials', () => {
    it('should call credential service to fetch credentials', async () => {
      const mockResponse = {
        credentials: [{
          id: '1',
          name: 'Test',
          description: 'Test',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          lastUsedAt: '2024-01-01T00:00:00Z'
        }]
      };
      mockCredentialService.fetchCredentials.mockResolvedValue(mockResponse);

      const result = await fetchCredentials();

      expect(mockCredentialService.fetchCredentials).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should propagate errors from credential service', async () => {
      const error = new Error('API Error');
      mockCredentialService.fetchCredentials.mockRejectedValue(error);

      await expect(fetchCredentials()).rejects.toThrow('API Error');
    });
  });

  describe('createCredential', () => {
    const credentialData = { name: 'Test Credential', value: 'test-value', description: 'Test' };

    it('should call credential service to create credential', async () => {
      const mockResponse = {
        id: '1',
        name: 'Test Credential',
        description: 'Test',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        lastUsedAt: '2024-01-01T00:00:00Z'
      };
      mockCredentialService.createCredential.mockResolvedValue(mockResponse);

      const result = await createCredential(credentialData);

      expect(mockCredentialService.createCredential).toHaveBeenCalledWith(credentialData);
      expect(result).toEqual(mockResponse);
    });

    it('should propagate errors from credential service', async () => {
      const error = new Error('Creation failed');
      mockCredentialService.createCredential.mockRejectedValue(error);

      await expect(createCredential(credentialData)).rejects.toThrow('Creation failed');
    });
  });

  describe('deleteCredential', () => {
    const credentialId = 'test-id';

    it('should call credential service to delete credential', async () => {
      mockCredentialService.deleteCredential.mockResolvedValue();

      const result = await deleteCredential(credentialId);

      expect(mockCredentialService.deleteCredential).toHaveBeenCalledWith(credentialId);
      expect(result).toEqual({ success: true });
    });

    it('should propagate errors from credential service', async () => {
      const error = new Error('Deletion failed');
      mockCredentialService.deleteCredential.mockRejectedValue(error);

      await expect(deleteCredential(credentialId)).rejects.toThrow('Deletion failed');
    });
  });

  describe('getCredentialValueForExecution', () => {
    const credentialId = 'test-id';

    it('should call credential service to get credential value', async () => {
      const mockValue = 'secret-value';
      mockCredentialService.getCredentialValueForExecution.mockResolvedValue(mockValue);

      const result = await getCredentialValueForExecution(credentialId);

      expect(mockCredentialService.getCredentialValueForExecution).toHaveBeenCalledWith(credentialId);
      expect(result).toBe(mockValue);
    });

    it('should propagate errors from credential service', async () => {
      const error = new Error('Value retrieval failed');
      mockCredentialService.getCredentialValueForExecution.mockRejectedValue(error);

      await expect(getCredentialValueForExecution(credentialId)).rejects.toThrow('Value retrieval failed');
    });
  });
});
