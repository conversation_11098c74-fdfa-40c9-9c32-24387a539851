import { InputDefinition } from "@/types";

/**
 * JSON Schema format to input type mapping
 */
export interface JsonSchemaProperty {
  type: string;
  format?: string;
  enum?: any[];
  properties?: Record<string, JsonSchemaProperty>;
  items?: JsonSchemaProperty;
  required?: string[];
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  default?: any;
  description?: string;
  title?: string;
}

/**
 * Input component mapping configuration
 */
export interface InputComponentMapping {
  component: string; // The component to use (e.g., 'StringInput', 'NumberInput')
  inputType: string; // The input_type to set on the InputDefinition
  props?: Record<string, any>; // Additional props to pass to the component
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    required?: boolean;
  };
}

/**
 * Dynamic input type mapping registry
 * Maps JSON Schema types and formats to appropriate input components
 */
export class DynamicInputTypeMapper {
  private static mappings: Map<string, InputComponentMapping> = new Map([
    // String types with formats
    ['string:uri', { component: 'StringInput', inputType: 'string', validation: { pattern: '^https?://.+' } }],
    ['string:url', { component: 'StringInput', inputType: 'string', validation: { pattern: '^https?://.+' } }],
    ['string:email', { component: 'StringInput', inputType: 'string', validation: { pattern: '^[^@]+@[^@]+\\.[^@]+$' } }],
    ['string:date', { component: 'StringInput', inputType: 'string', props: { type: 'date' } }],
    ['string:time', { component: 'StringInput', inputType: 'string', props: { type: 'time' } }],
    ['string:datetime', { component: 'StringInput', inputType: 'string', props: { type: 'datetime-local' } }],
    ['string:password', { component: 'StringInput', inputType: 'string', props: { type: 'password' } }],
    ['string:uuid', { component: 'StringInput', inputType: 'string', validation: { pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' } }],
    ['string:ipv4', { component: 'StringInput', inputType: 'string', validation: { pattern: '^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$' } }],
    ['string:ipv6', { component: 'StringInput', inputType: 'string' }],
    ['string:hostname', { component: 'StringInput', inputType: 'string' }],
    ['string:json-pointer', { component: 'StringInput', inputType: 'string' }],
    ['string:regex', { component: 'StringInput', inputType: 'string' }],

    // String with enum becomes dropdown
    ['string:enum', { component: 'SelectInput', inputType: 'dropdown' }],

    // Basic string
    ['string', { component: 'StringInput', inputType: 'string' }],

    // Built-in component input types (for backward compatibility)
    ['password', { component: 'StringInput', inputType: 'string', props: { type: 'password' } }],
    ['url', { component: 'StringInput', inputType: 'string', validation: { pattern: '^https?://.+' } }],
    ['email', { component: 'StringInput', inputType: 'string', validation: { pattern: '^[^@]+@[^@]+\\.[^@]+$' } }],

    // Number types
    ['number', { component: 'NumberInput', inputType: 'number' }],
    ['integer', { component: 'NumberInput', inputType: 'int' }],
    ['int', { component: 'NumberInput', inputType: 'int' }],
    ['float', { component: 'NumberInput', inputType: 'float' }],

    // Boolean types
    ['boolean', { component: 'BooleanInput', inputType: 'bool' }],
    ['bool', { component: 'BooleanInput', inputType: 'bool' }],

    // Selection types
    ['dropdown', { component: 'SelectInput', inputType: 'dropdown' }],
    ['select', { component: 'SelectInput', inputType: 'dropdown' }],

    // Object types
    ['object', { component: 'ObjectInput', inputType: 'object' }],
    ['dict', { component: 'ObjectInput', inputType: 'object' }],
    ['json', { component: 'ObjectInput', inputType: 'object' }],

    // Array types
    ['array', { component: 'ArrayInput', inputType: 'array' }],
    ['list', { component: 'ArrayInput', inputType: 'array' }],

    // File type
    ['file', { component: 'StringInput', inputType: 'string', props: { type: 'file' } }],

    // Null type (treat as optional string)
    ['null', { component: 'StringInput', inputType: 'string', props: { placeholder: 'Optional field' } }],
  ]);

  /**
   * Get the appropriate input component mapping for a JSON Schema property
   */
  static getMapping(schema: JsonSchemaProperty): InputComponentMapping {
    // First resolve any anyOf structures to get the actual schema to work with
    const resolvedSchema = resolveAnyOfSchema(schema);
    const { type, format, enum: enumValues } = resolvedSchema;

    // Handle enum types (convert to dropdown)
    if (enumValues && enumValues.length > 0) {
      return {
        component: 'SelectInput',
        inputType: 'dropdown',
        props: { options: enumValues.map(val => ({ value: val, label: String(val) })) }
      };
    }

    // Create lookup key
    const key = format ? `${type}:${format}` : type;

    // Get mapping or fallback
    const mapping = this.mappings.get(key) || this.getFallbackMapping(type);

    // Enhance mapping with schema-specific validation using resolved schema
    return this.enhanceMapping(mapping, resolvedSchema);
  }

  /**
   * Get fallback mapping for unknown types
   */
  private static getFallbackMapping(type: string): InputComponentMapping {
    // Fallback logic based on type patterns
    if (type?.includes('string') || type?.includes('text')) {
      return { component: 'StringInput', inputType: 'string' };
    }
    if (type?.includes('number') || type?.includes('int') || type?.includes('float')) {
      return { component: 'NumberInput', inputType: 'number' };
    }
    if (type?.includes('bool') || type?.includes('flag')) {
      return { component: 'BooleanInput', inputType: 'bool' };
    }
    if (type?.includes('object') || type?.includes('dict') || type?.includes('json')) {
      return { component: 'ObjectInput', inputType: 'object' };
    }
    if (type?.includes('array') || type?.includes('list')) {
      return { component: 'ArrayInput', inputType: 'array' };
    }
    
    // Ultimate fallback - string input
    return { component: 'StringInput', inputType: 'string' };
  }

  /**
   * Enhance mapping with schema-specific validation and properties
   */
  private static enhanceMapping(mapping: InputComponentMapping, schema: JsonSchemaProperty): InputComponentMapping {
    const enhanced = { ...mapping };
    
    // Add validation from schema
    if (!enhanced.validation) enhanced.validation = {};
    
    if (schema.minimum !== undefined) enhanced.validation.min = schema.minimum;
    if (schema.maximum !== undefined) enhanced.validation.max = schema.maximum;
    if (schema.minLength !== undefined) enhanced.validation.minLength = schema.minLength;
    if (schema.maxLength !== undefined) enhanced.validation.maxLength = schema.maxLength;
    if (schema.pattern) enhanced.validation.pattern = schema.pattern;
    
    // Add props from schema
    if (!enhanced.props) enhanced.props = {};
    
    if (schema.default !== undefined) enhanced.props.defaultValue = schema.default;
    if (schema.description) enhanced.props.description = schema.description;
    if (schema.title) enhanced.props.title = schema.title;
    
    return enhanced;
  }

  /**
   * Convert JSON Schema property to InputDefinition
   */
  static schemaToInputDefinition(
    name: string,
    schema: JsonSchemaProperty,
    required: boolean = false,
    displayName?: string
  ): InputDefinition {
    // Resolve anyOf structures first
    const resolvedSchema = resolveAnyOfSchema(schema);
    const mapping = this.getMapping(schema);

    return {
      name,
      display_name: displayName || resolvedSchema.title || name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      input_type: mapping.inputType,
      info: resolvedSchema.description,
      value: resolvedSchema.default,
      required,
      options: mapping.props?.options,
      // Add validation properties
      min_value: mapping.validation?.min,
      max_value: mapping.validation?.max,
      min_length: mapping.validation?.minLength,
      max_length: mapping.validation?.maxLength,
      pattern: mapping.validation?.pattern,
      // Additional properties for complex types
      properties: resolvedSchema.properties,
      // Mark as handle if needed (can be overridden)
      is_handle: false,
      is_list: resolvedSchema.type === 'array',
      advanced: false,
      real_time_refresh: false,
      input_types: [mapping.inputType],
      visibility_logic: 'OR'
    };
  }

  /**
   * Register a new input type mapping
   */
  static registerMapping(key: string, mapping: InputComponentMapping): void {
    this.mappings.set(key, mapping);
  }

  /**
   * Get all registered mappings (for debugging/inspection)
   */
  static getAllMappings(): Map<string, InputComponentMapping> {
    return new Map(this.mappings);
  }

  /**
   * Check if a mapping exists for a given key
   */
  static hasMapping(key: string): boolean {
    return this.mappings.has(key);
  }
}

/**
 * Utility function to convert a complete JSON Schema to InputDefinitions
 */
export function convertJsonSchemaToInputDefinitions(
  schema: { properties?: Record<string, JsonSchemaProperty>; required?: string[] }
): InputDefinition[] {
  if (!schema.properties) return [];
  
  const required = schema.required || [];
  
  return Object.entries(schema.properties).map(([name, property]) =>
    DynamicInputTypeMapper.schemaToInputDefinition(
      name,
      property,
      required.includes(name)
    )
  );
}

/**
 * Resolves $ref references in JSON Schema using $defs definitions.
 */
export function resolveSchemaReferences(
  schema: JsonSchemaProperty,
  definitions: Record<string, any> = {}
): JsonSchemaProperty {
  if (!schema || typeof schema !== 'object') {
    return schema;
  }

  // Handle direct $ref
  if (schema.$ref && typeof schema.$ref === 'string') {
    if (schema.$ref.startsWith("#/$defs/")) {
      const defName = schema.$ref.replace("#/$defs/", "");
      if (definitions[defName]) {
        // Preserve original properties and merge with resolved reference
        const { $ref, ...originalProps } = schema;
        const resolvedRef = resolveSchemaReferences(definitions[defName], definitions);
        return { ...resolvedRef, ...originalProps };
      }
    }
  }

  // Handle nested objects and arrays
  if (Array.isArray(schema)) {
    return schema.map(item => resolveSchemaReferences(item, definitions)) as any;
  }

  // Recursively resolve references in nested objects
  const resolved: any = {};
  for (const [key, value] of Object.entries(schema)) {
    if (typeof value === 'object' && value !== null) {
      resolved[key] = resolveSchemaReferences(value, definitions);
    } else {
      resolved[key] = value;
    }
  }

  return resolved;
}

/**
 * Resolves anyOf structures in JSON Schema by finding the most appropriate non-null schema
 * while preserving enum values and other important properties.
 * Also handles $ref resolution if definitions are provided.
 */
export function resolveAnyOfSchema(
  schema: JsonSchemaProperty,
  definitions: Record<string, any> = {}
): JsonSchemaProperty {
  // First resolve any $ref references
  let resolvedSchema = resolveSchemaReferences(schema, definitions);

  if (!resolvedSchema.anyOf || !Array.isArray(resolvedSchema.anyOf)) {
    return resolvedSchema;
  }

  // Find the first non-null schema in anyOf
  let nonNullSchema = resolvedSchema.anyOf.find((subSchema: any) => subSchema.type !== "null");

  if (!nonNullSchema) {
    // If all schemas are null, return the original schema
    return resolvedSchema;
  }

  // Resolve $ref in the selected non-null schema
  nonNullSchema = resolveSchemaReferences(nonNullSchema, definitions);

  // Merge the original schema properties with the selected non-null schema
  // The non-null schema takes precedence for type-specific properties like enum
  const finalResolvedSchema: JsonSchemaProperty = {
    ...resolvedSchema,
    ...nonNullSchema,
    // Remove anyOf since we've resolved it
    anyOf: undefined
  };

  return finalResolvedSchema;
}

/**
 * Enhanced type mapping that handles nested schemas and complex types
 */
export function mapJsonSchemaTypeToInputType(schemaType: string, schema: any): string {
  const mapping = DynamicInputTypeMapper.getMapping({ type: schemaType, ...schema });
  return mapping.inputType;
}
