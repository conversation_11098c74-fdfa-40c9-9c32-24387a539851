// src/lib/api.ts
import { WorkflowNodeData, ComponentsApiResponse } from "@/types";
import { Node, Edge } from "reactflow";
import { transformWorkflowForOrchestration } from "./mcp_marketplace_transform";
import { useComponentStateStore } from "@/store/mcpToolsStore";
import { API_ENDPOINTS } from "./apiConfig";
import { getClientAccessToken } from "./clientCookies";
import { getAccessToken } from "./cookies";
import { getConnectedNodesWithToolConnections } from "./validation/toolConnectionFlow";
import { collectAllFields } from "./validation/fieldValidation";
import { MissingField } from "./validation/types";
import { shouldIncludeField, logFieldStatus } from "./field-utils";
import { isNodeConnectedAsTool } from "@/utils/toolConnectionUtils";
import { enhanceComponentsWithDynamicData } from './componentEnhancer';
import { Agent, PaginatedAgentResponse } from "../types/agents";
import { DynamicInputTypeMapper, resolveAnyOfSchema } from './dynamicInputTypeMapper';
import { processNodeConfigTypes } from "@/utils/valueFormatting";
// Use environment variables for API URLs
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
export const BACKEND_API_URL = API_BASE_URL;

// NOTE: The fetchAgents function has been moved to a dedicated file: agentApi.ts
// This ensures better separation of concerns and prevents duplicated code
// Import from "@/lib/agentApi" instead

// Debug function for MCP Tools
export async function debugMCPTools(data: any): Promise<any> {
  try {
    // Make sure the selected_tool_name is included in the node_config if available
    if (data.node_config && !data.node_config.selected_tool_name && data.node_config.inputs) {
      // Try to find the selected_tool_name in the inputs
      const toolInput = data.node_config.inputs.find(
        (input: any) => input.name === "selected_tool_name",
      );
      if (toolInput && toolInput.value) {
        data.node_config.selected_tool_name = toolInput.value;
        console.log(
          `Added selected_tool_name to debug payload: ${data.node_config.selected_tool_name}`,
        );
      }
    }

    console.log(`Sending debug MCP tools request with data:`, data);

    const response = await fetch(`${BACKEND_API_URL}/debug_mcp_tools`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`Debug MCP tools response:`, result);

    return result;
  } catch (error) {
    console.error("Failed to debug MCP Tools:", error);
    return { success: false, error: String(error) };
  }
}

// Direct function to fetch MCP tools
export async function fetchMCPTools(nodeConfig: any, buttonName: string): Promise<any> {
  try {
    // Create a clean config with only essential properties to avoid duplicating inputs
    const cleanConfig: any = {
      // Extract only the essential configuration values
      mode: nodeConfig.mode || "Stdio",
      command: nodeConfig.command || "",
      sse_url: nodeConfig.sse_url || "",
      selected_tool_name: nodeConfig.selected_tool_name || "",
      connection_status: nodeConfig.connection_status || "Not Connected",
    };

    // Save the original mode for all operations
    const originalMode = cleanConfig.mode;
    console.log(`Original mode before operation: ${originalMode}`);

    // Ensure SSE URL is properly included for SSE mode
    if (buttonName === "fetch_sse_tools") {
      console.log(`SSE URL from config: ${cleanConfig.sse_url}`);

      // Force mode to SSE for this button
      cleanConfig.mode = "SSE";
      console.log(`Forced mode to SSE for fetch_sse_tools button`);

      // If SSE URL is not in the config, log a warning
      if (!cleanConfig.sse_url) {
        console.warn("SSE URL is missing in the config!");
      }
    } else if (buttonName === "fetch_stdio_tools") {
      // Force mode to Stdio for this button
      cleanConfig.mode = "Stdio";
      console.log(`Forced mode to Stdio for fetch_stdio_tools button`);
    } else if (buttonName === "selected_tool_name") {
      // For tool selection, preserve the current mode and connection status
      console.log(`Tool selection action: ${buttonName}`);
      console.log(`Preserving current mode: ${cleanConfig.mode}`);
      console.log(`Preserving connection status: ${cleanConfig.connection_status}`);

      // Make sure connection_status is set to Connected for tool selection
      if (cleanConfig.connection_status !== "Connected") {
        console.log(`Setting connection_status to Connected for tool selection`);
        cleanConfig.connection_status = "Connected";
      }
    }
    // Don't change the mode for other buttons

    // Ensure connection_status is included
    if (!cleanConfig.connection_status) {
      cleanConfig.connection_status = "Not Connected";
    }

    // Create a payload that simulates what the backend expects
    const payload: any = {
      config: cleanConfig,
    };

    // Set the button value based on the button name
    if (buttonName === "selected_tool_name") {
      payload.selected_tool_name = cleanConfig.selected_tool_name;
      console.log(`Setting selected_tool_name in payload: ${cleanConfig.selected_tool_name}`);

      // Make sure connection_status is set to Connected
      if (cleanConfig.connection_status !== "Connected") {
        console.log(`Ensuring connection_status is Connected for tool selection`);
        cleanConfig.connection_status = "Connected";
        payload.config.connection_status = "Connected";
      }
    } else {
      payload[buttonName] = true; // Set the button value to true
    }

    // IMPORTANT: Always preserve the mode from the config for all operations
    // This ensures we don't reset to Stdio when selecting a tool or performing other operations
    console.log(`Preserving mode in payload: ${originalMode}`);
    payload.config.mode = originalMode;

    // Always make sure we include the selected_tool_name if available
    console.log(`Selected tool name from config: ${cleanConfig.selected_tool_name}`);
    if (!cleanConfig.selected_tool_name && cleanConfig.inputs) {
      // Try to find the selected_tool_name in the inputs
      const toolInput = cleanConfig.inputs.find(
        (input: any) => input.name === "selected_tool_name",
      );
      if (toolInput && toolInput.value) {
        cleanConfig.selected_tool_name = toolInput.value;
        console.log(`Found selected_tool_name in inputs: ${cleanConfig.selected_tool_name}`);
      }
    }

    // Make sure the selected_tool_name is included in the payload
    if (cleanConfig.selected_tool_name) {
      payload.config.selected_tool_name = cleanConfig.selected_tool_name;
      console.log(`Added selected_tool_name to payload: ${payload.config.selected_tool_name}`);
    }

    console.log(`Sending direct MCP tools fetch request with payload:`, payload);

    const response = await fetch(`${BACKEND_API_URL}/fetch_mcp_tools`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log(`MCP tools fetch response:`, responseData);

    // Extract the result from the response
    const result = responseData.result || responseData;

    // Process the result to ensure it's properly structured
    if (result && result.inputs) {
      console.log(`Processing result with ${result.inputs.length} inputs`);

      // Ensure mode is preserved in the result
      if (buttonName === "fetch_sse_tools") {
        // For SSE mode, ensure the mode is set to SSE
        result.mode = "SSE";

        // Also fix the mode in the inputs array
        if (Array.isArray(result.inputs)) {
          const modeInput = result.inputs.find((input: any) => input.name === "mode");
          if (modeInput) {
            console.log(`Setting mode input value to SSE`);
            modeInput.value = "SSE";
          }
        }
      } else if (buttonName === "fetch_stdio_tools") {
        // For Stdio mode, ensure the mode is set to Stdio
        result.mode = "Stdio";

        // Also fix the mode in the inputs array
        if (Array.isArray(result.inputs)) {
          const modeInput = result.inputs.find((input: any) => input.name === "mode");
          if (modeInput) {
            console.log(`Setting mode input value to Stdio`);
            modeInput.value = "Stdio";
          }
        }
      }

      // Store the tool schemas if available
      if (result._internal_state && result._internal_state.tool_schemas) {
        console.log(
          `Storing ${Object.keys(result._internal_state.tool_schemas).length} tool schemas`,
        );
        // You could store these in a global state if needed
      }
    }

    return responseData.success ? result : responseData;
  } catch (error) {
    console.error("Failed to fetch MCP tools:", error);
    return { success: false, error: String(error) };
  }
}

/**
 * Fetch raw components without enhancement
 */
export async function fetchRawComponents(): Promise<ComponentsApiResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/components?refresh=true`);
    console.log("Sent the request to fetch components with the url as :", response.url);
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }
    const data: ComponentsApiResponse = await response.json();
    console.log("Fetched raw components:", data); // For debugging
    return data;
  } catch (error) {
    console.error("Failed to fetch raw components:", error);
    // Return empty object or re-throw error based on how you want to handle failures
    return {};
  }
}

export async function fetchComponents(): Promise<ComponentsApiResponse> {
  try {
    const data = await fetchRawComponents();

    // Enhance components with dynamic provider and model data
    const enhancedData = await enhanceComponentsWithDynamicData(data);
    console.log("Enhanced components with dynamic data");

    return enhancedData;
  } catch (error) {
    console.error("Failed to fetch components:", error);
    // Return empty object or re-throw error based on how you want to handle failures
    return {};
  }
}

/**
 * Fetch MCP component categories only (for initial sidebar load)
 * @returns A promise that resolves to an array of category names
 */
export async function fetchMCPCategories(): Promise<string[]> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    // Fetch only the first page to get categories
    const response = await fetch(`${API_ENDPOINTS.MCPS.LIST}?page=1&page_size=50`, {
      method: "GET",
      headers: headers,
    });

    console.log("Sent the request to fetch MCP categories with the url as:", response.url);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log("Fetched MCP response for categories:", responseData);

    // Extract categories from the response
    let mcpData: any[] = [];
    if (Array.isArray(responseData)) {
      mcpData = responseData;
    } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
      mcpData = responseData.data;
    } else {
      console.error("MCP response is not in expected format for categories");
      return [];
    }

    // Extract unique categories
    const categories = new Set<string>();
    mcpData.forEach((mcp: any) => {
      if (mcp.component_category && mcp.component_category.trim()) {
        categories.add(mcp.component_category.trim());
      } else {
        categories.add("Tools"); // Category for components with null component_category
      }
    });

    const categoryList = Array.from(categories);
    console.log("Extracted MCP categories:", categoryList);
    return categoryList;
  } catch (error) {
    console.error("Failed to fetch MCP categories:", error);
    return [];
  }
}

/**
 * Fetch MCP components for a specific category
 * @param category The category to fetch components for
 * @returns A promise that resolves to a ComponentsApiResponse object
 */
export async function fetchMCPComponentsByCategory(category: string, page: number = 1, pageSize: number = 10): Promise<ComponentsApiResponse> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    // Handle the "Tools" category specially - it represents components with null component_category
    let url;
    if (category === "Tools") {
      // For "Tools" category, call the API without component_category parameter
      // This will naturally return components with null component_category
      url = `${API_ENDPOINTS.MCPS.LIST}?page=${page}&page_size=${pageSize}`;
    } else {
      // Encode the category for URL
      const encodedCategory = encodeURIComponent(category);
      url = `${API_ENDPOINTS.MCPS.LIST}?page=${page}&page_size=${pageSize}&component_category=${encodedCategory}`;
    }
    
    const response = await fetch(url, {
      method: "GET",
      headers: headers,
    });

    console.log(`Sent the request to fetch MCP components for category "${category}" with the url as:`, response.url);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log(`Fetched MCP response for category "${category}":`, responseData);

    // Check if the response is in the expected format
    let mcpData: any[] = [];
    if (Array.isArray(responseData)) {
      mcpData = responseData;
    } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
      mcpData = responseData.data;
    } else {
      console.error(`MCP response for category "${category}" is not in expected format`);
      return {};
    }

    console.log(`Processed MCP data for category "${category}":`, mcpData);

    // Transform the MCP array into the ComponentsApiResponse format
    const mcpComponents: ComponentsApiResponse = {};

    // Process each MCP component for this category
    mcpData.forEach((mcp: any) => {
      console.log(`Processing MCP: ${mcp.name}, component_category: "${mcp.component_category}"`);

      // For "Tools" category, we want components that either:
      // 1. Have no component_category field
      // 2. Have an empty/null component_category
      // 3. Have component_category that doesn't match any specific category
      if (category === "Tools") {
        // Check if this component has a specific category assigned
        if (mcp.component_category && mcp.component_category.trim()) {
          // Skip components that have a specific category assigned
          console.log(`Skipping MCP ${mcp.name} - has component_category "${mcp.component_category}" but requested Tools category`);
          return;
        }
        // If we reach here, the component has no specific category, so it belongs to Tools
        console.log(`Including MCP ${mcp.name} in Tools category (no specific category assigned)`);
      } else {
        // For other categories, check if the component belongs to this category
        if (mcp.component_category && mcp.component_category.trim() !== category) {
          console.log(`Skipping MCP ${mcp.name} - has component_category "${mcp.component_category}" but requested ${category} category`);
          return;
        }
        console.log(`Including MCP ${mcp.name} in category "${category}"`);
      }

      // Determine the category key for grouping
      let categoryKey = category;

      // Filter components for "Tools" category - only include those with null component_category
      if (category === "Tools" && mcp.component_category && mcp.component_category.trim()) {
        console.log(`Skipping MCP ${mcp.name} - has component_category "${mcp.component_category}" but requested Tools category`);
        return; // Skip this component as it doesn't belong to Tools category
      }

      // For each tool in the MCP, create a component
      if (
        mcp.mcp_tools_config &&
        mcp.mcp_tools_config.tools &&
        mcp.mcp_tools_config.tools.length > 0
      ) {
        mcp.mcp_tools_config.tools.forEach((tool: any) => {
          // Create a unique component name
          const componentName = `MCP_${mcp.name}_${tool.name}`.replace(/\s+/g, "_");

          // Log tool information
          console.log(`  Tool: ${tool.name} -> Category: "${categoryKey}"`);
          console.log(`  Component name: ${componentName}`);

          // Initialize the category if it doesn't exist
          if (!mcpComponents[categoryKey]) {
            mcpComponents[categoryKey] = {};
          }

          // Create the component definition
          mcpComponents[categoryKey][componentName] = {
            name: componentName,
            display_name: `${mcp.name} - ${tool.name}`,
            description: tool.description || mcp.description,
            category: categoryKey,
            icon: "Cloud", // Default icon for MCP components
            beta: true,
            inputs: [],
            outputs: [],
            is_valid: true,
            path: `mcp.${componentName.toLowerCase()}`,
            type: "MCP",
            env_keys: mcp.env_keys || [],
            env_credential_status: mcp.env_credential_status || null,
            logo: mcp.logo || null,
            oauth_details: mcp.oauth_details || null,
            mcp_info: {
              server_id: mcp.id || "",
              server_path: mcp.url || "",
              tool_name: tool.name,
              input_schema: tool.input_schema,
              output_schema: tool.output_schema,
            },
          };

          // Convert input schema to input definitions
          if (tool.input_schema && tool.input_schema.properties) {
            const requiredFields = tool.input_schema.required || [];
            const definitions = tool.input_schema.$defs || {};

            Object.entries(tool.input_schema.properties).forEach(
              ([propName, propSchema]: [string, any]) => {
                // Handle anyOf schemas by finding the first non-null type
                const resolvedSchema = resolveAnyOfSchema(propSchema, definitions);
                const resolvedType = resolvedSchema.type;

                // Debug logging for array types and anyOf schemas
                if (resolvedType === "array" || propSchema.anyOf) {
                  console.log(`[API] Processing complex field ${propName}:`, {
                    originalType: propSchema.type,
                    resolvedType,
                    hasAnyOf: !!propSchema.anyOf,
                    propSchema,
                    resolvedSchema
                  });
                }

                const inputDef = {
                  name: propName,
                  display_name: resolvedSchema.title || propName.replace(/_/g, " "),
                  info: resolvedSchema.description || "",
                  input_type: mapJsonSchemaTypeToInputType(resolvedType, resolvedSchema),
                  input_types: null,
                  required: requiredFields.includes(propName),
                  is_handle: true,
                  is_list: resolvedType === "array",
                  real_time_refresh: false,
                  advanced: false,
                  value: resolvedSchema.default || null,
                  options: resolvedSchema.enum ? resolvedSchema.enum : null,
                  visibility_rules: null,
                  visibility_logic: "OR" as "OR" | "AND",
                };

                // Debug logging for final inputDef (arrays and complex schemas)
                if (resolvedType === "array" || propSchema.anyOf || inputDef.is_list) {
                  console.log(`[API] Final inputDef for ${propName}:`, {
                    name: inputDef.name,
                    display_name: inputDef.display_name,
                    input_type: inputDef.input_type,
                    is_list: inputDef.is_list,
                    resolvedType,
                    originalHadAnyOf: !!propSchema.anyOf
                  });
                }

                mcpComponents[categoryKey][componentName].inputs.push(inputDef);
              },
            );
          }

          // Add output definitions based on output schema
          if (tool.output_schema && tool.output_schema.properties) {
            Object.entries(tool.output_schema.properties).forEach(
              ([propName, propSchema]: [string, any]) => {
                const outputDef = {
                  name: propName,
                  display_name: propSchema.title || propName.replace(/_/g, " "),
                  output_type: mapJsonSchemaTypeToOutputType(propSchema.type),
                };

                mcpComponents[categoryKey][componentName].outputs.push(outputDef);
              },
            );
          }

          // If no outputs were defined, add a default output
          if (mcpComponents[categoryKey][componentName].outputs.length === 0) {
            mcpComponents[categoryKey][componentName].outputs.push({
              name: "result",
              display_name: "Result",
              output_type: "any",
            });
          }
        });
      } else {
        // If the MCP server doesn't have any tools, create a component for the server itself
        console.log(`MCP server ${mcp.name} has no tools, creating server component`);

        // Create a unique component name for the MCP server itself
        const componentName = `MCP_${mcp.name}_Server`.replace(/\s+/g, "_");

        // Initialize the category if it doesn't exist
        if (!mcpComponents[categoryKey]) {
          mcpComponents[categoryKey] = {};
        }

        // Create the component definition for the MCP server
        mcpComponents[categoryKey][componentName] = {
          name: componentName,
          display_name: mcp.name,
          description: mcp.description || `MCP Server: ${mcp.name}`,
          category: categoryKey,
          icon: "Cloud",
          beta: true,
          inputs: [],
          outputs: [
            {
              name: "result",
              display_name: "Result",
              output_type: "any",
            },
          ],
          is_valid: true,
          path: `mcp.${componentName.toLowerCase()}`,
          type: "MCP",
          env_keys: mcp.env_keys || [],
          env_credential_status: mcp.env_credential_status || null,
          oauth_details: mcp.oauth_details || null,
          mcp_info: {
            server_id: mcp.id || "",
            server_path: mcp.url || "",
            tool_name: "server",
            input_schema: null,
            output_schema: { type: "object", properties: {} },
          },
        };

        console.log(
          `  Created MCP Server component: ${componentName} in category: "${categoryKey}"`,
        );
      }
    });

    console.log(`Transformed MCP components for category "${category}":`, mcpComponents);
    return mcpComponents;
  } catch (error) {
    console.error(`Failed to fetch MCP components for category "${category}":`, error);
    return {};
  }
}

/**
 * Fetch additional MCP components for a specific category with pagination
 * This function merges new results with existing ones for pagination
 * @param category The category to fetch components for
 * @param page The page number to fetch
 * @param pageSize The number of items per page
 * @returns A promise that resolves to a ComponentsApiResponse object with new components
 */
export async function fetchMCPComponentsByCategoryPaginated(
  category: string,
  page: number,
  pageSize: number = 5
): Promise<ComponentsApiResponse> {
  return fetchMCPComponentsByCategory(category, page, pageSize);
}

/**
 * Fetch MCP (Marketplace Components) from the API (legacy function - loads all components)
 * @returns A promise that resolves to a ComponentsApiResponse object
 */
export async function fetchMCPComponents(): Promise<ComponentsApiResponse> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_ENDPOINTS.MCPS.LIST}`, {
      method: "GET",
      headers: headers,
    });

    console.log("Sent the request to fetch MCP components with the url as:", response.url);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    // The API returns an array of MCP components, but we need to transform it to match ComponentsApiResponse
    const responseData = await response.json();
    console.log("Fetched MCP response:", responseData); // For debugging

    // Check if the response is in the expected format (array or data property containing array)
    let mcpData: any[] = [];

    if (Array.isArray(responseData)) {
      // If the response is already an array, use it directly
      console.log("MCP response is an array with", responseData.length, "items");
      mcpData = responseData;
    } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
      // If the response has a data property that is an array, use that
      console.log("MCP response has data property with", responseData.data.length, "items");
      mcpData = responseData.data;
    } else {
      // Handle the case where the response is an object with the data we need
      console.error("MCP response is not in expected format");
      console.log("MCP response type:", typeof responseData);
      console.log("MCP response structure:", Object.keys(responseData || {}));

      // If the response is the example data provided by the user, use it directly
      if (responseData && typeof responseData === "object" && responseData.data) {
        console.log("Using data property from response");
        mcpData = Array.isArray(responseData.data) ? responseData.data : [responseData.data];
      } else if (responseData && typeof responseData === "object") {
        // If it's an object with MCP properties, wrap it in an array
        console.log("Wrapping response object in array");
        mcpData = [responseData];
      } else {
        // Return empty object if we can't process the response
        console.error("Cannot process MCP response, returning empty MCP Marketplace category");
        return { "MCP Marketplace": {} };
      }
    }

    console.log("Processed MCP data:", mcpData);

    // Transform the MCP array into the ComponentsApiResponse format
    // Initialize the components object
    const mcpComponents: ComponentsApiResponse = {};

    // Process each MCP component
    mcpData.forEach((mcp: any) => {
      console.log(`Processing MCP: ${mcp.name}, component_category: "${mcp.component_category}"`);

      // Determine the category based on component_category field
      let categoryKey = "MCP Marketplace"; // Default category
      if (mcp.component_category && mcp.component_category.trim()) {
        // Use the component_category as the category key
        categoryKey = mcp.component_category.trim();
      }

      // For each tool in the MCP, create a component
      if (
        mcp.mcp_tools_config &&
        mcp.mcp_tools_config.tools &&
        mcp.mcp_tools_config.tools.length > 0
      ) {
        mcp.mcp_tools_config.tools.forEach((tool: any) => {
          // Create a unique component name
          const componentName = `MCP_${mcp.name}_${tool.name}`.replace(/\s+/g, "_");

          // Log tool information
          console.log(`  Tool: ${tool.name} -> Category: "${categoryKey}"`);
          console.log(`  Component name: ${componentName}`);
          console.log(`  OAuth details:`, mcp.oauth_details);

          // Initialize the category if it doesn't exist
          if (!mcpComponents[categoryKey]) {
            mcpComponents[categoryKey] = {};
          }

          // Create the component definition
          mcpComponents[categoryKey][componentName] = {
            name: componentName,
            display_name: `${mcp.name} - ${tool.name}`,
            description: tool.description || mcp.description,
            category: categoryKey,
            icon: "Cloud", // Default icon for MCP components
            beta: true,
            inputs: [],
            outputs: [],
            is_valid: true,
            path: `mcp.${componentName.toLowerCase()}`,
            type: "MCP",
            env_keys: mcp.env_keys || [], // Include env_keys from the MCP server
            env_credential_status: mcp.env_credential_status || null, // Include env_credential_status from the MCP server
            logo: mcp.logo || null, // Include logo URL from the MCP server
            oauth_details: mcp.oauth_details || null, // Include OAuth details from the MCP server
            mcp_info: {
              server_id: mcp.id || "",
              server_path: mcp.url || "",
              tool_name: tool.name,
              input_schema: tool.input_schema,
              output_schema: tool.output_schema,
            },
          };

          // Convert input schema to input definitions
          if (tool.input_schema && tool.input_schema.properties) {
            const requiredFields = tool.input_schema.required || [];

            Object.entries(tool.input_schema.properties).forEach(
              ([propName, propSchema]: [string, any]) => {
                const inputDef = {
                  name: propName,
                  display_name: propSchema.title || propName.replace(/_/g, " "),
                  info: propSchema.description || "",
                  input_type: mapJsonSchemaTypeToInputType(propSchema.type, propSchema),
                  input_types: null,
                  required: requiredFields.includes(propName),
                  is_handle: true, // Make all inputs connectable
                  is_list: propSchema.type === "array",
                  real_time_refresh: false,
                  advanced: false,
                  value: propSchema.default || null,
                  options: propSchema.enum ? propSchema.enum : null,
                  visibility_rules: null,
                  visibility_logic: "OR" as "OR" | "AND",
                };

                mcpComponents[categoryKey][componentName].inputs.push(inputDef);
              },
            );
          }

          // Add output definitions based on output schema
          if (tool.output_schema && tool.output_schema.properties) {
            Object.entries(tool.output_schema.properties).forEach(
              ([propName, propSchema]: [string, any]) => {
                const outputDef = {
                  name: propName,
                  display_name: propSchema.title || propName.replace(/_/g, " "),
                  output_type: mapJsonSchemaTypeToOutputType(propSchema.type),
                };

                mcpComponents[categoryKey][componentName].outputs.push(outputDef);
              },
            );
          }

          // If no outputs were defined, add a default output
          if (mcpComponents[categoryKey][componentName].outputs.length === 0) {
            mcpComponents[categoryKey][componentName].outputs.push({
              name: "result",
              display_name: "Result",
              output_type: "any",
            });
          }
        });
      } else {
        // If the MCP server doesn't have any tools, create a component for the server itself
        console.log(`MCP server ${mcp.name} has no tools, creating server component`);

        // Create a unique component name for the MCP server itself
        const componentName = `MCP_${mcp.name}_Server`.replace(/\s+/g, "_");

        // Initialize the category if it doesn't exist
        if (!mcpComponents[categoryKey]) {
          mcpComponents[categoryKey] = {};
        }

        // Create the component definition for the MCP server
        mcpComponents[categoryKey][componentName] = {
          name: componentName,
          display_name: mcp.name, // Just use the MCP name without " - ToolName"
          description: mcp.description || `MCP Server: ${mcp.name}`,
          category: categoryKey,
          icon: "Cloud", // Default icon for MCP components
          beta: true,
          inputs: [],
          outputs: [
            {
              name: "result",
              display_name: "Result",
              output_type: "any",
            },
          ],
          is_valid: true,
          path: `mcp.${componentName.toLowerCase()}`,
          type: "MCP",
          env_keys: mcp.env_keys || [], // Include env_keys from the MCP server
          env_credential_status: mcp.env_credential_status || null, // Include env_credential_status from the MCP server
          oauth_details: mcp.oauth_details || null, // Include OAuth details from the MCP server
          mcp_info: {
            server_id: mcp.id || "",
            server_path: mcp.url || "",
            tool_name: "server", // Special value to indicate this is the server itself
            input_schema: null,
            output_schema: { type: "object", properties: {} }, // Empty schema but not null
          },
        };

        console.log(
          `  Created MCP Server component: ${componentName} in category: "${categoryKey}"`,
        );
      }
    });

    console.log("Transformed MCP components:", mcpComponents);
    console.log("MCP component categories found:", Object.keys(mcpComponents));

    // Log component count per category
    Object.entries(mcpComponents).forEach(([category, components]) => {
      console.log(`  Category "${category}": ${Object.keys(components).length} components`);
    });

    return mcpComponents;
  } catch (error) {
    console.error("Failed to fetch MCP components:", error);
    // Return empty object with just the MCP Marketplace category
    return { "MCP Marketplace": {} };
  }
}

// Helper function to create a component for an MCP server without tools
function createMCPServerComponent(mcp: any, categoryKey: string): [string, any] {
  // Create a unique component name for the MCP server itself
  const componentName = `MCP_${mcp.name}_Server`.replace(/\s+/g, "_");

  console.log(`  Creating MCP Server component: ${componentName} in category: "${categoryKey}"`);

  // Create the component definition
  const componentDef = {
    name: componentName,
    display_name: mcp.name, // Just use the MCP name without " - ToolName"
    description: mcp.description || `MCP Server: ${mcp.name}`,
    category: categoryKey,
    icon: "Cloud", // Default icon for MCP components
    beta: true,
    inputs: [],
    outputs: [],
    is_valid: true,
    path: `mcp.${componentName.toLowerCase()}`,
    type: "MCP",
    mcp_info: {
      server_id: mcp.id || "",
      server_path: mcp.url || "",
      tool_name: "server", // Special value to indicate this is the server itself
      input_schema: null,
      output_schema: { type: "object", properties: {} }, // Empty schema but not null
    },
  };

  return [componentName, componentDef];
}

/**
 * Helper function to map JSON Schema types to input types
 * Uses the universal dynamic input type mapper
 */
function mapJsonSchemaTypeToInputType(schemaType: string, schema: any): string {
  try {
    // Use the imported DynamicInputTypeMapper class
    const mapping = DynamicInputTypeMapper.getMapping({ type: schemaType, ...schema });
    return mapping.inputType;
  } catch (error) {
    console.error(`[API] Dynamic mapper failed for type ${schemaType}:`, error);

    // Emergency fallback - basic type mapping
    const typeMap: Record<string, string> = {
      string: "string",
      number: "number",
      integer: "number",
      boolean: "boolean",
      object: "object",
      array: "array",
    };

    return typeMap[schemaType] || "string";
  }
}

/**
 * Helper function to map JSON Schema types to output types
 */
function mapJsonSchemaTypeToOutputType(schemaType: string): string {
  switch (schemaType) {
    case "string":
      return "string";
    case "number":
    case "integer":
      return "number";
    case "boolean":
      return "boolean";
    case "object":
      return "object";
    case "array":
      return "list";
    default:
      return "any";
  }
}

// Describes the data structure sent TO the backend for execution
export interface WorkflowExecutionPayload {
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[]; // Changed from connections to edges to match backend expectations
  workflow_id: string; // Optional workflow ID
}

// Describes the expected data structure received FROM the backend after execution
export interface ExecutionResult {
  success: boolean;
  results?: Record<string, any>; // Optional: Dictionary mapping node_id to its output data
  error?: string; // Optional: Error message if success is false
  message?: string; // Optional: Success message
  correlation_id?: string; // Optional: Correlation ID for tracking execution
  // You could add more fields here later, like execution logs, timing, etc.
}

// Describes the data structure sent TO the backend for saving a workflow
export interface WorkflowSavePayload {
  nodes: Node<WorkflowNodeData>[];
  edges: any[]; // Using any[] for flexibility (previously called connections)
  filename?: string; // Optional filename
  workflow_name?: string; // Optional workflow name
  workflow_id?: string; // Optional workflow ID
}

// Describes the expected data structure received FROM the backend after saving
export interface SaveResult {
  success: boolean;
  filepath?: string; // Optional: Path where the file was saved
  workflow_id?: string; // Optional: ID of the saved workflow
  error?: string; // Optional: Error message if success is false
  message?: string; // Optional: Additional information about the save operation
}

// Describes the data structure sent TO the backend for workflow validation
export interface WorkflowValidationPayload {
  workflow_data: any; // The workflow data to validate
}

// Describes the expected data structure received FROM the backend after validation
export interface ValidationResult {
  is_valid: boolean;
  error?: string; // Optional: Error message if is_valid is false
}

export async function executeWorkflow(payload: WorkflowExecutionPayload): Promise<ExecutionResult> {
  // Get the component state store
  const componentState = useComponentStateStore.getState();

  // Add component state to the nodes
  const nodesWithState = payload.nodes.map((node) => {
    // Create a deep copy of the node
    const nodeCopy = JSON.parse(JSON.stringify(node));

    // Get the component state for this node
    const nodeState = componentState.nodes[node.id];

    // Log the original node data
    console.log(`Original node ${node.id} data:`, {
      config: node.data.config,
      definition: node.data.definition
        ? {
            inputs: node.data.definition.inputs,
          }
        : null,
    });

    // If there's state for this node, add it to the node data
    if (nodeState) {
      // Add the component state to the node data
      nodeCopy.data.component_state = nodeState;

      // If there's a config in the component state, add it to the node config
      if (nodeState.config) {
        // Merge the component state config with the node config
        nodeCopy.data.config = {
          ...nodeCopy.data.config,
          ...nodeState.config,
        };

        // Log the component state config
        console.log(`Component state config for node ${node.id}:`, nodeState.config);
      }

      // Also add any direct values from the component state to the node config
      for (const [key, value] of Object.entries(nodeState)) {
        if (key !== "config" && value !== null && value !== undefined && value !== "") {
          nodeCopy.data.config[key] = value;
          console.log(
            `Added direct value from component state for node ${node.id}: ${key}=${value}`,
          );
        }
      }

      // Log the node with state
      console.log(`Added component state to node ${node.id}:`, nodeState);
      console.log(`Updated node config:`, nodeCopy.data.config);
    } else {
      // If there's no state for this node, check if there are any values in the inputs
      if (nodeCopy.data.definition && nodeCopy.data.definition.inputs) {
        const inputs = nodeCopy.data.definition.inputs;
        for (const input of inputs) {
          if (
            input.value !== null &&
            input.value !== undefined &&
            input.value !== "" &&
            (typeof input.value !== "object" || Object.keys(input.value).length > 0)
          ) {
            // Add the input value to the node config
            nodeCopy.data.config[input.name] = input.value;
          }
        }
      }
    }

    return nodeCopy;
  });

  // Create a new payload with the nodes with state
  const payloadWithState = {
    nodes: nodesWithState,
    edges: payload.edges,
  };

  // Transform the payload for the external orchestration service
  // This will properly format MCP Marketplace components
  const { nodes, edges } = transformWorkflowForOrchestration(
    payloadWithState.nodes,
    payloadWithState.edges,
  );

  // Post-process the transformed nodes to ensure API request method is preserved
  const processedNodes = nodes.map((node) => {
    // Check if this is an API request node
    if (node.data.originalType === "ApiRequestNode" || node.data.type === "ApiRequestNode") {
      console.log(`Processing API request node ${node.id}`);

      // Ensure the method is preserved from the original node
      const originalNode = payloadWithState.nodes.find((n) => n.id === node.id);
      if (originalNode && originalNode.data.config && originalNode.data.config.method) {
        console.log(
          `Preserving method ${originalNode.data.config.method} for API request node ${node.id}`,
        );
        if (node.data.config) {
          node.data.config.method = originalNode.data.config.method;
        }
      }
    }
    return node;
  });

  // Create the transformed payload without mcp_configs
  const transformedPayload = {
    nodes: processedNodes,
    edges,
    workflow_id: payload.workflow_id, // Include the workflow_id from the original payload
  };

  // Log the original payload
  console.log(`Original execution payload:`, payload);

  // Log the payload with state
  console.log(`Payload with state:`, payloadWithState);

  // Log the transformed payload
  console.log(`Transformed payload:`, transformedPayload);

  // Save the payloads to localStorage for debugging
  try {
    localStorage.setItem("original_payload", JSON.stringify(payload));
    localStorage.setItem("payload_with_state", JSON.stringify(payloadWithState));
    localStorage.setItem("transformed_payload", JSON.stringify(transformedPayload));
    console.log("Saved payloads to localStorage for debugging");
  } catch (error) {
    console.error("Error saving payloads to localStorage:", error);
  }

  // Use the backend API for execution, which will forward to the external service
  const endpoint = `${BACKEND_API_URL}/execute`; // Backend API endpoint

  console.log(`Sending execution request to ${endpoint} with payload:`, transformedPayload);

  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(endpoint, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(transformedPayload), // Convert the JavaScript object to a JSON string
    });

    // Check if the request was successful (status code 2xx)
    if (!response.ok) {
      let errorDetail = "Unknown execution error";
      try {
        // Try to parse more detailed error from backend response body
        const errorData = await response.json();
        errorDetail = errorData.detail || JSON.stringify(errorData);
      } catch (parseError) {
        // If parsing fails, use the status text
        errorDetail = response.statusText;
      }
      // Throw an error to be caught by the catch block below
      throw new Error(`Execution failed: ${response.status} ${errorDetail}`);
    }

    // If successful, parse the JSON response from the backend
    const resultData = await response.json();

    // Ensure the result matches the expected structure (basic check)
    if (typeof resultData.success !== "boolean") {
      throw new Error("Invalid response format received from backend.");
    }

    console.log("Received execution result:", resultData);
    return resultData as ExecutionResult; // Type assertion
  } catch (error) {
    console.error("Error executing workflow via API:", error);
    // Return a standardized error format consistent with ExecutionResult
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

export async function saveWorkflowToServer(payload: WorkflowSavePayload): Promise<SaveResult> {
  // Determine if this is a new workflow or an existing one
  const isExistingWorkflow = !!payload.workflow_id;

  // For new workflows, check for duplicate names
  if (!isExistingWorkflow && payload.workflow_name) {
    try {
      // Import the checkWorkflowNameExists function
      const { checkWorkflowNameExists } = await import("@/app/(features)/workflows/api");
      const nameExists = await checkWorkflowNameExists(payload.workflow_name);
      
      if (nameExists) {
        return {
          success: false,
          error: `A workflow with the name "${payload.workflow_name}" already exists. Please choose a different name.`,
        };
      }
    } catch (error) {
      console.error("Error checking workflow name existence:", error);
      // If there's an error checking, we should allow the creation to proceed
      // rather than blocking it due to a network issue
    }
  }

  // For existing workflows, check for duplicate names excluding the current workflow
  if (isExistingWorkflow && payload.workflow_name && payload.workflow_id) {
    try {
      // Import the necessary functions
      const { checkWorkflowNameExistsExcluding, fetchWorkflowById } = await import("@/app/(features)/workflows/api");
      
      // Fetch the current workflow to get its current name
      const currentWorkflow = await fetchWorkflowById(payload.workflow_id);
      const currentName = currentWorkflow.workflow.name;
      
      // Only check for duplicates if the name is actually changing
      if (currentName.toLowerCase() !== payload.workflow_name.toLowerCase()) {
        const nameExists = await checkWorkflowNameExistsExcluding(payload.workflow_name, payload.workflow_id);
        
        if (nameExists) {
          return {
            success: false,
            error: `A workflow with the name "${payload.workflow_name}" already exists. Please choose a different name.`,
          };
        }
      }
    } catch (error) {
      console.error("Error checking workflow name existence for update:", error);
      // If there's an error checking, we should allow the update to proceed
      // rather than blocking it due to a network issue
    }
  }

  // Use the appropriate endpoint based on whether it's a new or existing workflow
  let endpoint;

  if (isExistingWorkflow && payload.workflow_id) {
    // Clean and encode the workflow ID to prevent URL issues
    const cleanId = payload.workflow_id.toString().trim();
    console.log(`Using workflow ID for update: "${cleanId}"`);

    // For existing workflows, use the UPDATE endpoint with the workflow ID in the URL
    endpoint = `${API_ENDPOINTS.WORKFLOWS.UPDATE(cleanId)}`;
  } else {
    // For new workflows, use the CREATE endpoint
    endpoint = `${API_ENDPOINTS.WORKFLOWS.CREATE}`;
  }

  // Use appropriate HTTP method based on whether it's a new or existing workflow
  const method = isExistingWorkflow ? "PATCH" : "POST";

  // Enhanced logging for debugging
  console.log(`isExistingWorkflow: ${isExistingWorkflow}`);
  console.log(`workflow_id: "${payload.workflow_id}"`);
  console.log(`Final endpoint URL: "${endpoint}"`);
  console.log(`HTTP method: ${method}`);
  console.log(
    `Sending save workflow request to ${endpoint} with method ${method} and payload:`,
    payload,
  );

  // Find the StartNode to update its configuration
  const startNode = payload.nodes.find(
    (node) =>
      node.data.originalType === "StartNode" ||
      node.id === "start-node" ||
      node.data.label === "Start",
  );

  // Check if we have a StartNode
  if (startNode) {
    console.log(`[WORKFLOW SAVE] Found StartNode with ID: ${startNode.id}`);

    // Ensure the StartNode has a config object
    if (!startNode.data.config) {
      console.log(`[WORKFLOW SAVE] StartNode has no config object, creating one`);
      startNode.data.config = {};
    }

    // Ensure the config has a collected_parameters object
    if (!startNode.data.config.collected_parameters) {
      console.log(`[WORKFLOW SAVE] StartNode has no collected_parameters object, creating one`);
      startNode.data.config.collected_parameters = {};
    }

    // Create a fresh collected_parameters object
    if (startNode.data.config) {
      startNode.data.config.collected_parameters = {};
    }

    console.log(
      `[WORKFLOW SAVE] Using enhanced graph traversal logic to find all connected nodes (including tool connections)`,
    );

    // Use the enhanced getConnectedNodesWithToolConnections function that includes tool connections
    // This performs a breadth-first search to find all nodes reachable from the StartNode, including tool-connected nodes
    const connectedNodes = getConnectedNodesWithToolConnections(
      payload.nodes,
      payload.edges,
      startNode.id,
    );
    console.log(
      `[WORKFLOW SAVE] Found ${connectedNodes.size} nodes connected to StartNode (including tool connections): ${Array.from(connectedNodes).join(", ")}`,
    );

    // Use the same collectAllFields function that the Run button uses
    // This collects all fields from connected nodes, including required and optional fields
    const allFields = collectAllFields(payload.nodes, connectedNodes, payload.edges);
    console.log(`[WORKFLOW SAVE] Collected ${allFields.length} fields from connected nodes`);

    // Log detailed field information
    if (allFields.length > 0) {
      console.log(`[WORKFLOW SAVE] Field details:`);
      allFields.forEach((field: MissingField, index: number) => {
        console.log(
          `[WORKFLOW SAVE]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}, Required: ${field.required ? "YES" : "NO"}`,
        );
      });
    }

    // Add only fields that should be included to the StartNode's collected_parameters
    if (allFields.length > 0) {
      console.log(
        `[WORKFLOW SAVE] Adding fields to StartNode collected_parameters (excluding fields with incoming connections)`,
      );

      allFields.forEach((field: MissingField) => {
        const fieldId = `${field.nodeId}_${field.name}`;

        // CRITICAL FIX: Skip fields from nodes connected as tools to agentic components
        // Tool parameters should be handled internally by the agent runtime, not prompted to users
        if (isNodeConnectedAsTool(field.nodeId, payload.edges)) {
          console.log(
            `[WORKFLOW SAVE] DEFENSIVE FILTER: Excluding field from tool-connected node: ${field.nodeName} (${field.nodeId}) - field: ${field.name}`,
          );
          return;
        }

        // Check if this field has an incoming connection from another node
        const hasIncomingConnection = field.is_handle === true && field.is_connected === true;

        // Use the shared utility function to determine if the field should be included
        const isRequired = field.required !== false;
        const isDirectlyConnected = field.directly_connected_to_start === true;
        const hasConfiguredValue = field.currentValue !== undefined;

        // Log field status for debugging
        console.log(`[WORKFLOW SAVE] Field ${fieldId} status:
          - required: ${isRequired ? "YES" : "NO"}
          - directly connected to Start: ${isDirectlyConnected ? "YES" : "NO"}
          - has configured value: ${hasConfiguredValue ? "YES" : "NO"}
          - has incoming connection: ${hasIncomingConnection ? "YES" : "NO"}`);

        // Only include fields that should be included based on our filtering logic
        if (
          shouldIncludeField(
            isRequired,
            isDirectlyConnected,
            hasConfiguredValue,
            hasIncomingConnection,
          )
        ) {
          if (startNode.data.config && startNode.data.config.collected_parameters) {
            startNode.data.config.collected_parameters[fieldId] = {
              node_id: field.nodeId,
              node_name: field.nodeName,
              input_name: field.name,
              value: field.currentValue,
              connected_to_start: true,
              required: field.required,
              input_type: field.inputType,
              options: field.options,
            };

            console.log(`[WORKFLOW SAVE] Added field ${fieldId} to StartNode collected_parameters`);
          }
        } else {
          console.log(`[WORKFLOW SAVE] Skipping field ${fieldId} - excluded by filtering logic`);
        }
      });

      console.log(`[WORKFLOW SAVE] Finished adding fields to StartNode collected_parameters`);
    } else {
      console.log(`[WORKFLOW SAVE] No fields found to add to StartNode collected_parameters`);
    }

    // Log the final StartNode configuration
    console.log(`[WORKFLOW SAVE] StartNode complete data:`, startNode.data);
    console.log(`[WORKFLOW SAVE] StartNode config:`, startNode.data.config);
    console.log(
      `[WORKFLOW SAVE] StartNode collected_parameters:`,
      startNode.data.config.collected_parameters,
    );
  } else {
    console.log(`[WORKFLOW SAVE] No StartNode found in the workflow`);
  }

  // Extract the start node data
  const start_node_data = extractStartNodeData(payload.nodes, payload.edges);
  console.log(`[WORKFLOW SAVE] Extracted start_node_data:`, start_node_data);

  // Initialize variables for filtered nodes and edges
  let filteredNodes = payload.nodes;
  let filteredEdges = payload.edges;
  let unconnectedNodes: Node<WorkflowNodeData>[] = [];
  let unconnectedEdges: Edge[] = [];

  // Filter out nodes that are not connected to the StartNode but preserve them separately
  if (startNode) {
    console.log(
      `[WORKFLOW SAVE] Using enhanced graph traversal logic to find all connected nodes (including tool connections)`,
    );

    // Use the enhanced getConnectedNodesWithToolConnections function to find all nodes reachable from the StartNode
    const connectedNodes = getConnectedNodesWithToolConnections(
      payload.nodes,
      payload.edges,
      startNode.id,
    );
    console.log(
      `[WORKFLOW SAVE] Found ${connectedNodes.size} nodes connected to StartNode (including tool connections): ${Array.from(connectedNodes).join(", ")}`,
    );

    // Separate connected and unconnected nodes
    filteredNodes = payload.nodes.filter((node) => connectedNodes.has(node.id));
    unconnectedNodes = payload.nodes.filter((node) => !connectedNodes.has(node.id));

    // Filter edges to include only those connecting filtered nodes
    filteredEdges = payload.edges.filter(
      (edge) => connectedNodes.has(edge.source) && connectedNodes.has(edge.target),
    );

    // Identify unconnected edges (edges between unconnected nodes)
    unconnectedEdges = payload.edges.filter(
      (edge) => !connectedNodes.has(edge.source) || !connectedNodes.has(edge.target),
    );

    // Check if there are any disconnected nodes
    const disconnectedNodesCount = unconnectedNodes.length;
    if (disconnectedNodesCount > 0) {
      console.log(
        `[WORKFLOW SAVE] Found ${disconnectedNodesCount} disconnected nodes that will be preserved separately for canvas state`,
      );
      console.log(
        `[WORKFLOW SAVE] Unconnected nodes: ${unconnectedNodes.map(n => `${n.data.label} (${n.id})`).join(", ")}`,
      );

      console.log(
        `[WORKFLOW SAVE] Connected workflow contains ${filteredNodes.length} nodes and ${filteredEdges.length} edges`,
      );
      console.log(
        `[WORKFLOW SAVE] Found ${unconnectedEdges.length} unconnected edges that will be preserved separately`,
      );

      // Import toast dynamically to avoid server-side rendering issues
      try {
        // Use dynamic import for toast
        const { toast } = await import("sonner");

        // Show an updated warning toast notification
        toast.warning(
          `${disconnectedNodesCount} unconnected ${disconnectedNodesCount === 1 ? "node has" : "nodes have"} been saved separately.`,
          {
            description: "Unconnected nodes are preserved for canvas state but won't be executed.",
            duration: 3000,
          },
        );
      } catch (error) {
        console.error("Failed to show toast notification:", error);
      }
    } else {
      console.log(`[WORKFLOW SAVE] All nodes are connected to StartNode`);
    }
  } else {
    // If no StartNode is found, treat all nodes as unconnected to preserve them
    console.warn(`[WORKFLOW SAVE] No StartNode found, preserving all nodes as unconnected`);
    unconnectedNodes = payload.nodes;
    filteredNodes = [];
    filteredEdges = [];
    unconnectedEdges = payload.edges; // All edges become unconnected
  }

  // TYPE CONVERSION: Convert node config values to proper types before saving
  const nodesWithTypedConfigs = filteredNodes.map((node) => {
    // Process node configuration to ensure proper types
    if (node.data.config && node.data.definition) {
      const processedConfig = processNodeConfigTypes(node, node.data.definition);
      return {
        ...node,
        data: {
          ...node.data,
          config: processedConfig,
        },
      };
    }
    return node;
  });

  // OPTIMIZED TOOL CONNECTION WRAPPER: Single source of truth with minimal redundancy
  // This wrapper eliminates duplicate MCP metadata and keeps only essential tool connection data
  const processedNodes = nodesWithTypedConfigs.map((node) => {
    // Only process AgenticAI nodes that can have tool connections
    if (node.data.originalType === "AgenticAI") {
      console.log(`[WORKFLOW SAVE] Processing AgenticAI node ${node.id} for tool connections`);

      // Find all edges connecting to this AgenticAI node's tool handles
      const toolEdges = filteredEdges.filter(
        (edge) =>
          edge.target === node.id &&
          edge.targetHandle &&
          (edge.targetHandle.includes("tool") || edge.targetHandle === "tools"),
      );

      if (toolEdges.length > 0) {
        console.log(
          `[WORKFLOW SAVE] Found ${toolEdges.length} tool edges for AgenticAI node ${node.id}`,
        );

        // Create optimized tool connections array with minimal redundancy
        const optimizedTools: any[] = [];

        toolEdges.forEach((edge) => {
          const sourceNode = nodesWithTypedConfigs.find((n) => n.id === edge.source);
          if (sourceNode) {
            // Create optimized tool connection with only essential data
            const toolConnection = {
              // Basic identification
              node_id: sourceNode.id,
              node_type: sourceNode.data.originalType || sourceNode.data.type || "Unknown",
              node_label:
                sourceNode.data.label || sourceNode.data.definition?.display_name || "Unknown Tool",

              // Component information (for compatibility)
              component_id: sourceNode.id,
              component_type: sourceNode.data.originalType || sourceNode.data.type || "Unknown",
              component_name:
                sourceNode.data.label || sourceNode.data.definition?.display_name || "Unknown Tool",

              // Essential component data (inputs/outputs only, no redundant schemas)
              component_definition: {
                name: sourceNode.data.definition?.name,
                display_name: sourceNode.data.definition?.display_name,
                description: sourceNode.data.definition?.description,
                inputs: sourceNode.data.definition?.inputs || [],
                outputs: sourceNode.data.definition?.outputs || [],
                type: sourceNode.data.definition?.type,
                path: sourceNode.data.definition?.path,
              },

              // Component config (user-configured values only)
              component_config: sourceNode.data.config || {},

              // MCP metadata (single source of truth for MCP info)
              ...(sourceNode.data.definition?.mcp_info && {
                mcp_server_id: sourceNode.data.definition.mcp_info.server_id,
                mcp_tool_name: sourceNode.data.definition.mcp_info.tool_name,
                // Only include essential schema info, not the full redundant schemas
                mcp_input_schema_required:
                  sourceNode.data.definition.mcp_info.input_schema?.required || [],
                mcp_output_properties: Object.keys(
                  sourceNode.data.definition.mcp_info.output_schema?.properties || {},
                ),
              }),
            };

            optimizedTools.push(toolConnection);
            console.log(
              `[WORKFLOW SAVE] Added optimized tool connection: ${sourceNode.data.label} -> ${node.data.label}`,
            );
          }
        });

        // Create the updated node with optimized tool connections
        const updatedNode = {
          ...node,
          data: {
            ...node.data,
            config: {
              ...node.data.config,
              // Single source of truth for tool connections (optimized)
              tools: optimizedTools,
              // Clear any legacy tool connection formats to prevent duplication
              tool_connections: undefined,
              mcp_configs: undefined,
            },
          },
        };

        console.log(
          `[WORKFLOW SAVE] Generated optimized tool connections for AgenticAI node ${node.id}:`,
          optimizedTools,
        );
        return updatedNode;
      } else {
        // No tool connections found, ensure clean config
        const cleanedNode = {
          ...node,
          data: {
            ...node.data,
            config: {
              ...node.data.config,
              // Clear any legacy tool connection formats
              tools: [],
              tool_connections: undefined,
              mcp_configs: undefined,
            },
          },
        };
        console.log(
          `[WORKFLOW SAVE] No tool connections found for AgenticAI node ${node.id}, cleaned config`,
        );
        return cleanedNode;
      }
    }

    // For non-AgenticAI nodes, return as-is
    return node;
  });

  // Create the request payload with processed nodes, edges, and unconnected nodes
  const requestPayload = {
    name: payload.workflow_name || payload.filename, // Use workflow_name if available
    description: payload.filename,
    workflow_data: {
      nodes: processedNodes,
      edges: filteredEdges,
      // FEATURE: Preserve unconnected nodes for canvas state restoration
      ...(unconnectedNodes.length > 0 && { unconnected_nodes: unconnectedNodes }),
      // FEATURE: Preserve unconnected edges for canvas state restoration
      ...(unconnectedEdges.length > 0 && { unconnected_edges: unconnectedEdges }),
    },
    start_node_data: start_node_data,
  };

  console.log(
    `[WORKFLOW SAVE] Request payload summary: ${processedNodes.length} connected nodes, ${filteredEdges.length} edges, ${unconnectedNodes.length} unconnected nodes, ${unconnectedEdges.length} unconnected edges`,
  );

  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    // Log the final request payload with StartNode config
    console.log(`[WORKFLOW SAVE] Final request payload:`, requestPayload);
    console.log(
      `[WORKFLOW SAVE] Final request payload JSON:`,
      JSON.stringify(requestPayload, null, 2),
    );

    const response = await fetch(endpoint, {
      method: method,
      headers: headers,
      body: JSON.stringify(requestPayload),
    });

    // Check if the request was successful (status code 2xx)
    if (!response.ok) {
      let errorDetail = "Unknown save error";
      try {
        // Try to parse more detailed error from backend response body
        const errorData = await response.json();
        errorDetail = errorData.detail || JSON.stringify(errorData);
      } catch (parseError) {
        // If parsing fails, use the status text
        errorDetail = response.statusText;
      }

      // Handle specific error codes
      if (response.status === 400) {
        errorDetail = "Invalid workflow data. Please check your workflow configuration.";
      } else if (response.status === 403) {
        errorDetail = "You don't have permission to save this workflow.";
      } else if (response.status === 404) {
        errorDetail = "Workflow not found. It may have been deleted.";
      } else if (response.status === 500) {
        errorDetail = "Server error occurred while saving the workflow. Please try again later.";
      }

      // Throw an error to be caught by the catch block below
      throw new Error(`Save failed: ${response.status} ${errorDetail}`);
    }

    // If successful, parse the JSON response from the backend
    const resultData = await response.json();

    // Handle different response formats from the new endpoint
    let saveResult: SaveResult;

    // The new endpoint returns a workflow object directly
    if (resultData.workflow) {
      // New endpoint format
      saveResult = {
        success: true,
        workflow_id: resultData.workflow.id,
        message: "Workflow saved successfully",
        error: undefined,
      };
    }
    // Fallback for other response formats
    else if (typeof resultData.success === "boolean") {
      // Old format with success property
      saveResult = {
        success: resultData.success,
        workflow_id: resultData.workflow_id,
        message: resultData.message || "Workflow saved successfully",
        error: resultData.error,
      };
    } else {
      throw new Error("Invalid response format received from backend.");
    }

    console.log("Received save result:", saveResult);
    return saveResult;
  } catch (error) {
    console.error("Error saving workflow via API:", error);
    // Return a standardized error format consistent with SaveResult
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Processes a JSON object into a structured representation with proper nested properties
 *
 * @param jsonData The JSON object to process
 * @param parentKey The parent key for nested properties
 * @param parentTransitionId The transition ID to use for all properties
 * @returns Processed object with proper structure
 */
export function processJsonObject(
  jsonData: any,
  parentKey: string = "",
  parentTransitionId: string = "",
): {
  field: string;
  type: string;
  transition_id: string;
  properties?: Array<{
    field: string;
    type: string;
    transition_id: string;
    properties?: any[];
  }>;
  enum?: string[];
} {
  // Handle null or undefined
  if (jsonData === null || jsonData === undefined) {
    return {
      field: parentKey,
      type: "string",
      transition_id: parentTransitionId,
    };
  }

  // If it's an object (but not an array), process it as an object with properties
  if (typeof jsonData === "object" && !Array.isArray(jsonData)) {
    const properties: Array<{
      field: string;
      type: string;
      transition_id: string;
      properties?: any[];
    }> = [];

    // Process each property of the object
    Object.entries(jsonData).forEach(([key, value]) => {
      if (value === null || value === undefined) {
        // Handle null values as simple string properties
        properties.push({
          field: key,
          type: "string",
          transition_id: parentTransitionId,
        });
      } else if (typeof value === "object" && !Array.isArray(value)) {
        // Recursively process nested objects
        const nestedObject = processJsonObject(value, key, parentTransitionId);
        properties.push(nestedObject);
      } else if (Array.isArray(value)) {
        // Handle arrays
        if (value.length > 0 && typeof value[0] === "object") {
          // If array contains objects, process the first one as an example
          const nestedObject = processJsonObject(value[0], key, parentTransitionId);
          properties.push({
            field: key,
            type: "array",
            transition_id: parentTransitionId,
            properties: [nestedObject],
          });
        } else {
          // Simple array
          properties.push({
            field: key,
            type: "array",
            transition_id: parentTransitionId,
          });
        }
      } else {
        // Handle primitive types
        let type: string = typeof value;
        if (type === "number") {
          // Check if it's an integer or float
          type = Number.isInteger(value) ? "integer" : "number";
        }

        properties.push({
          field: key,
          type: type,
          transition_id: parentTransitionId,
        });
      }
    });

    // Return the object with its properties
    return {
      field: parentKey,
      type: "object",
      transition_id: parentTransitionId,
      properties: properties,
    };
  } else if (Array.isArray(jsonData)) {
    // It's an array
    if (jsonData.length > 0 && typeof jsonData[0] === "object") {
      // If array contains objects, process the first one as an example
      const nestedObject = processJsonObject(jsonData[0], `${parentKey}Item`, parentTransitionId);
      return {
        field: parentKey,
        type: "array",
        transition_id: parentTransitionId,
        properties: [nestedObject],
      };
    } else {
      // Simple array
      return {
        field: parentKey,
        type: "array",
        transition_id: parentTransitionId,
      };
    }
  } else {
    // It's a primitive type
    let type: string = typeof jsonData;
    if (type === "number") {
      // Check if it's an integer or float
      type = Number.isInteger(jsonData) ? "integer" : "number";
    }

    return {
      field: parentKey,
      type: type,
      transition_id: parentTransitionId,
    };
  }
}

// The flattenJson function has been replaced by processJsonObject

/**
 * Checks if a handle input is connected to another node
 *
 * @param nodeId The ID of the node containing the handle
 * @param inputName The name of the handle input
 * @param edges The array of edges in the workflow
 * @returns True if the handle is connected to another node
 */
function isConnected(nodeId: string, inputName: string, edges: Edge[]): boolean {
  // For target handles, the connection would be to a target handle with the input name
  const isConnected = edges.some(
    (edge) => edge.target === nodeId && edge.targetHandle === inputName,
  );

  // Log all edges for debugging
  console.log(`[EXTRACT_START_NODE] All edges (${edges.length}):`);
  edges.forEach((edge, index) => {
    console.log(
      `[EXTRACT_START_NODE] Edge ${index + 1}: source=${edge.source}, sourceHandle=${edge.sourceHandle}, target=${edge.target}, targetHandle=${edge.targetHandle}`,
    );
  });

  // Log the specific check we're making
  console.log(
    `[EXTRACT_START_NODE] Checking if handle ${nodeId}.${inputName} is connected: ${isConnected ? "YES" : "NO"}`,
  );

  // If connected, log which edge(s) connect to this handle
  if (isConnected) {
    const connectingEdges = edges.filter(
      (edge) => edge.target === nodeId && edge.targetHandle === inputName,
    );
    connectingEdges.forEach((edge, index) => {
      console.log(
        `[EXTRACT_START_NODE] Found connecting edge ${index + 1}: source=${edge.source}, sourceHandle=${edge.sourceHandle}, target=${edge.target}, targetHandle=${edge.targetHandle}`,
      );
    });
  }

  return isConnected;
}

/**
 * Extracts required input fields from the start node
 * @param nodes The workflow nodes
 * @param edges The array of edges in the workflow
 * @returns Array of start node data objects
 */
function extractStartNodeData(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[] = [],
): Array<{
  field: string;
  type: string;
  enum?: string[];
  transition_id: string;
  properties?: Array<{
    field: string;
    type: string;
    transition_id: string;
    properties?: any[];
  }>;
}> {
  console.log(`[EXTRACT_START_NODE] Starting extraction from ${nodes.length} nodes`);

  // Find the start node
  const startNode = nodes.find(
    (node) =>
      node.data.originalType === "StartNode" ||
      node.id === "start-node" ||
      node.data.label === "Start",
  );

  if (!startNode) {
    console.log(`[EXTRACT_START_NODE] No StartNode found in the workflow`);
    return [];
  }

  console.log(`[EXTRACT_START_NODE] Found StartNode with ID: ${startNode.id}`);

  if (!startNode.data.config) {
    console.log(`[EXTRACT_START_NODE] StartNode has no config object`);
    return [];
  }

  if (!startNode.data.config.collected_parameters) {
    console.log(`[EXTRACT_START_NODE] StartNode has no collected_parameters object`);
    return [];
  }

  console.log(`[EXTRACT_START_NODE] StartNode config:`, startNode.data.config);
  console.log(
    `[EXTRACT_START_NODE] StartNode collected_parameters:`,
    startNode.data.config.collected_parameters,
  );

  const result: Array<{
    field: string;
    type: string;
    enum?: string[];
    transition_id: string;
    properties?: Array<{
      field: string;
      type: string;
      transition_id: string;
      properties?: any[];
    }>;
  }> = [];

  // Extract parameters from the start node
  const params = startNode.data.config.collected_parameters;

  Object.entries(params).forEach(([fieldId, paramData]: [string, any]) => {
    console.log(`[EXTRACT_START_NODE] Processing field: ${fieldId}`, paramData);

    // Extract node_id and field_name from the fieldId
    const nodeId = paramData.node_id;
    const fieldName = paramData.input_name;

    console.log(`[EXTRACT_START_NODE] Extracted nodeId: ${nodeId}, fieldName: ${fieldName}`);

    // Check if this field is required (consider it required unless explicitly marked as optional)
    const isRequired = paramData.required !== false;

    // Check if this field is directly connected to the Start node
    const isDirectlyConnected = paramData.connected_to_start === true;

    // Find the node in the current workflow
    const node = nodes.find((node) => node.id === nodeId);

    // Check if the node has a config with a value for this field
    const hasConfiguredValue = node?.data?.config && node.data.config[fieldName] !== undefined;

    // Check if this field has an incoming connection from another node
    const hasIncomingConnection = isConnected(nodeId, fieldName, edges);

    // Use the shared utility function to log field status
    logFieldStatus(
      "ExtractStartNodeData",
      fieldId,
      node?.data?.label || "Unknown Node",
      fieldName,
      isRequired,
      paramData.required,
      isDirectlyConnected || false,
      hasConfiguredValue || false,
      hasConfiguredValue && node?.data?.config ? node.data.config[fieldName] : undefined,
      hasIncomingConnection,
    );

    // Use the shared utility function to determine if the field should be included
    if (
      shouldIncludeField(
        isRequired,
        isDirectlyConnected || false,
        hasConfiguredValue || false,
        hasIncomingConnection,
      )
    ) {
      // Determine the data type
      let dataType = paramData.input_type || "string";
      let enumValues: string[] | undefined = undefined;

      if (paramData.input_type) {
        dataType = paramData.input_type;
        console.log(`[EXTRACT_START_NODE] Found dataType in paramData.input_type: ${dataType}`);
      } else {
        console.log(`[EXTRACT_START_NODE] No input_type found, using default: ${dataType}`);
      }

      if ((dataType === "json" || dataType === "object") && paramData.value) {
        try {
          // If value is already an object, use it directly
          const jsonData =
            typeof paramData.value === "object" ? paramData.value : JSON.parse(paramData.value);

          console.log(`[EXTRACT_START_NODE] Processing ${dataType} type data`);

          // Process the JSON object with proper structure
          const processedObject = processJsonObject(jsonData, fieldName, nodeId);
          console.log(`[EXTRACT_START_NODE] Processed object structure:`, processedObject);

          // Add the processed object to the result
          result.push(processedObject);

          // Skip adding the original JSON field since we've added the processed version
          return;
        } catch (error) {
          console.log(
            `[EXTRACT_START_NODE] Error processing ${dataType}: ${error}. Using as regular string.`,
          );
          // Fall back to treating it as a string
          dataType = "string";
        }
      }

      // Check for enum values if dataType is enum
      if (dataType === "enum") {
        if (Array.isArray(paramData.options)) {
          enumValues = paramData.options;
          console.log(`[EXTRACT_START_NODE] Found enum values in paramData.options`);
        }
      }

      console.log(`[EXTRACT_START_NODE] Determined dataType: ${dataType}`);

      // For regular fields, create a simple field object
      result.push({
        field: fieldName,
        type: dataType,
        ...(enumValues && { enum: enumValues }),
        transition_id: `transition-${nodeId}`,
      });
    } else {
      console.log(
        `[EXTRACT_START_NODE] Skipping field ${fieldId} (not required, has configured value, or not directly connected)`,
      );
    }
  });

  console.log(`[EXTRACT_START_NODE] Final extracted result:`, result);
  return result;
}

export async function validateWorkflow(
  payload: WorkflowValidationPayload,
): Promise<ValidationResult> {
  const endpoint = `${BACKEND_API_URL}/validate_workflow`; // Endpoint for validation

  console.log(`Sending validation request to ${endpoint} with payload:`, payload);

  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(endpoint, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(payload),
    });

    // Check if the request was successful (status code 2xx)
    if (!response.ok) {
      let errorDetail = "Unknown validation error";
      try {
        // Try to parse more detailed error from backend response body
        const errorData = await response.json();
        errorDetail = errorData.detail || JSON.stringify(errorData);
      } catch (parseError) {
        // If parsing fails, use the status text
        errorDetail = response.statusText;
      }
      // Throw an error to be caught by the catch block below
      throw new Error(`Validation failed: ${response.status} ${errorDetail}`);
    }

    // If successful, parse the JSON response from the backend
    const resultData = await response.json();

    // Ensure the result matches the expected structure (basic check)
    if (typeof resultData.is_valid !== "boolean") {
      throw new Error("Invalid response format received from backend.");
    }

    console.log("Received validation result:", resultData);
    return resultData as ValidationResult; // Type assertion
  } catch (error) {
    console.error("Error validating workflow via API:", error);
    // Return a standardized error format consistent with ValidationResult
    return {
      is_valid: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Credential Management Types and Functions
// Import types from centralized location
export type {
  Credential,
  CredentialCreate,
  CredentialUpdate,
  CredentialListResponse,
  CredentialDeleteResponse,
} from "../types/credentials";

// Import credential service
import { credentialService } from "../services/credentialService";
import type {
  Credential,
  CredentialCreate,
  CredentialListResponse,
  CredentialDeleteResponse,
} from "../types/credentials";

// Fetch all credentials
export async function fetchCredentials(): Promise<CredentialListResponse> {
  console.log("Using real credential API");
  return await credentialService.fetchCredentials();
}

// Create a new credential
export async function createCredential(credential: CredentialCreate): Promise<Credential> {
  console.log("Using real credential API");
  return await credentialService.createCredential(credential);
}

// Delete a credential
export async function deleteCredential(credentialId: string): Promise<CredentialDeleteResponse> {
  console.log("Using real credential API");
  await credentialService.deleteCredential(credentialId);
  return { success: true };
}

// Get credential value for workflow execution
export async function getCredentialValueForExecution(credentialId: string): Promise<string> {
  console.log("Using real credential API for execution");
  return await credentialService.getCredentialValueForExecution(credentialId);
}

// Update MCP tool output schema
export async function updateMCPToolOutputSchema(payload: {
  mcp_id: string;
  tool_name: string;
  output_schema_json: object;
}): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_ENDPOINTS.MCPS.UPDATE_OUTPUT_SCHEMA}`, {
      method: "PUT",
      headers: headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      let errorDetail = "Unknown error";
      try {
        const errorData = await response.json();
        errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);
      } catch (parseError) {
        errorDetail = response.statusText;
      }
      throw new Error(`Failed to update output schema: ${response.status} ${errorDetail}`);
    }

    const resultData = await response.json();
    return {
      success: true,
      message: resultData.message || "Output schema updated successfully",
    };
  } catch (error) {
    console.error("Error updating MCP tool output schema:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Update MCP environment keys
export async function updateMCPEnvironmentKeys(payload: {
  mcp_id: string;
  env_key_values: Array<{ key: string; value: string }>;
}): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_ENDPOINTS.MCPS.UPDATE_ENV_KEYS}?mcp_id=${payload.mcp_id}`, {
      method: "PUT",
      headers: headers,
      body: JSON.stringify({ env_key_values: payload.env_key_values }),
    });

    if (!response.ok) {
      let errorDetail = "Unknown error";
      try {
        const errorData = await response.json();
        errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);
      } catch (parseError) {
        errorDetail = response.statusText;
      }
      throw new Error(`Failed to update environment keys: ${response.status} ${errorDetail}`);
    }

    const resultData = await response.json();
    return {
      success: true,
      message: resultData.message || "Environment keys updated successfully",
    };
  } catch (error) {
    console.error("Error updating MCP environment keys:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// OAuth API functions
export async function fetchOAuthProviders(): Promise<{ success: boolean; providers?: any[]; error?: string }> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}/oauth/providers`, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch OAuth providers: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      success: true,
      providers: data.providers || [],
    };
  } catch (error) {
    console.error("Error fetching OAuth providers:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

export async function initiateOAuthFlow(params: {
  provider: string;
  tool_name: string;
  user_id: string;
  scopes?: string;
  redirect_url?: string;
}): Promise<{ success: boolean; auth_url?: string; error?: string }> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    // Build query parameters
    const queryParams = new URLSearchParams({
      provider: params.provider,
      tool_name: params.tool_name,
      user_id: params.user_id,
    });

    if (params.scopes) {
      queryParams.append("scopes", params.scopes);
    }

    if (params.redirect_url) {
      queryParams.append("redirect_url", params.redirect_url);
    }

    const url = `${API_BASE_URL}/oauth/authorize?${queryParams.toString()}`;

    // For OAuth flow, we need to redirect the browser
    if (typeof window !== "undefined") {
      window.location.href = url;
      return { success: true, auth_url: url };
    } else {
      return { success: true, auth_url: url };
    }
  } catch (error) {
    console.error("Error initiating OAuth flow:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

export async function checkOAuthCredentials(params: {
  tool_name: string;
  provider?: string;
}): Promise<{ success: boolean; is_connected?: boolean; expires_in?: number; error?: string }> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    // Build query parameters
    const queryParams = new URLSearchParams({
      tool_name: params.tool_name,
    });

    if (params.provider) {
      queryParams.append("provider", params.provider);
    }

    const response = await fetch(`${API_BASE_URL}/oauth/credentials?${queryParams.toString()}`, {
      method: "GET",
      headers: headers,
    });

    if (response.status === 404) {
      // Credentials not found means not connected
      return { success: true, is_connected: false };
    }

    if (!response.ok) {
      throw new Error(`Failed to check OAuth credentials: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      success: true,
      is_connected: true,
      expires_in: data.expires_in,
    };
  } catch (error) {
    console.error("Error checking OAuth credentials:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Workflow Execution with User Inputs
export interface WorkflowExecuteWithUserInputsPayload {
  workflow_id: string;
  approval: boolean; // Changed from string to boolean for proper type safety
  payload: {
    user_dependent_fields: string[];
    user_payload_template: Record<string, any>;
  };
}

export interface WorkflowExecuteResponse {
  correlationId?: string;
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Executes a workflow with user-provided inputs
 *
 * @param payload The execution payload containing workflow_id, approval status, and user inputs
 * @returns A promise that resolves to a WorkflowExecuteResponse
 */
export async function executeWorkflowWithUserInputs(
  payload: WorkflowExecuteWithUserInputsPayload,
): Promise<WorkflowExecuteResponse> {
  try {
    console.log(`Executing workflow with user inputs:`, payload);

    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    // Send the request to the execution endpoint
    const response = await fetch(API_ENDPOINTS.WORKFLOW_EXECUTION.EXECUTE, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      let errorDetail = "Unknown execution error";
      try {
        // Try to parse more detailed error from response body
        const errorData = await response.json();
        errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);
      } catch (parseError) {
        // If parsing fails, use the status text
        errorDetail = response.statusText;
      }
      throw new Error(`Execution failed: ${response.status} ${errorDetail}`);
    }

    const resultData = await response.json();
    console.log("Received execution result:", resultData);

    return {
      success: true,
      correlationId: resultData.correlationId,
      message: resultData.message || "Workflow execution started successfully",
    };
  } catch (error) {
    console.error("Error executing workflow with user inputs:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Workflow Approval Types and Functions
export interface ApprovalResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Sends an approval decision for a workflow that is waiting for approval.
 *
 * @param correlationId The correlation ID of the workflow execution
 * @param decision The approval decision ('approve' or 'reject')
 * @returns A promise that resolves to an ApprovalResponse
 */
export async function sendApprovalDecision(
  correlationId: string,
  decision: "approve" | "reject",
): Promise<ApprovalResponse> {
  try {
    console.log(`Sending ${decision} decision for correlation ID: ${correlationId}`);

    // Direct call to the external API, not going through our backend
    const response = await fetch(API_ENDPOINTS.WORKFLOW_EXECUTION.APPROVE, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getClientAccessToken()}`,
      },
      body: JSON.stringify({
        correlationId,
        decision,
      }),
    });

    if (!response.ok) {
      let errorDetail = "Unknown approval error";
      try {
        // Try to parse more detailed error from response body
        const errorData = await response.json();
        errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);
      } catch (parseError) {
        // If parsing fails, use the status text
        errorDetail = response.statusText;
      }
      throw new Error(`Approval failed: ${response.status} ${errorDetail}`);
    }

    const resultData = await response.json();
    console.log("Received approval result:", resultData);

    return {
      success: true,
      message: resultData.message || `Workflow ${decision}d successfully`,
    };
  } catch (error) {
    console.error("Error sending approval decision:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Workflow Regeneration Types and Functions
export interface RegenerateResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Sends a regenerate request for a specific node in a workflow execution.
 *
 * @param correlationId The correlation ID of the workflow execution
 * @param nodeId The ID of the node to regenerate
 * @returns A promise that resolves to a RegenerateResponse
 */
export async function sendRegenerateRequest(
  correlationId: string,
  nodeId: string,
): Promise<RegenerateResponse> {
  try {
    console.log(`Sending regenerate request for node ${nodeId} with correlation ID: ${correlationId}`);

    // Direct call to the external API, not going through our backend
    const response = await fetch(API_ENDPOINTS.WORKFLOW_EXECUTION.RE_EXECUTE, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getClientAccessToken()}`,
      },
      body: JSON.stringify({
        action: "regenerate",
        correlationId,
        node_id: nodeId,
      }),
    });

    if (!response.ok) {
      let errorDetail = "Unknown regenerate error";
      try {
        // Try to parse more detailed error from response body
        const errorData = await response.json();
        errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);
      } catch (parseError) {
        // If parsing fails, use the status text
        errorDetail = response.statusText;
      }
      throw new Error(`Regenerate failed: ${response.status} ${errorDetail}`);
    }

    const resultData = await response.json();
    console.log("Received regenerate result:", resultData);

    return {
      success: true,
      message: resultData.message || "Node regeneration started successfully",
    };
  } catch (error) {
    console.error("Error sending regenerate request:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Provider and Model API Types
export interface Provider {
  id: string;
  provider: string;
  description: string;
  baseUrl: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  modelCount: number;
}

export interface Model {
  id: string;
  providerId: string;
  model: string;
  modelId: string;
  description: string;
  pricePerTokens: number;
  maxTokens: number;
  temperature: number;
  providerType: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  provider: Provider;
}

export interface ProvidersApiResponse {
  success: boolean;
  message: string;
  providers: Provider[];
  pagination?: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
}

export interface ModelsApiResponse {
  success: boolean;
  message: string;
  models: Model[];
  pagination?: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
}

/**
 * Fetch model providers from the API
 * @returns A promise that resolves to a ProvidersApiResponse object
 */
export async function fetchProviders(): Promise<ProvidersApiResponse> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_ENDPOINTS.PROVIDERS.LIST}?page=1&page_size=100&is_active=true`, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data: ProvidersApiResponse = await response.json();
    console.log("Fetched providers:", data);
    return data;
  } catch (error) {
    console.error("Failed to fetch providers:", error);
    // Return fallback data
    return {
      success: false,
      message: "Failed to fetch providers",
      providers: [
        {
          id: "fallback-openai",
          provider: "OpenAI",
          description: "OpenAI provider",
          baseUrl: "https://api.openai.com/v1",
          isActive: true,
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          modelCount: 5
        },
        {
          id: "fallback-anthropic",
          provider: "Anthropic",
          description: "Anthropic provider",
          baseUrl: "https://api.anthropic.com",
          isActive: true,
          isDefault: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          modelCount: 3
        }
      ]
    };
  }
}

/**
 * Fetch MCPs by IDs
 * @param ids Array of MCP IDs to fetch
 * @returns A promise that resolves to MCPs data
 */
export async function fetchMCPsByIds(ids: string[]): Promise<any> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}/mcps/by-ids`, {
      method: "POST",
      headers: headers,
      body: JSON.stringify({ ids }),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log("Fetched MCPs by IDs:", data);
    return data;
  } catch (error) {
    console.error("Failed to fetch MCPs by IDs:", error);
    return { success: false, message: "Failed to fetch MCPs", mcps: [], total: 0 };
  }
}

/**
 * Fetch workflows by IDs
 * @param ids Array of workflow IDs to fetch
 * @returns A promise that resolves to workflows data
 */
export async function fetchWorkflowsByIds(ids: string[]): Promise<any> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}/workflows/by-ids`, {
      method: "POST",
      headers: headers,
      body: JSON.stringify({ ids }),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log("Fetched workflows by IDs:", data);
    return data;
  } catch (error) {
    console.error("Failed to fetch workflows by IDs:", error);
    return { success: false, message: "Failed to fetch workflows", workflows: [], total: 0 };
  }
}

/**
 * Fetch models from the API
 * @returns A promise that resolves to a ModelsApiResponse object
 */
export async function fetchModels(): Promise<ModelsApiResponse> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_ENDPOINTS.MODELS.LIST}?page=1&page_size=100&is_active=true`, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data: ModelsApiResponse = await response.json();
    console.log("Fetched models:", data);
    return data;
  } catch (error) {
    console.error("Failed to fetch models:", error);
    // Return fallback data
    return {
      success: false,
      message: "Failed to fetch models",
      models: [
        {
          id: "fallback-gpt4o",
          providerId: "fallback-openai",
          model: "gpt-4o",
          modelId: "gpt-4o",
          description: "GPT-4o model",
          pricePerTokens: 0.005,
          maxTokens: 4096,
          temperature: 0.7,
          providerType: "chat",
          isActive: true,
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          provider: {
            id: "fallback-openai",
            provider: "OpenAI",
            description: "OpenAI provider",
            baseUrl: "https://api.openai.com/v1",
            isActive: true,
            isDefault: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            modelCount: 5
          }
        }
      ]
    };
  }
}

/**
 * Fetch models for a specific provider from the API
 * @param providerId - The ID of the provider
 * @returns A promise that resolves to a ModelsApiResponse object
 */
export async function fetchModelsByProvider(providerId: string): Promise<ModelsApiResponse> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_ENDPOINTS.MODELS.BY_PROVIDER(providerId)}?page=1&page_size=100&is_active=true`, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data: ModelsApiResponse = await response.json();
    console.log(`Fetched models for provider ${providerId}:`, data);
    return data;
  } catch (error) {
    console.error(`Failed to fetch models for provider ${providerId}:`, error);
    // Return fallback data
    return {
      success: false,
      message: `Failed to fetch models for provider ${providerId}`,
      models: []
    };
  }
}

/**
 * Fetch MCP components for a specific category with pagination metadata
 * @param category The category to fetch components for
 * @param page The page number to fetch
 * @param pageSize The number of items per page
 * @returns A promise that resolves to an object with data and metadata
 */
export async function fetchMCPComponentsByCategoryWithMetadata(
  category: string, 
  page: number = 1, 
  pageSize: number = 5
): Promise<{
  data: ComponentsApiResponse;
  metadata: {
    total: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}> {
  try {
    // Get the access token based on environment
    let accessToken;
    if (typeof window !== "undefined") {
      // Client-side
      accessToken = getClientAccessToken();
    } else {
      // Server-side
      accessToken = await getAccessToken();
    }

    // Prepare headers with authentication
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add Authorization header if we have a token
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    // Handle the "Tools" category specially - it represents components with null component_category
    let url;
    if (category === "Tools") {
      // For "Tools" category, call the API without component_category parameter
      // This will naturally return components with null component_category
      url = `${API_ENDPOINTS.MCPS.LIST}?page=${page}&page_size=${pageSize}`;
    } else {
      // Encode the category for URL
      const encodedCategory = encodeURIComponent(category);
      url = `${API_ENDPOINTS.MCPS.LIST}?page=${page}&page_size=${pageSize}&component_category=${encodedCategory}`;
    }
    
    const response = await fetch(url, {
      method: "GET",
      headers: headers,
    });

    console.log(`Sent the request to fetch MCP components for category "${category}" with the url as:`, response.url);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log(`Fetched MCP response for category "${category}":`, responseData);

    // Extract data and metadata from the response
    let mcpData: any[] = [];
    let metadata = {
      total: 0,
      totalPages: 1,
      currentPage: page,
      pageSize: pageSize,
      hasNextPage: false,
      hasPreviousPage: page > 1,
    };

    // Handle the new API structure with metadata
    if (responseData && responseData.data && Array.isArray(responseData.data)) {
      mcpData = responseData.data;
      if (responseData.metadata) {
        metadata = {
          total: responseData.metadata.total || 0,
          totalPages: responseData.metadata.totalPages || 1,
          currentPage: responseData.metadata.currentPage || page,
          pageSize: responseData.metadata.pageSize || pageSize,
          hasNextPage: responseData.metadata.hasNextPage || false,
          hasPreviousPage: responseData.metadata.hasPreviousPage || (page > 1),
        };
      }
    } else if (Array.isArray(responseData)) {
      // Fallback for old API structure
      mcpData = responseData;
    } else {
      console.error(`MCP response for category "${category}" is not in expected format`);
      return {
        data: {},
        metadata: {
          total: 0,
          totalPages: 1,
          currentPage: page,
          pageSize: pageSize,
          hasNextPage: false,
          hasPreviousPage: false,
        }
      };
    }

    console.log(`Processed MCP data for category "${category}":`, mcpData);

    // Transform the MCP array into the ComponentsApiResponse format
    const mcpComponents: ComponentsApiResponse = {};

    // Process each MCP component for this category
    mcpData.forEach((mcp: any) => {
      console.log(`Processing MCP: ${mcp.name}, component_category: "${mcp.component_category}"`);

      // For "Tools" category, include all MCPs regardless of their component_category
      if (category === "Tools") {
        console.log(`Including MCP ${mcp.name} in Tools category`);
      } else {
        // For other categories, check if the component belongs to this category
        if (mcp.component_category && mcp.component_category.trim() !== category) {
          console.log(`Skipping MCP ${mcp.name} - has component_category "${mcp.component_category}" but requested ${category} category`);
          return;
        }
        console.log(`Including MCP ${mcp.name} in category "${category}"`);
      }

      // Determine the category key for grouping
      let categoryKey = category;

      // For each tool in the MCP, create a component
      if (
        mcp.mcp_tools_config &&
        mcp.mcp_tools_config.tools &&
        mcp.mcp_tools_config.tools.length > 0
      ) {
        mcp.mcp_tools_config.tools.forEach((tool: any) => {
          // Create a unique component name
          const componentName = `MCP_${mcp.name}_${tool.name}`.replace(/\s+/g, "_");

          // Log tool information
          console.log(`  Tool: ${tool.name} -> Category: "${categoryKey}"`);
          console.log(`  Component name: ${componentName}`);

          // Initialize the category if it doesn't exist
          if (!mcpComponents[categoryKey]) {
            mcpComponents[categoryKey] = {};
          }

          // Create the component definition
          mcpComponents[categoryKey][componentName] = {
            name: componentName,
            display_name: `${mcp.name} - ${tool.name}`,
            description: tool.description || mcp.description,
            category: categoryKey,
            icon: "Cloud", // Default icon for MCP components
            beta: true,
            inputs: [],
            outputs: [],
            is_valid: true,
            path: `mcp.${componentName.toLowerCase()}`,
            type: "MCP",
            env_keys: mcp.env_keys || [],
            env_credential_status: mcp.env_credential_status || null,
            logo: mcp.logo || null,
            oauth_details: mcp.oauth_details || null,
            mcp_info: {
              server_id: mcp.id || "",
              server_path: mcp.url || "",
              tool_name: tool.name,
              input_schema: tool.input_schema,
              output_schema: tool.output_schema,
            },
          };

          // Convert input schema to input definitions
          if (tool.input_schema && tool.input_schema.properties) {
            const requiredFields = tool.input_schema.required || [];
            const definitions = tool.input_schema.$defs || {};

            Object.entries(tool.input_schema.properties).forEach(
              ([propName, propSchema]: [string, any]) => {
                // Handle anyOf schemas by finding the first non-null type
                const resolvedSchema = resolveAnyOfSchema(propSchema, definitions);
                const resolvedType = resolvedSchema.type;

                // Debug logging for array types and anyOf schemas
                if (resolvedType === "array" || propSchema.anyOf) {
                  console.log(`[API] Processing complex field ${propName}:`, {
                    originalType: propSchema.type,
                    resolvedType,
                    hasAnyOf: !!propSchema.anyOf,
                    propSchema,
                    resolvedSchema
                  });
                }

                const inputDef = {
                  name: propName,
                  display_name: resolvedSchema.title || propName.replace(/_/g, " "),
                  info: resolvedSchema.description || "",
                  input_type: mapJsonSchemaTypeToInputType(resolvedType, resolvedSchema),
                  input_types: null,
                  required: requiredFields.includes(propName),
                  is_handle: true,
                  is_list: resolvedType === "array",
                  real_time_refresh: false,
                  advanced: false,
                  value: resolvedSchema.default || null,
                  options: resolvedSchema.enum ? resolvedSchema.enum : null,
                  visibility_rules: null,
                  visibility_logic: "OR" as "OR" | "AND",
                };

                // Debug logging for final inputDef (arrays and complex schemas)
                if (resolvedType === "array" || propSchema.anyOf || inputDef.is_list) {
                  console.log(`[API] Final inputDef for ${propName}:`, {
                    name: inputDef.name,
                    display_name: inputDef.display_name,
                    input_type: inputDef.input_type,
                    is_list: inputDef.is_list,
                    resolvedType,
                    originalHadAnyOf: !!propSchema.anyOf
                  });
                }

                mcpComponents[categoryKey][componentName].inputs.push(inputDef);
              },
            );
          }

          // Add output definitions based on output schema
          if (tool.output_schema && tool.output_schema.properties) {
            Object.entries(tool.output_schema.properties).forEach(
              ([propName, propSchema]: [string, any]) => {
                const outputDef = {
                  name: propName,
                  display_name: propSchema.title || propName.replace(/_/g, " "),
                  output_type: mapJsonSchemaTypeToOutputType(propSchema.type),
                };

                mcpComponents[categoryKey][componentName].outputs.push(outputDef);
              },
            );
          }

          // If no outputs were defined, add a default output
          if (mcpComponents[categoryKey][componentName].outputs.length === 0) {
            mcpComponents[categoryKey][componentName].outputs.push({
              name: "result",
              display_name: "Result",
              output_type: "any",
            });
          }
        });
      } else {
        // If the MCP server doesn't have any tools, create a component for the server itself
        console.log(`MCP server ${mcp.name} has no tools, creating server component`);

        // Create a unique component name for the MCP server itself
        const componentName = `MCP_${mcp.name}_Server`.replace(/\s+/g, "_");

        // Initialize the category if it doesn't exist
        if (!mcpComponents[categoryKey]) {
          mcpComponents[categoryKey] = {};
        }

        // Create the component definition for the MCP server
        mcpComponents[categoryKey][componentName] = {
          name: componentName,
          display_name: mcp.name,
          description: mcp.description || `MCP Server: ${mcp.name}`,
          category: categoryKey,
          icon: "Cloud",
          beta: true,
          inputs: [],
          outputs: [
            {
              name: "result",
              display_name: "Result",
              output_type: "any",
            },
          ],
          is_valid: true,
          path: `mcp.${componentName.toLowerCase()}`,
          type: "MCP",
          env_keys: mcp.env_keys || [],
          env_credential_status: mcp.env_credential_status || null,
          oauth_details: mcp.oauth_details || null,
          mcp_info: {
            server_id: mcp.id || "",
            server_path: mcp.url || "",
            tool_name: "server",
            input_schema: null,
            output_schema: { type: "object", properties: {} },
          },
        };

        console.log(
          `  Created MCP Server component: ${componentName} in category: "${categoryKey}"`,
        );
      }
    });

    console.log(`Transformed MCP components for category "${category}":`, mcpComponents);
    return {
      data: mcpComponents,
      metadata: metadata
    };
  } catch (error) {
    console.error(`Failed to fetch MCP components for category "${category}":`, error);
    return {
      data: {},
      metadata: {
        total: 0,
        totalPages: 1,
        currentPage: page,
        pageSize: pageSize,
        hasNextPage: false,
        hasPreviousPage: false,
      }
    };
  }
}
