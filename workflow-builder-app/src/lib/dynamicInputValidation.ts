import { InputDefinition } from "@/types";
import { z } from "zod";
import { DynamicInputTypeMapper, JsonSchemaProperty } from "./dynamicInputTypeMapper";

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  message: string;
  errors?: string[];
}

/**
 * Dynamic input validation system that can validate any input type
 */
export class DynamicInputValidator {
  /**
   * Validate an input value based on its definition using dynamic mapping
   */
  static validateInput(inputDef: InputDefinition, value: any): ValidationResult {
    try {
      // Skip validation for empty optional inputs
      if (!inputDef.required && (value === undefined || value === null || value === "")) {
        return { isValid: true, message: "" };
      }

      // Create schema from input definition
      const schema = this.createSchemaFromInputDef(inputDef);
      
      // Validate using Zod
      const result = schema.safeParse(value);
      
      if (result.success) {
        return { isValid: true, message: "Valid" };
      } else {
        const errors = result.error.errors.map(err => err.message);
        return {
          isValid: false,
          message: errors[0] || "Invalid input",
          errors
        };
      }
    } catch (error) {
      console.warn(`Validation error for ${inputDef.name}:`, error);
      return { isValid: false, message: "Validation failed" };
    }
  }

  /**
   * Create a Zod schema from an InputDefinition
   */
  static createSchemaFromInputDef(inputDef: InputDefinition): z.ZodTypeAny {
    // Get the mapping for this input type
    const schema: JsonSchemaProperty = {
      type: this.getBaseType(inputDef.input_type),
      format: this.getFormat(inputDef.input_type),
      enum: inputDef.options?.map(opt => typeof opt === 'string' ? opt : opt.value),
      minimum: inputDef.min_value,
      maximum: inputDef.max_value,
      minLength: inputDef.min_length,
      maxLength: inputDef.max_length,
      pattern: inputDef.pattern,
      properties: inputDef.properties,
    };

    return this.createZodSchemaFromJsonSchema(schema, inputDef.required);
  }

  /**
   * Create a Zod schema from a JSON Schema property
   */
  static createZodSchemaFromJsonSchema(schema: JsonSchemaProperty, required: boolean = false): z.ZodTypeAny {
    let zodSchema: z.ZodTypeAny;

    // Handle enum types first
    if (schema.enum && schema.enum.length > 0) {
      zodSchema = z.enum(schema.enum as [string, ...string[]]);
    } else {
      // Handle by type
      switch (schema.type) {
        case 'string':
          zodSchema = this.createStringSchema(schema);
          break;
        case 'number':
        case 'integer':
          zodSchema = this.createNumberSchema(schema);
          break;
        case 'boolean':
          zodSchema = z.boolean();
          break;
        case 'object':
          zodSchema = this.createObjectSchema(schema);
          break;
        case 'array':
          zodSchema = this.createArraySchema(schema);
          break;
        case 'null':
          zodSchema = z.null();
          break;
        default:
          // Fallback to any for unknown types
          zodSchema = z.any();
      }
    }

    // Make optional if not required
    if (!required) {
      zodSchema = zodSchema.optional();
    }

    return zodSchema;
  }

  /**
   * Create string schema with format validation
   */
  private static createStringSchema(schema: JsonSchemaProperty): z.ZodString {
    let stringSchema = z.string();

    // Apply length constraints
    if (schema.minLength !== undefined) {
      stringSchema = stringSchema.min(schema.minLength, {
        message: `Must be at least ${schema.minLength} characters`
      });
    }
    if (schema.maxLength !== undefined) {
      stringSchema = stringSchema.max(schema.maxLength, {
        message: `Must be at most ${schema.maxLength} characters`
      });
    }

    // Apply pattern validation
    if (schema.pattern) {
      stringSchema = stringSchema.regex(new RegExp(schema.pattern), {
        message: "Invalid format"
      });
    }

    // Apply format-specific validation
    if (schema.format) {
      switch (schema.format) {
        case 'uri':
        case 'url':
          stringSchema = stringSchema.url({ message: "Must be a valid URL" });
          break;
        case 'email':
          stringSchema = stringSchema.email({ message: "Must be a valid email address" });
          break;
        case 'uuid':
          stringSchema = stringSchema.uuid({ message: "Must be a valid UUID" });
          break;
        case 'date':
          stringSchema = stringSchema.regex(/^\d{4}-\d{2}-\d{2}$/, {
            message: "Must be a valid date (YYYY-MM-DD)"
          });
          break;
        case 'time':
          stringSchema = stringSchema.regex(/^\d{2}:\d{2}(:\d{2})?$/, {
            message: "Must be a valid time (HH:MM or HH:MM:SS)"
          });
          break;
        case 'datetime':
          stringSchema = stringSchema.datetime({ message: "Must be a valid datetime" });
          break;
        case 'ipv4':
          stringSchema = stringSchema.ip({ version: 'v4', message: "Must be a valid IPv4 address" });
          break;
        case 'ipv6':
          stringSchema = stringSchema.ip({ version: 'v6', message: "Must be a valid IPv6 address" });
          break;
      }
    }

    return stringSchema;
  }

  /**
   * Create number schema with range validation
   */
  private static createNumberSchema(schema: JsonSchemaProperty): z.ZodNumber {
    let numberSchema = schema.type === 'integer' ? z.number().int() : z.number();

    if (schema.minimum !== undefined) {
      numberSchema = numberSchema.min(schema.minimum, {
        message: `Must be at least ${schema.minimum}`
      });
    }
    if (schema.maximum !== undefined) {
      numberSchema = numberSchema.max(schema.maximum, {
        message: `Must be at most ${schema.maximum}`
      });
    }

    return numberSchema;
  }

  /**
   * Create object schema
   */
  private static createObjectSchema(schema: JsonSchemaProperty): z.ZodRecord {
    if (schema.properties && Object.keys(schema.properties).length > 0) {
      // Create a proper object schema with defined properties
      const shape: Record<string, z.ZodTypeAny> = {};
      const required = schema.required || [];

      Object.entries(schema.properties).forEach(([key, prop]) => {
        shape[key] = this.createZodSchemaFromJsonSchema(prop, required.includes(key));
      });

      return z.object(shape);
    } else {
      // Generic object schema
      return z.record(z.any());
    }
  }

  /**
   * Create array schema
   */
  private static createArraySchema(schema: JsonSchemaProperty): z.ZodArray<any> {
    let arraySchema = z.array(z.any());

    // If items schema is defined, use it
    if (schema.items) {
      const itemSchema = this.createZodSchemaFromJsonSchema(schema.items, false);
      arraySchema = z.array(itemSchema);
    }

    return arraySchema;
  }

  /**
   * Extract base type from input_type string
   */
  private static getBaseType(inputType: string): string {
    if (inputType.includes(':')) {
      return inputType.split(':')[0];
    }
    
    const typeMap: Record<string, string> = {
      'int': 'integer',
      'float': 'number',
      'bool': 'boolean',
      'dict': 'object',
      'json': 'object',
      'list': 'array',
      'url': 'string',
      'email': 'string',
      'password': 'string',
      'multiline': 'string',
      'code': 'string',
      'dropdown': 'string',
      'select': 'string',
    };
    
    return typeMap[inputType] || inputType;
  }

  /**
   * Extract format from input_type string
   */
  private static getFormat(inputType: string): string | undefined {
    if (inputType.includes(':')) {
      return inputType.split(':')[1];
    }
    
    const formatMap: Record<string, string> = {
      'url': 'uri',
      'email': 'email',
      'password': 'password',
      'date': 'date',
      'time': 'time',
      'datetime': 'datetime',
    };
    
    return formatMap[inputType];
  }

  /**
   * Validate all inputs in a node configuration
   */
  static validateAllInputs(
    inputs: InputDefinition[],
    config: Record<string, any>
  ): Record<string, ValidationResult> {
    const results: Record<string, ValidationResult> = {};

    inputs.forEach((inputDef) => {
      // Skip handle inputs
      if (inputDef.is_handle) return;

      const value = config[inputDef.name];
      results[inputDef.name] = this.validateInput(inputDef, value);
    });

    return results;
  }

  /**
   * Check if all inputs in a configuration are valid
   */
  static areAllInputsValid(
    inputs: InputDefinition[],
    config: Record<string, any>
  ): boolean {
    const results = this.validateAllInputs(inputs, config);
    return Object.values(results).every(result => result.isValid);
  }
}

/**
 * Convenience function for validating a single input
 */
export function validateInput(inputDef: InputDefinition, value: any): ValidationResult {
  return DynamicInputValidator.validateInput(inputDef, value);
}

/**
 * Convenience function for validating all inputs
 */
export function validateAllInputs(
  inputs: InputDefinition[],
  config: Record<string, any>
): Record<string, ValidationResult> {
  return DynamicInputValidator.validateAllInputs(inputs, config);
}
