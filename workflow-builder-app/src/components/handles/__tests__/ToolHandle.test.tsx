/**
 * Tests for ToolHandle component
 */

import React from "react";
import { render, screen, fireEvent, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import { ReactFlowProvider } from "reactflow";
import ToolHandle from "../ToolHandle";

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock getComputedStyle
Object.defineProperty(window, 'getComputedStyle', {
  value: jest.fn(() => ({
    content: '"🔧"',
    getPropertyValue: jest.fn(),
  })),
});

// Test wrapper component with React Flow provider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ReactFlowProvider>
    {children}
  </ReactFlowProvider>
);

// Helper function to render ToolHandle with providers
const renderToolHandle = (props: any = {}) => {
  const defaultProps = {
    id: "tool_1",
    displayName: "Tool 1",
    position: "left",
    isConnectable: true,
    ...props,
  };

  return render(
    <TestWrapper>
      <ToolHandle {...defaultProps} />
    </TestWrapper>
  );
};

describe("ToolHandle Component", () => {
  describe("Rendering", () => {
    test("renders tool handle with correct styling", () => {
      renderToolHandle();

      const handle = screen.getByTestId("tool-handle-tool_1");
      expect(handle).toBeInTheDocument();
      expect(handle).toHaveClass("tool-handle");
    });

    test("displays wrench icon", () => {
      renderToolHandle();

      const handle = screen.getByTestId("tool-handle-tool_1");
      expect(handle).toHaveAttribute("data-tool-handle", "true");
      
      // Check for wrench icon in CSS content
      const computedStyle = window.getComputedStyle(handle, "::before");
      expect(computedStyle.content).toContain("🔧");
    });

    test("renders with correct display name", () => {
      renderToolHandle({ displayName: "Custom Tool" });

      const label = screen.getByText("Custom Tool");
      expect(label).toBeInTheDocument();
      expect(label).toHaveClass("tool-handle-label");
    });

    test("applies correct position styling", () => {
      renderToolHandle({ position: "right" });

      const container = screen.getByTestId("tool-handle-container-tool_1");
      expect(container).toHaveClass("tool-handle-right");
    });
  });

  describe("Interactions", () => {
    test("handles hover states correctly", () => {
      renderToolHandle();

      const handle = screen.getByTestId("tool-handle-tool_1");
      
      fireEvent.mouseEnter(handle);
      expect(handle).toHaveClass("tool-handle-hover");
      
      fireEvent.mouseLeave(handle);
      expect(handle).not.toHaveClass("tool-handle-hover");
    });

    test("handles focus states correctly", () => {
      renderToolHandle();

      const handle = screen.getByTestId("tool-handle-tool_1");

      act(() => {
        fireEvent.focus(handle);
      });
      expect(handle).toHaveClass("tool-handle-focus");

      act(() => {
        fireEvent.blur(handle);
      });
      expect(handle).not.toHaveClass("tool-handle-focus");
    });

    test("supports keyboard navigation", () => {
      renderToolHandle();

      const handle = screen.getByTestId("tool-handle-tool_1");
      expect(handle).toHaveAttribute("tabIndex", "0");
      
      fireEvent.keyDown(handle, { key: "Enter" });
      // Should trigger connection logic (to be implemented)
    });

    test("calls onConnect when connection is made", () => {
      const onConnect = jest.fn();
      renderToolHandle({ onConnect });

      const handle = screen.getByTestId("tool-handle-tool_1");
      fireEvent.click(handle);
      
      // Connection logic will be implemented
      expect(onConnect).toHaveBeenCalledWith("tool_1");
    });
  });

  describe("Accessibility", () => {
    test("has proper ARIA attributes", () => {
      renderToolHandle({ displayName: "Data Tool" });

      const handle = screen.getByTestId("tool-handle-tool_1");
      expect(handle).toHaveAttribute("aria-label", "Tool handle: Data Tool");
      expect(handle).toHaveAttribute("role", "button");
    });

    test("supports screen readers", () => {
      renderToolHandle({ displayName: "Analysis Tool" });

      const handle = screen.getByTestId("tool-handle-tool_1");
      expect(handle).toHaveAttribute("aria-describedby");
      
      const description = screen.getByText(/Connect workflow components as tools/);
      expect(description).toBeInTheDocument();
    });

    test("has proper focus management", () => {
      renderToolHandle();

      const handle = screen.getByTestId("tool-handle-tool_1");

      act(() => {
        handle.focus();
      });

      expect(document.activeElement).toBe(handle);
      expect(handle).toHaveAttribute("tabIndex", "0");
    });
  });

  describe("States", () => {
    test("shows connected state when tool is connected", () => {
      renderToolHandle({ isConnected: true });

      const handle = screen.getByTestId("tool-handle-tool_1");
      expect(handle).toHaveClass("tool-handle-connected");
      
      const label = screen.getByTestId("tool-handle-label-tool_1");
      expect(label).toHaveClass("tool-handle-label-connected");
    });

    test("shows disabled state when not connectable", () => {
      renderToolHandle({ isConnectable: false });

      const handle = screen.getByTestId("tool-handle-tool_1");
      expect(handle).toHaveClass("tool-handle-disabled");
      expect(handle).toHaveAttribute("tabIndex", "-1");
    });

    test("shows error state when connection fails", () => {
      renderToolHandle({ hasError: true, errorMessage: "Connection failed" });

      const handle = screen.getByTestId("tool-handle-tool_1");
      expect(handle).toHaveClass("tool-handle-error");
      
      const errorMessage = screen.getByText("Connection failed");
      expect(errorMessage).toBeInTheDocument();
    });
  });

  describe("Positioning", () => {
    test("calculates correct position for left handles", () => {
      renderToolHandle({ position: "left", index: 2 });

      const container = screen.getByTestId("tool-handle-container-tool_1");
      expect(container).toHaveStyle("top: 90px"); // 30px * (2 + 1)
    });

    test("calculates correct position for right handles", () => {
      renderToolHandle({ position: "right", index: 1 });

      const container = screen.getByTestId("tool-handle-container-tool_1");
      expect(container).toHaveStyle("top: 60px"); // 30px * (1 + 1)
    });

    test("handles dynamic positioning with multiple handles", () => {
      renderToolHandle({ 
        position: "left", 
        index: 0, 
        totalHandles: 5,
        spacing: 25 
      });

      const container = screen.getByTestId("tool-handle-container-tool_1");
      expect(container).toHaveStyle("top: 25px"); // 25px * (0 + 1)
    });
  });

  describe("Performance", () => {
    test("renders efficiently with many handles", () => {
      const startTime = performance.now();
      
      for (let i = 0; i < 10; i++) {
        renderToolHandle({ id: `tool_${i}`, displayName: `Tool ${i}` });
      }
      
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(100); // Should render in <100ms
    });

    test("handles rapid state changes efficiently", () => {
      renderToolHandle();

      const handle = screen.getByTestId("tool-handle-tool_1");
      
      const startTime = performance.now();
      
      // Simulate rapid hover/focus changes
      for (let i = 0; i < 50; i++) {
        fireEvent.mouseEnter(handle);
        fireEvent.mouseLeave(handle);
      }
      
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(200); // Should handle in <200ms
    });
  });
});
