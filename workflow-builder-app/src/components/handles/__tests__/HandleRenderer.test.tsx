/**
 * Tests for Handle<PERSON>enderer component
 */

import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { ReactFlowProvider } from "reactflow";
import HandleRenderer from "../HandleRenderer";

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock tool connection utilities
jest.mock("@/utils/toolConnectionUtils", () => ({
  isToolHandle: jest.fn(),
  getAvailableToolSlots: jest.fn(),
  calculateHandlePositions: jest.fn(),
}));

import { isToolHandle, getAvailableToolSlots, calculateHandlePositions } from "@/utils/toolConnectionUtils";

const mockIsToolHandle = isToolHandle as jest.MockedFunction<typeof isToolHandle>;
const mockGetAvailableToolSlots = getAvailableToolSlots as jest.MockedFunction<typeof getAvailableToolSlots>;
const mockCalculateHandlePositions = calculateHandlePositions as jest.MockedFunction<typeof calculateHandlePositions>;

// Test wrapper component with React Flow provider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ReactFlowProvider>
    {children}
  </ReactFlowProvider>
);

// Mock input definitions
const createMockInputs = (includeTools = true) => [
  {
    name: "input_data",
    display_name: "Input Data",
    input_types: ["Any"],
    is_handle: true,
  },
  {
    name: "query",
    display_name: "Query",
    input_types: ["String"],
    is_handle: true,
  },
  ...(includeTools ? [
    {
      name: "tools",
      display_name: "Tools",
      input_types: ["Any"],
      is_handle: true,
    },
  ] : []),
];

// Helper function to render HandleRenderer with providers
const renderHandleRenderer = (props: any = {}) => {
  const defaultProps = {
    inputs: createMockInputs(),
    outputs: [],
    nodeId: "test-node",
    isConnectable: true,
    position: "left",
    ...props,
  };

  return render(
    <TestWrapper>
      <HandleRenderer {...defaultProps} />
    </TestWrapper>
  );
};

describe("HandleRenderer Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockIsToolHandle.mockImplementation((name) => name === "tools");
    mockGetAvailableToolSlots.mockReturnValue([]);
    mockCalculateHandlePositions.mockReturnValue([
      { name: "input_data", position: { top: 20, left: -5 } },
      { name: "query", position: { top: 50, left: -5 } },
      { name: "tools", position: { top: 80, left: -5 } },
    ]);
  });

  describe("Handle Rendering", () => {
    test("renders all input handles correctly", () => {
      renderHandleRenderer();

      expect(screen.getByTestId("handle-input_data")).toBeInTheDocument();
      expect(screen.getByTestId("handle-query")).toBeInTheDocument();
      expect(screen.getByTestId("tool-handle-tools")).toBeInTheDocument();
    });

    test("separates tool handles from regular handles", () => {
      renderHandleRenderer();

      const regularHandles = screen.getAllByTestId(/^handle-(?!tool)/);
      const toolHandle = screen.getByTestId("tool-handle-tools");

      expect(regularHandles).toHaveLength(2); // input_data, query
      expect(toolHandle).toBeInTheDocument(); // single tools handle
    });

    test("renders handles with correct labels", () => {
      renderHandleRenderer();

      expect(screen.getByText("Input Data")).toBeInTheDocument();
      expect(screen.getByText("Query")).toBeInTheDocument();
      expect(screen.getByText("Tools")).toBeInTheDocument();
    });
  });

  describe("Tool Handle Management", () => {
    test("renders single tools handle", () => {
      renderHandleRenderer();

      const toolsHandle = screen.getByTestId("tool-handle-tools");
      expect(toolsHandle).toBeInTheDocument();
      expect(toolsHandle).toHaveAttribute("data-tool-handle", "true");
    });

    test("tools handle accepts multiple connections", () => {
      renderHandleRenderer();

      const toolsHandle = screen.getByTestId("tool-handle-tools");
      expect(toolsHandle).toBeInTheDocument();

      // The tools handle should be connectable
      expect(toolsHandle).toHaveClass("connectable");
    });
  });



  describe("Layout and Positioning", () => {
    test("calculates optimal spacing for handles", () => {
      renderHandleRenderer({
        inputs: createMockInputs(),
        autoSpacing: true
      });

      expect(mockCalculateHandlePositions).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ name: "input_data" }),
          expect.objectContaining({ name: "query" }),
          expect.objectContaining({ name: "tools" }),
        ]),
        expect.objectContaining({ autoSpacing: true })
      );
    });

    test("handles overflow with many handles gracefully", () => {
      const manyInputs = Array.from({ length: 20 }, (_, i) => ({
        name: `input_${i}`,
        display_name: `Input ${i}`,
        input_types: ["Any"],
        is_handle: true,
      }));

      renderHandleRenderer({
        inputs: manyInputs,
        enableScrolling: true
      });

      const container = screen.getByTestId("handles-container");
      expect(container).toHaveClass("handles-scrollable");
    });

    test("adjusts layout for different node sizes", () => {
      renderHandleRenderer({
        nodeHeight: 200,
        nodeWidth: 300
      });

      expect(mockCalculateHandlePositions).toHaveBeenCalledWith(
        expect.any(Array),
        expect.objectContaining({
          nodeHeight: 200,
          nodeWidth: 300
        })
      );
    });
  });

  describe("Performance Optimization", () => {
    test("memoizes handle rendering", () => {
      const { rerender } = renderHandleRenderer();

      const initialRenderTime = performance.now();

      // Re-render with same props
      rerender(
        <TestWrapper>
          <HandleRenderer
            inputs={createMockInputs()}
            outputs={[]}
            nodeId="test-node"
            isConnectable={true}
            position="left"
          />
        </TestWrapper>
      );

      const rerenderTime = performance.now();
      expect(rerenderTime - initialRenderTime).toBeLessThan(50); // Relaxed timing for CI
    });

    test("efficiently handles large numbers of handles", () => {
      const manyInputs = Array.from({ length: 100 }, (_, i) => ({
        name: `input_${i}`,
        display_name: `Input ${i}`,
        input_types: ["Any"],
        is_handle: true,
      }));

      const startTime = performance.now();
      renderHandleRenderer({ inputs: manyInputs });
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(500); // Relaxed timing for CI
    });
  });
});
