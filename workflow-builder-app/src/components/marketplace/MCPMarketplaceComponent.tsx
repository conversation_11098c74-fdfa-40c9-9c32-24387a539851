import { useEffect, useRef, useMemo } from "react";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { useComponentStateStore } from "@/store/mcpToolsStore";
import { useMCPMarketplaceInputs } from "./MCPMarketplaceInputGenerator";

interface MCPMarketplaceComponentProps {
  node: Node<WorkflowNodeData>;
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void;
}

/**
 * Component for handling MCP Marketplace components in the workflow
 */
export function MCPMarketplaceComponent({ node, onNodeDataChange }: MCPMarketplaceComponentProps) {
  // Use the input generator to process inputs for the node
  const inputs = useMCPMarketplaceInputs(node, onNodeDataChange);

  // Track if we're currently being edited through the inspector panel
  const isBeingEditedRef = useRef(false);

  // Track the last config we processed to avoid circular updates
  const lastProcessedConfigRef = useRef<string>("");

  // Track the last logged state to prevent excessive logging
  const lastLoggedStateRef = useRef<string>("");

  // Memoize component info for debugging to reduce re-computation
  const componentInfo = useMemo(() => {
    if (process.env.NODE_ENV === 'development' && node?.data?.definition) {
      return {
        name: node.data.definition.name,
        inputsLength: inputs.length,
        schema: node.data.definition.mcp_info
      };
    }
    return null;
  }, [node?.data?.definition?.name, node?.data?.definition?.mcp_info]); // Removed inputs.length to prevent double logging

  // Optimized logging with debouncing - only log when component definition changes
  useEffect(() => {
    if (componentInfo && inputs.length > 0) { // Only log when inputs are actually loaded
      const currentState = JSON.stringify({
        name: componentInfo.name,
        schema: componentInfo.schema
      }); // Don't include inputs length in state comparison

      if (currentState !== lastLoggedStateRef.current) {
        console.log("MCP Marketplace component:", componentInfo.name);
        console.log("MCP Marketplace component inputs length:", inputs.length);
        console.log("MCP Marketplace component schema:", componentInfo.schema);
        lastLoggedStateRef.current = currentState;
      }
    }
  }, [componentInfo, inputs.length]); // Re-added inputs.length but with condition to only log when > 0

  // Memoize inputs processing to reduce unnecessary computations
  const processedInputs = useMemo(() => {
    if (!node?.data || inputs.length === 0) return null;

    const newConfig = { ...node.data.config };
    let hasChanges = false;

    // Update the config with default values for any missing inputs
    // For MCP components, we need to set defaults for ALL inputs, including handles
    inputs.forEach((input) => {
      if (newConfig[input.name] === undefined && input.value !== undefined && input.value !== null && input.value !== '') {
        newConfig[input.name] = input.value;
        hasChanges = true;
      }
    });

    // Also set values for all inputs with non-empty values (this ensures defaults are properly applied)
    inputs.forEach((input) => {
      // Only set if the config doesn't already have a user-defined value
      if (newConfig[input.name] === undefined) {
        if (
          input.value !== null &&
          input.value !== undefined &&
          input.value !== "" &&
          (typeof input.value !== "object" || Object.keys(input.value).length > 0)
        ) {
          newConfig[input.name] = input.value;
          hasChanges = true;
        }
      }
    });

    return hasChanges ? newConfig : null;
  }, [node?.data?.config, inputs]);

  // Update the node config when inputs change, but only if not being edited through inspector
  useEffect(() => {
    // Skip this effect if we're currently being edited through the inspector panel
    // or if the node data hasn't actually changed
    if (!node || !node.data || !processedInputs) {
      return;
    }

    // Get the current config JSON to compare
    const currentConfigJson = JSON.stringify(node.data.config || {});
    const newConfigJson = JSON.stringify(processedInputs);

    // If this is the same config we just processed, skip to avoid circular updates
    if (currentConfigJson === lastProcessedConfigRef.current || currentConfigJson === newConfigJson) {
      return;
    }

    // Update our reference to the current config
    lastProcessedConfigRef.current = newConfigJson;

    // Store the config in the component state
    const stateStore = useComponentStateStore.getState();
    stateStore.setValue(node.id, "config", processedInputs);

    // Update the node data
    onNodeDataChange(node.id, {
      ...node.data,
      config: processedInputs,
    });

    // Reset the editing flag
    setTimeout(() => {
      isBeingEditedRef.current = false;
    }, 0);
  }, [node, processedInputs, onNodeDataChange]);

  // Render the actual input fields for MCP Marketplace components
  return (
    <div className="space-y-4 p-4">
      <h3 className="text-sm font-medium">MCP Component Configuration</h3>
      {inputs.map((input) => {
        const value = getMCPMarketplaceInputValue(node, input.name, input.value);

        return (
          <div key={input.name} className="space-y-2">
            <label className="text-xs font-medium text-gray-700">
              {input.display_name || input.name}
              {input.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            {input.info && (
              <p className="text-xs text-gray-500">{input.info}</p>
            )}

            {/* Simple input rendering based on type */}
            {input.input_type === 'dropdown' && input.options ? (
              <select
                value={value || ''}
                onChange={(e) => updateMCPMarketplaceConfig(node, input.name, e.target.value, onNodeDataChange)}
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
              >
                <option value="">Select...</option>
                {input.options.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            ) : input.input_type === 'bool' ? (
              <input
                type="checkbox"
                checked={value || false}
                onChange={(e) => updateMCPMarketplaceConfig(node, input.name, e.target.checked, onNodeDataChange)}
                className="h-4 w-4"
              />
            ) : input.input_type === 'int' || input.input_type === 'number' ? (
              <input
                type="number"
                value={value || ''}
                onChange={(e) => updateMCPMarketplaceConfig(node, input.name, parseInt(e.target.value) || 0, onNodeDataChange)}
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                placeholder={`Enter ${input.display_name || input.name}...`}
              />
            ) : (
              <input
                type="text"
                value={value || ''}
                onChange={(e) => updateMCPMarketplaceConfig(node, input.name, e.target.value, onNodeDataChange)}
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                placeholder={`Enter ${input.display_name || input.name}...`}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}

/**
 * Helper function to get a value from the component state store for MCP Marketplace components
 */
export function getMCPMarketplaceValue(nodeId: string, key: string, defaultValue?: any) {
  const stateStore = useComponentStateStore.getState();
  const config = stateStore.getValue(nodeId, "config", {});
  return config[key] !== undefined ? config[key] : defaultValue;
}

/**
 * Helper function to set a value in the component state store for MCP Marketplace components
 */
export function setMCPMarketplaceValue(nodeId: string, key: string, value: any) {
  const stateStore = useComponentStateStore.getState();
  const config = stateStore.getValue(nodeId, "config", {});
  const newConfig = {
    ...config,
    [key]: value,
  };
  stateStore.setValue(nodeId, "config", newConfig);
}

/**
 * Check if a node is an MCP Marketplace component
 */
export function isMCPMarketplaceComponent(node: Node<WorkflowNodeData> | null): boolean {
  if (!node) return false;

  // Primary check: node.data.type is 'mcp'
  if (node.data.type === "mcp") return true;

  // Fallback checks for backward compatibility

  // Check if the node originalType is MCPToolsComponent
  if (node.data.originalType === "MCPToolsComponent") return true;

  // Check if the node has mcp_info property
  if (node.data.definition && node.data.definition.mcp_info) return true;

  // Check if the node path contains 'mcp_marketplace'
  if (
    node.data.definition &&
    node.data.definition.path &&
    node.data.definition.path.includes("mcp_marketplace")
  )
    return true;

  // Check if the node display name contains 'Script Generator' or 'Generator'
  if (
    node.data.definition &&
    node.data.definition.display_name &&
    (node.data.definition.display_name.includes("Script Generator") ||
      node.data.definition.display_name.includes("Generator"))
  )
    return true;

  // Check if the node label contains 'Script Generator' or 'Generator'
  if (
    node.data.label &&
    (node.data.label.includes("Script Generator") || node.data.label.includes("Generator"))
  )
    return true;

  return false;
}

// Track the last update to prevent circular updates
const lastUpdateMap = new Map<string, { inputName: string, value: any, timestamp: number }>();

/**
 * Update the config for an MCP Marketplace component
 */
export function updateMCPMarketplaceConfig(
  node: Node<WorkflowNodeData>,
  inputName: string,
  value: any,
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void,
) {
  // Check for duplicate/circular updates
  const nodeKey = `${node.id}-${inputName}`;
  const lastUpdate = lastUpdateMap.get(nodeKey);
  const now = Date.now();

  // If we have a recent update (within 100ms) with the same value, skip to prevent circular updates
  if (lastUpdate &&
      now - lastUpdate.timestamp < 100 &&
      JSON.stringify(lastUpdate.value) === JSON.stringify(value)) {
    return;
  }

  // Record this update
  lastUpdateMap.set(nodeKey, { inputName, value, timestamp: now });

  // Get current config
  const currentConfig = node.data.config || {};

  // Check if the value is actually changing
  if (JSON.stringify(currentConfig[inputName]) === JSON.stringify(value)) {
    return;
  }

  if (process.env.NODE_ENV === 'development') {
    console.log(`Current config for node ${node.id}:`, currentConfig);
  }

  // Update config
  const newConfig = {
    ...currentConfig,
    [inputName]: value,
  };

  // Update node data
  const newData = {
    ...node.data,
    config: newConfig,
  };

  // Update component state first

  setMCPMarketplaceValue(node.id, inputName, value);

  // Get the updated component state
  const stateStore = useComponentStateStore.getState();
  const updatedConfig = stateStore.getValue(node.id, "config", {});

  // Call the onNodeDataChange callback
  onNodeDataChange(node.id, newData);
}

/**
 * Get the value for an input in an MCP Marketplace component
 */
export function getMCPMarketplaceInputValue(
  node: Node<WorkflowNodeData>,
  inputName: string,
  defaultValue: any,
) {
  // First try to get from the component state
  const stateStore = useComponentStateStore.getState();
  const config = stateStore.getValue(node.id, "config", {});
  if (config[inputName] !== undefined) {
    return config[inputName];
  }

  // Then try to get from the node config
  if (node.data.config && node.data.config[inputName] !== undefined) {
    return node.data.config[inputName];
  }

  // Finally, return the default value
  return defaultValue;
}
