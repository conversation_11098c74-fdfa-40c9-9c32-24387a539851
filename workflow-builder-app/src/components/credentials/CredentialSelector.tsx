"use client";

import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertCircle, Search, Key } from "lucide-react";
import { getCachedCredentials, isCredentialsLoading } from "@/lib/credentialEnhancer";
import type { Credential, CredentialError } from "@/types/credentials";
import { getErrorMessage } from "@/utils/credentialErrorHandler";

interface CredentialSelectorProps {
  /** Callback when a credential is selected */
  onSelect: (credentialId: string) => void;
  /** Currently selected credential ID */
  value?: string;
  /** Placeholder text */
  placeholder?: string;
  /** Whether the selector is disabled */
  disabled?: boolean;
  /** Whether to show search functionality */
  searchable?: boolean;
  /** Custom CSS class */
  className?: string;
}

const CredentialSelector: React.FC<CredentialSelectorProps> = ({
  onSelect,
  value,
  placeholder = "Select a credential",
  disabled = false,
  searchable = false,
  className = "",
}) => {
  const [credentials, setCredentials] = useState<Credential[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  // Load credentials on component mount using cached data
  useEffect(() => {
    loadCredentials();
  }, []);

  const loadCredentials = async () => {
    setLoading(true);
    setError(null);
    try {
      // Use cached credentials instead of direct API call
      const credentials = await getCachedCredentials();
      setCredentials(credentials || []);
    } catch (err) {
      const errorMessage = err && typeof err === 'object' && 'message' in err
        ? getErrorMessage(err as CredentialError)
        : err instanceof Error
        ? err.message
        : "Failed to load credentials";
      setError(errorMessage);
      console.error("Error fetching credentials:", err);
    } finally {
      setLoading(false);
    }
  };

  // Filter credentials based on search term
  const filteredCredentials = useMemo(() => {
    if (!searchTerm) return credentials;
    
    const term = searchTerm.toLowerCase();
    return credentials.filter(
      (cred) =>
        cred.name.toLowerCase().includes(term) ||
        (cred.description && cred.description.toLowerCase().includes(term))
    );
  }, [credentials, searchTerm]);

  // Find selected credential
  const selectedCredential = credentials.find((cred) => cred.id === value);

  const handleSelect = (credentialId: string) => {
    onSelect(credentialId);
    setIsOpen(false);
    setSearchTerm(""); // Clear search when selecting
  };

  const handleOpenChange = (open: boolean) => {
    if (disabled) return;
    setIsOpen(open);
    if (!open) {
      setSearchTerm(""); // Clear search when closing
    }
  };

  return (
    <div className={className}>
      <Select
        value={value}
        onValueChange={handleSelect}
        disabled={disabled}
        open={isOpen}
        onOpenChange={handleOpenChange}
      >
        <SelectTrigger
          className="w-full"
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
        >
          <div className="flex items-center gap-2">
            <Key className="h-4 w-4 text-muted-foreground" />
            <SelectValue placeholder={placeholder}>
              {selectedCredential ? selectedCredential.name : placeholder}
            </SelectValue>
          </div>
        </SelectTrigger>
        
        <SelectContent className="w-full">
          {/* Search input */}
          {searchable && (
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search credentials..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                  autoFocus
                />
              </div>
            </div>
          )}

          {/* Loading state */}
          {loading && (
            <div className="p-4 text-center text-muted-foreground">
              Loading credentials...
            </div>
          )}

          {/* Error state */}
          {error && (
            <div className="p-4 text-center">
              <div className="flex items-center justify-center gap-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">Error loading credentials</span>
              </div>
            </div>
          )}

          {/* Empty state */}
          {!loading && !error && credentials.length === 0 && (
            <div className="p-4 text-center text-muted-foreground">
              No credentials available
            </div>
          )}

          {/* No search results */}
          {!loading && !error && credentials.length > 0 && filteredCredentials.length === 0 && searchTerm && (
            <div className="p-4 text-center text-muted-foreground">
              No credentials match your search
            </div>
          )}

          {/* Credential list */}
          {!loading && !error && filteredCredentials.length > 0 && (
            <>
              {filteredCredentials.map((credential) => (
                <SelectItem
                  key={credential.id}
                  value={credential.id}
                  className="cursor-pointer"
                >
                  <div className="flex flex-col items-start gap-1">
                    <div className="font-medium">{credential.name}</div>
                    {credential.description && (
                      <div className="text-xs text-muted-foreground">
                        {credential.description}
                      </div>
                    )}
                  </div>
                </SelectItem>
              ))}
            </>
          )}
        </SelectContent>
      </Select>
    </div>
  );
};

export default CredentialSelector;
