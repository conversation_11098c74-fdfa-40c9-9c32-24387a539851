/**
 * Tests for CredentialManager component
 * Following TDD methodology - tests written first
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CredentialManager from '../CredentialManager';
import {
  fetchCredentials,
  createCredential,
  deleteCredential,
} from '../../../lib/api';
import { CredentialErrorType } from '../../../types/credentials';

// Mock the API functions
jest.mock('../../../lib/api');
const mockFetchCredentials = fetchCredentials as jest.MockedFunction<typeof fetchCredentials>;
const mockCreateCredential = createCredential as jest.MockedFunction<typeof createCredential>;
const mockDeleteCredential = deleteCredential as jest.MockedFunction<typeof deleteCredential>;

// Mock window.confirm
const mockConfirm = jest.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true,
});

describe('CredentialManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockConfirm.mockReturnValue(true);
  });

  describe('Initial Loading', () => {
    it('should display loading state initially', async () => {
      mockFetchCredentials.mockImplementation(() => new Promise(() => {})); // Never resolves

      render(<CredentialManager />);

      expect(screen.getByText('Loading credentials...')).toBeInTheDocument();
    });

    it('should load and display credentials on mount', async () => {
      const mockCredentials = [
        {
          id: 'cred-1',
          name: 'Test API Key',
          description: 'Test description',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          lastUsedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: 'cred-2',
          name: 'Another Key',
          description: 'Another description',
          createdAt: '2024-01-02T00:00:00Z',
          updatedAt: '2024-01-02T00:00:00Z',
          lastUsedAt: '2024-01-02T00:00:00Z',
        },
      ];

      mockFetchCredentials.mockResolvedValue({ credentials: mockCredentials });

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByText('Test API Key')).toBeInTheDocument();
        expect(screen.getByText('Another Key')).toBeInTheDocument();
        expect(screen.getByText('Test description')).toBeInTheDocument();
        expect(screen.getByText('Another description')).toBeInTheDocument();
      });
    });

    it('should display empty state when no credentials exist', async () => {
      mockFetchCredentials.mockResolvedValue({ credentials: [] });

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByText('No credentials found. Add one above.')).toBeInTheDocument();
      });
    });

    it('should handle loading errors gracefully', async () => {
      const error = {
        type: CredentialErrorType.NETWORK_ERROR,
        message: 'Network connection failed',
      };
      mockFetchCredentials.mockRejectedValue(error);

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByText('Network connection failed')).toBeInTheDocument();
      });
    });
  });

  describe('Create Credential Form', () => {
    beforeEach(() => {
      mockFetchCredentials.mockResolvedValue({ credentials: [] });
    });

    it('should render form with all required fields', async () => {
      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByLabelText('Name')).toBeInTheDocument();
        expect(screen.getByLabelText('Description')).toBeInTheDocument();
        expect(screen.getByLabelText('Value')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Add Credential' })).toBeInTheDocument();
      });
    });

    it('should create credential with valid data', async () => {
      const newCredential = {
        id: 'new-cred',
        name: 'New API Key',
        description: 'New description',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        lastUsedAt: '2024-01-01T00:00:00Z',
      };

      mockCreateCredential.mockResolvedValue(newCredential);
      mockFetchCredentials.mockResolvedValueOnce({ credentials: [] })
                          .mockResolvedValueOnce({ credentials: [newCredential] });

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByLabelText('Name')).toBeInTheDocument();
      });

      // Fill out the form
      fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'New API Key' } });
      fireEvent.change(screen.getByLabelText('Description'), { target: { value: 'New description' } });
      fireEvent.change(screen.getByLabelText('Value'), { target: { value: 'secret-value' } });

      // Submit the form
      fireEvent.click(screen.getByRole('button', { name: 'Add Credential' }));

      await waitFor(() => {
        expect(mockCreateCredential).toHaveBeenCalledWith({
          name: 'New API Key',
          description: 'New description',
          value: 'secret-value',
        });
      });

      // Verify form is reset and credentials are reloaded
      await waitFor(() => {
        expect(screen.getByLabelText('Name')).toHaveValue(''); // Name field should be empty
        expect(screen.getByText('New API Key')).toBeInTheDocument(); // New credential should appear
      });
    });

    it('should handle validation errors', async () => {
      const validationError = new Error('Name must be unique');

      mockCreateCredential.mockRejectedValue(validationError);

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByLabelText('Name')).toBeInTheDocument();
      });

      // Fill in valid form data that passes HTML5 validation but fails API validation
      fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'Duplicate Name' } });
      fireEvent.change(screen.getByLabelText('Value'), { target: { value: 'test-value' } });

      // Submit form - this should trigger API validation error
      fireEvent.click(screen.getByRole('button', { name: 'Add Credential' }));

      await waitFor(() => {
        expect(screen.getByText('Name must be unique')).toBeInTheDocument();
      });
    });

    it('should show loading state during creation', async () => {
      let resolveCreate: (value: any) => void;
      const createPromise = new Promise((resolve) => {
        resolveCreate = resolve;
      });

      mockCreateCredential.mockReturnValue(createPromise);

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByLabelText('Name')).toBeInTheDocument();
      });

      // Fill out and submit form
      fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'Test' } });
      fireEvent.change(screen.getByLabelText('Value'), { target: { value: 'test-value' } });
      fireEvent.click(screen.getByRole('button', { name: 'Add Credential' }));

      // Should show loading state
      expect(screen.getByText('Adding...')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Adding...' })).toBeDisabled();

      // Resolve the promise
      resolveCreate!({
        id: 'test',
        name: 'Test',
        description: '',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        lastUsedAt: '2024-01-01T00:00:00Z',
      });

      await waitFor(() => {
        expect(screen.getByText('Add Credential')).toBeInTheDocument();
      });
    });
  });

  describe('Delete Credential', () => {
    const mockCredentials = [
      {
        id: 'cred-1',
        name: 'Test API Key',
        description: 'Test description',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        lastUsedAt: '2024-01-01T00:00:00Z',
      },
    ];

    beforeEach(() => {
      mockFetchCredentials.mockResolvedValue({ credentials: mockCredentials });
    });

    it('should delete credential after confirmation', async () => {
      mockDeleteCredential.mockResolvedValue({ success: true });
      mockFetchCredentials.mockResolvedValueOnce({ credentials: mockCredentials })
                          .mockResolvedValueOnce({ credentials: [] });

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByText('Test API Key')).toBeInTheDocument();
      });

      // Click delete button
      fireEvent.click(screen.getByRole('button', { name: 'Delete' }));

      expect(mockConfirm).toHaveBeenCalledWith('Are you sure you want to delete credential "Test API Key"?');

      await waitFor(() => {
        expect(mockDeleteCredential).toHaveBeenCalledWith('cred-1');
      });
    });

    it('should not delete credential if user cancels', async () => {
      mockConfirm.mockReturnValue(false);

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByText('Test API Key')).toBeInTheDocument();
      });

      // Click delete button
      fireEvent.click(screen.getByRole('button', { name: 'Delete' }));

      expect(mockConfirm).toHaveBeenCalled();
      expect(mockDeleteCredential).not.toHaveBeenCalled();
    });

    it('should handle delete errors', async () => {
      const deleteError = {
        type: CredentialErrorType.NOT_FOUND_ERROR,
        message: 'Credential not found',
      };
      mockDeleteCredential.mockRejectedValue(deleteError);

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByText('Test API Key')).toBeInTheDocument();
      });

      // Click delete button
      fireEvent.click(screen.getByRole('button', { name: 'Delete' }));

      await waitFor(() => {
        expect(screen.getByText('Credential not found')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should display user-friendly error messages', async () => {
      const authError = {
        type: CredentialErrorType.AUTHENTICATION_ERROR,
        message: 'Authentication required. Please log in again.',
      };
      mockFetchCredentials.mockRejectedValue(authError);

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByText('Authentication required. Please log in again.')).toBeInTheDocument();
      });
    });

    it('should clear errors when retrying operations', async () => {
      // First call fails
      mockFetchCredentials.mockRejectedValueOnce(new Error('Network error'));
      // Second call succeeds
      mockFetchCredentials.mockResolvedValueOnce({ credentials: [] });

      render(<CredentialManager />);

      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });

      // Retry by trying to create a credential (which triggers reload)
      fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'Test' } });
      fireEvent.change(screen.getByLabelText('Value'), { target: { value: 'test-value' } });

      mockCreateCredential.mockResolvedValue({
        id: 'test',
        name: 'Test',
        description: '',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        lastUsedAt: '2024-01-01T00:00:00Z',
      });

      fireEvent.click(screen.getByRole('button', { name: 'Add Credential' }));

      await waitFor(() => {
        expect(screen.queryByText('Network error')).not.toBeInTheDocument();
      });
    });
  });
});
