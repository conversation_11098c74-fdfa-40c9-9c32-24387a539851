/**
 * Tests for CredentialSelector component
 * Following TDD methodology - tests written first
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CredentialSelector from '../CredentialSelector';
import { fetchCredentials } from '../../../lib/api';

// Mock the API functions
jest.mock('../../../lib/api');
const mockFetchCredentials = fetchCredentials as jest.MockedFunction<typeof fetchCredentials>;

describe('CredentialSelector', () => {
  const mockCredentials = [
    {
      id: 'cred-1',
      name: 'OpenAI API Key',
      description: 'For content generation',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      lastUsedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: 'cred-2',
      name: 'Anthropic Key',
      description: 'For Claude API',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z',
      lastUsedAt: '2024-01-02T00:00:00Z',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockFetchCredentials.mockResolvedValue({ credentials: mockCredentials });
  });

  describe('Basic Rendering', () => {
    it('should render with default placeholder', async () => {
      render(<CredentialSelector onSelect={jest.fn()} />);

      expect(screen.getByText('Select a credential')).toBeInTheDocument();
    });

    it('should render with custom placeholder', async () => {
      render(<CredentialSelector onSelect={jest.fn()} placeholder="Choose API key" />);

      expect(screen.getByText('Choose API key')).toBeInTheDocument();
    });

    it('should load credentials on mount', async () => {
      render(<CredentialSelector onSelect={jest.fn()} />);

      await waitFor(() => {
        expect(mockFetchCredentials).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Credential Selection', () => {
    it('should display credentials in dropdown', async () => {
      render(<CredentialSelector onSelect={jest.fn()} />);

      // Open dropdown
      fireEvent.click(screen.getByText('Select a credential'));

      await waitFor(() => {
        expect(screen.getByText('OpenAI API Key')).toBeInTheDocument();
        expect(screen.getByText('Anthropic Key')).toBeInTheDocument();
      });
    });

    it('should show credential descriptions', async () => {
      render(<CredentialSelector onSelect={jest.fn()} />);

      // Open dropdown
      fireEvent.click(screen.getByText('Select a credential'));

      await waitFor(() => {
        expect(screen.getByText('For content generation')).toBeInTheDocument();
        expect(screen.getByText('For Claude API')).toBeInTheDocument();
      });
    });

    it('should call onSelect when credential is chosen', async () => {
      const mockOnSelect = jest.fn();
      render(<CredentialSelector onSelect={mockOnSelect} />);

      // Open dropdown
      fireEvent.click(screen.getByText('Select a credential'));

      await waitFor(() => {
        expect(screen.getByText('OpenAI API Key')).toBeInTheDocument();
      });

      // Select credential
      fireEvent.click(screen.getByText('OpenAI API Key'));

      expect(mockOnSelect).toHaveBeenCalledWith('cred-1');
    });

    it('should display selected credential name', async () => {
      const mockOnSelect = jest.fn();
      render(<CredentialSelector onSelect={mockOnSelect} value="cred-1" />);

      await waitFor(() => {
        expect(screen.getByText('OpenAI API Key')).toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading state while fetching credentials', async () => {
      let resolveCredentials: (value: any) => void;
      const credentialsPromise = new Promise((resolve) => {
        resolveCredentials = resolve;
      });
      mockFetchCredentials.mockReturnValue(credentialsPromise);

      render(<CredentialSelector onSelect={jest.fn()} />);

      // Open dropdown
      fireEvent.click(screen.getByText('Select a credential'));

      expect(screen.getByText('Loading credentials...')).toBeInTheDocument();

      // Resolve the promise
      resolveCredentials!({ credentials: mockCredentials });

      await waitFor(() => {
        expect(screen.getByText('OpenAI API Key')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle fetch errors gracefully', async () => {
      mockFetchCredentials.mockRejectedValue(new Error('Network error'));

      render(<CredentialSelector onSelect={jest.fn()} />);

      // Open dropdown
      fireEvent.click(screen.getByText('Select a credential'));

      await waitFor(() => {
        expect(screen.getByText('Error loading credentials')).toBeInTheDocument();
      });
    });

    it('should show empty state when no credentials exist', async () => {
      mockFetchCredentials.mockResolvedValue({ credentials: [] });

      render(<CredentialSelector onSelect={jest.fn()} />);

      // Open dropdown
      fireEvent.click(screen.getByText('Select a credential'));

      await waitFor(() => {
        expect(screen.getByText('No credentials available')).toBeInTheDocument();
      });
    });
  });

  describe('Filtering', () => {
    it('should filter credentials by search term', async () => {
      render(<CredentialSelector onSelect={jest.fn()} searchable />);

      // Open dropdown
      fireEvent.click(screen.getByText('Select a credential'));

      await waitFor(() => {
        expect(screen.getByText('OpenAI API Key')).toBeInTheDocument();
        expect(screen.getByText('Anthropic Key')).toBeInTheDocument();
      });

      // Search for "OpenAI"
      const searchInput = screen.getByPlaceholderText('Search credentials...');
      fireEvent.change(searchInput, { target: { value: 'OpenAI' } });

      expect(screen.getByText('OpenAI API Key')).toBeInTheDocument();
      expect(screen.queryByText('Anthropic Key')).not.toBeInTheDocument();
    });

    it('should show no results message when search yields no matches', async () => {
      render(<CredentialSelector onSelect={jest.fn()} searchable />);

      // Open dropdown
      fireEvent.click(screen.getByText('Select a credential'));

      await waitFor(() => {
        expect(screen.getByText('OpenAI API Key')).toBeInTheDocument();
      });

      // Search for non-existent credential
      const searchInput = screen.getByPlaceholderText('Search credentials...');
      fireEvent.change(searchInput, { target: { value: 'NonExistent' } });

      expect(screen.getByText('No credentials match your search')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', async () => {
      render(<CredentialSelector onSelect={jest.fn()} />);

      const trigger = screen.getByRole('combobox');
      expect(trigger).toHaveAttribute('aria-expanded', 'false');
      expect(trigger).toHaveAttribute('aria-haspopup', 'listbox');
    });

    it('should update ARIA attributes when opened', async () => {
      render(<CredentialSelector onSelect={jest.fn()} />);

      const trigger = screen.getByRole('combobox');
      fireEvent.click(trigger);

      await waitFor(() => {
        expect(trigger).toHaveAttribute('aria-expanded', 'true');
      });
    });
  });

  describe('Disabled State', () => {
    it('should render as disabled when disabled prop is true', () => {
      render(<CredentialSelector onSelect={jest.fn()} disabled />);

      const trigger = screen.getByRole('combobox');
      expect(trigger).toBeDisabled();
    });

    it('should not open dropdown when disabled', () => {
      render(<CredentialSelector onSelect={jest.fn()} disabled />);

      const trigger = screen.getByRole('combobox');
      fireEvent.click(trigger);

      expect(screen.queryByText('Loading credentials...')).not.toBeInTheDocument();
    });
  });
});
