"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { AlertCircle, CheckCircle, Download, Upload, Database, ArrowRight } from "lucide-react";
import {
  isMigrationNeeded,
  migrateCredentials,
  clearLocalStorageCredentials,
  backupLocalStorageCredentials,
  getMigrationStats,
  markMigrationCompleted,
  type MigrationResult,
  type MigrationProgress,
} from "@/utils/credentialMigration";

interface MigrationState {
  status: 'checking' | 'ready' | 'migrating' | 'completed' | 'error' | 'not-needed';
  progress?: MigrationProgress;
  result?: MigrationResult;
  error?: string;
  stats?: {
    localCredentials: number;
    apiCredentials: number;
    migrationRecommended: boolean;
    lastMigrationDate?: string;
  };
}

const CredentialMigration: React.FC = () => {
  const [migrationState, setMigrationState] = useState<MigrationState>({
    status: 'checking',
  });
  const [backupData, setBackupData] = useState<string | null>(null);

  useEffect(() => {
    checkMigrationStatus();
  }, []);

  const checkMigrationStatus = async () => {
    try {
      setMigrationState({ status: 'checking' });

      const [migrationCheck, stats] = await Promise.all([
        isMigrationNeeded(),
        getMigrationStats(),
      ]);

      if (!migrationCheck.needed) {
        setMigrationState({
          status: 'not-needed',
          stats,
        });
        return;
      }

      setMigrationState({
        status: 'ready',
        stats,
      });
    } catch (error) {
      setMigrationState({
        status: 'error',
        error: error instanceof Error ? error.message : 'Failed to check migration status',
      });
    }
  };

  const handleCreateBackup = () => {
    try {
      const backup = backupLocalStorageCredentials();
      if (backup) {
        setBackupData(backup);
        
        // Download backup file
        const blob = new Blob([backup], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `credential-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      setMigrationState({
        ...migrationState,
        status: 'error',
        error: 'Failed to create backup',
      });
    }
  };

  const handleStartMigration = async () => {
    try {
      setMigrationState({
        ...migrationState,
        status: 'migrating',
        progress: {
          total: 0,
          processed: 0,
          current: 'Starting migration...',
          status: 'preparing',
        },
      });

      const result = await migrateCredentials((progress) => {
        setMigrationState(prev => ({
          ...prev,
          progress,
        }));
      });

      if (result.success) {
        // Mark migration as completed
        markMigrationCompleted();
        
        setMigrationState({
          status: 'completed',
          result,
        });
      } else {
        setMigrationState({
          status: 'error',
          error: `Migration completed with errors: ${result.errorCount} failed`,
          result,
        });
      }
    } catch (error) {
      setMigrationState({
        status: 'error',
        error: error instanceof Error ? error.message : 'Migration failed',
      });
    }
  };

  const handleClearLocalStorage = async () => {
    if (window.confirm('Are you sure you want to clear the old localStorage credentials? This action cannot be undone.')) {
      const success = clearLocalStorageCredentials();
      if (success) {
        await checkMigrationStatus(); // Refresh status
      } else {
        setMigrationState({
          ...migrationState,
          status: 'error',
          error: 'Failed to clear localStorage credentials',
        });
      }
    }
  };

  const renderProgressBar = () => {
    if (!migrationState.progress) return null;

    const { total, processed, current, status } = migrationState.progress;
    const percentage = total > 0 ? (processed / total) * 100 : 0;

    return (
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>{current}</span>
          <span>{processed}/{total}</span>
        </div>
        <Progress value={percentage} className="w-full" />
        <div className="text-xs text-muted-foreground">
          Status: {status}
        </div>
      </div>
    );
  };

  const renderMigrationResult = () => {
    if (!migrationState.result) return null;

    const { migratedCount, skippedCount, errorCount, errors, details } = migrationState.result;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{migratedCount}</div>
            <div className="text-sm text-green-700">Migrated</div>
          </div>
          <div className="p-3 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{skippedCount}</div>
            <div className="text-sm text-yellow-700">Skipped</div>
          </div>
          <div className="p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{errorCount}</div>
            <div className="text-sm text-red-700">Errors</div>
          </div>
        </div>

        {errors.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-red-600">Errors:</h4>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {errors.map((error, index) => (
                <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {error.error}
                </div>
              ))}
            </div>
          </div>
        )}

        <details className="text-sm">
          <summary className="cursor-pointer font-medium">Migration Details</summary>
          <div className="mt-2 space-y-1 text-muted-foreground">
            {details.map((detail, index) => (
              <div key={index}>{detail}</div>
            ))}
          </div>
        </details>
      </div>
    );
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Credential Migration
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status Display */}
        {migrationState.status === 'checking' && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Checking migration status...</p>
          </div>
        )}

        {migrationState.status === 'not-needed' && (
          <div className="text-center py-8 space-y-4">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
            <div>
              <h3 className="text-lg font-medium">No Migration Needed</h3>
              <p className="text-muted-foreground">
                {migrationState.stats?.lastMigrationDate 
                  ? `Migration was completed on ${new Date(migrationState.stats.lastMigrationDate).toLocaleDateString()}`
                  : 'No credentials found in localStorage to migrate'
                }
              </p>
            </div>
            {migrationState.stats && (
              <div className="text-sm text-muted-foreground">
                <p>Local credentials: {migrationState.stats.localCredentials}</p>
                <p>API credentials: {migrationState.stats.apiCredentials}</p>
              </div>
            )}
          </div>
        )}

        {migrationState.status === 'ready' && (
          <div className="space-y-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium">Migration Available</h3>
              <p className="text-muted-foreground">
                Found {migrationState.stats?.localCredentials} credentials in localStorage that can be migrated to secure storage.
              </p>
            </div>

            {/* Migration Steps */}
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">1</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">Create Backup (Recommended)</h4>
                  <p className="text-sm text-muted-foreground">
                    Download a backup of your current credentials before migration.
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCreateBackup}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Backup
                </Button>
              </div>

              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-green-600">2</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">Start Migration</h4>
                  <p className="text-sm text-muted-foreground">
                    Migrate credentials from localStorage to secure API storage.
                  </p>
                </div>
                <Button
                  onClick={handleStartMigration}
                  className="flex items-center gap-2"
                >
                  <ArrowRight className="h-4 w-4" />
                  Migrate
                </Button>
              </div>
            </div>

            {backupData && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">Backup created successfully</span>
                </div>
              </div>
            )}
          </div>
        )}

        {migrationState.status === 'migrating' && (
          <div className="space-y-4">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <h3 className="text-lg font-medium">Migration in Progress</h3>
              <p className="text-muted-foreground">Please wait while we migrate your credentials...</p>
            </div>
            {renderProgressBar()}
          </div>
        )}

        {migrationState.status === 'completed' && (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium">Migration Completed!</h3>
              <p className="text-muted-foreground">
                Your credentials have been successfully migrated to secure storage.
              </p>
            </div>

            {renderMigrationResult()}

            <div className="p-3 border rounded-lg">
              <h4 className="font-medium mb-2">Next Steps</h4>
              <div className="space-y-2 text-sm">
                <p>✅ Your credentials are now stored securely in the API</p>
                <p>✅ You can manage them in the Credential Manager</p>
                <p>⚠️ Consider clearing the old localStorage data for security</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearLocalStorage}
                className="mt-3"
              >
                Clear Old Data
              </Button>
            </div>
          </div>
        )}

        {migrationState.status === 'error' && (
          <div className="space-y-4">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium">Migration Error</h3>
              <p className="text-muted-foreground">{migrationState.error}</p>
            </div>

            {migrationState.result && renderMigrationResult()}

            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={checkMigrationStatus}>
                Retry
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CredentialMigration;
