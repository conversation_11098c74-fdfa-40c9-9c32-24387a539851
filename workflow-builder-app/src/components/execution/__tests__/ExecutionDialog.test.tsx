// Test for log formatting functionality
describe('Log Download Formatting', () => {
  // Mock URL.createObjectURL and related APIs
  const mockCreateObjectURL = jest.fn();
  const mockRevokeObjectURL = jest.fn();
  const mockClick = jest.fn();
  const mockAppendChild = jest.fn();
  const mockRemoveChild = jest.fn();

  beforeAll(() => {
    Object.defineProperty(window, 'URL', {
      value: {
        createObjectURL: mockCreateObjectURL,
        revokeObjectURL: mockRevokeObjectURL,
      },
      configurable: true,
    });

    Object.defineProperty(document, 'createElement', {
      value: jest.fn().mockImplementation((tagName) => {
        if (tagName === 'a') {
          return {
            href: '',
            download: '',
            click: mockClick,
          };
        }
        return {};
      }),
      configurable: true,
    });

    Object.defineProperty(document.body, 'appendChild', {
      value: mockAppendChild,
      configurable: true,
    });

    Object.defineProperty(document.body, 'removeChild', {
      value: mockRemoveChild,
      configurable: true,
    });

    // Mock Blob constructor
    global.Blob = jest.fn().mockImplementation((content, options) => ({
      content,
      options,
    })) as any;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockCreateObjectURL.mockReturnValue('mock-url');
  });

  it('should properly format mixed logs (strings and objects)', () => {
    // Test the log formatting logic directly
    const mockLogs = [
      'Simple string log',
      {
        workflow_status: 'running',
        node_id: 'test-node-1',
        message: 'Processing data',
        timestamp: '2024-01-01T10:00:00Z'
      },
      'Another string log',
      {
        workflow_status: 'completed',
        node_id: 'test-node-2',
        result: { success: true, data: [1, 2, 3] }
      }
    ];

    // Simulate the log formatting logic from handleDownloadLogs
    const formattedLogs = mockLogs.map((log) => {
      if (typeof log === 'object' && log !== null) {
        // For objects, convert to formatted JSON
        return JSON.stringify(log, null, 2);
      } else if (typeof log === 'string') {
        // For strings, return as-is
        return log;
      } else {
        // For any other type, convert to string
        return String(log);
      }
    });

    const logContent = formattedLogs.join("\n\n");

    // Verify string logs are preserved as-is
    expect(logContent).toContain('Simple string log');
    expect(logContent).toContain('Another string log');

    // Verify object logs are properly JSON stringified
    expect(logContent).toContain('"workflow_status": "running"');
    expect(logContent).toContain('"node_id": "test-node-1"');
    expect(logContent).toContain('"message": "Processing data"');
    expect(logContent).toContain('"workflow_status": "completed"');
    expect(logContent).toContain('"result": {');
    expect(logContent).toContain('"success": true');
    expect(logContent).toContain('"data": [');

    // Verify no "[object Object]" strings are present
    expect(logContent).not.toContain('[object Object]');

    // Verify proper JSON formatting with indentation
    expect(logContent).toContain('{\n  "workflow_status": "running"');
    expect(logContent).toContain('{\n  "workflow_status": "completed"');
  });

  it('should handle edge cases in log formatting', () => {
    const edgeCaseLogs = [
      null,
      undefined,
      '',
      0,
      false,
      [],
      {},
      { empty: null },
      'normal string'
    ];

    // Filter out null/undefined as the real code would
    const validLogs = edgeCaseLogs.filter(log => log !== null && log !== undefined);

    const formattedLogs = validLogs.map((log) => {
      if (typeof log === 'object' && log !== null) {
        return JSON.stringify(log, null, 2);
      } else if (typeof log === 'string') {
        return log;
      } else {
        return String(log);
      }
    });

    const logContent = formattedLogs.join("\n\n");

    // Verify different types are handled correctly
    expect(logContent).toContain('normal string');
    expect(logContent).toContain('0'); // number converted to string
    expect(logContent).toContain('false'); // boolean converted to string
    expect(logContent).toContain('[]'); // empty array as JSON
    expect(logContent).toContain('{}'); // empty object as JSON
    expect(logContent).toContain('"empty": null'); // object with null value

    // Should not contain [object Object]
    expect(logContent).not.toContain('[object Object]');
  });
});
