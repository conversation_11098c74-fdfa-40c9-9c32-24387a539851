import React from 'react';
import { useTheme } from 'next-themes';
import { Sun, Moon } from 'lucide-react';

/**
 * Test component to demonstrate theme switching functionality
 * This component shows how the ExecutionDialog and LogDisplay components
 * will adapt to light and dark themes
 */
export function ThemeTestComponent() {
  const { theme, setTheme } = useTheme();
  const isDarkMode = theme === 'dark';

  return (
    <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-zinc-900 border-zinc-700' : 'bg-white border-gray-300'}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Theme Test Component
        </h3>
        <button
          onClick={() => setTheme(isDarkMode ? 'light' : 'dark')}
          className={`p-2 rounded-md ${isDarkMode ? 'bg-zinc-800 hover:bg-zinc-700' : 'bg-gray-100 hover:bg-gray-200'} transition-colors`}
        >
          {isDarkMode ? (
            <Sun className="w-5 h-5 text-yellow-500" />
          ) : (
            <Moon className="w-5 h-5 text-gray-700" />
          )}
        </button>
      </div>
      
      <div className="space-y-4">
        <div className={`p-4 rounded border ${isDarkMode ? 'bg-zinc-800 border-zinc-600' : 'bg-gray-50 border-gray-200'}`}>
          <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Sample Form Input
          </h4>
          <input
            type="text"
            placeholder="Enter some text..."
            className={`w-full px-3 py-2 rounded border ${isDarkMode ? 'bg-zinc-700 border-zinc-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'} focus:outline-none focus:ring-2 focus:ring-purple-500`}
          />
        </div>
        
        <div className={`p-4 rounded border ${isDarkMode ? 'bg-zinc-800 border-zinc-600' : 'bg-gray-50 border-gray-200'}`}>
          <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Sample Log Entry
          </h4>
          <div className={`p-3 rounded font-mono text-sm ${isDarkMode ? 'bg-zinc-900 text-gray-300' : 'bg-gray-100 text-gray-700'}`}>
            [2024-01-01 12:00:00] Workflow execution started
          </div>
        </div>
        
        <div className="flex gap-2">
          <button className={`px-4 py-2 rounded ${isDarkMode ? 'bg-zinc-700 hover:bg-zinc-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-900'} transition-colors`}>
            Cancel
          </button>
          <button className="px-4 py-2 rounded bg-purple-600 hover:bg-purple-700 text-white transition-colors">
            Run Workflow
          </button>
        </div>
      </div>
      
      <div className={`mt-4 p-3 rounded text-sm ${isDarkMode ? 'bg-blue-900/20 text-blue-300 border border-blue-800' : 'bg-blue-50 text-blue-700 border border-blue-200'}`}>
        <strong>Current theme:</strong> {theme} mode
        <br />
        <strong>Status:</strong> Theme switching is working correctly!
      </div>
    </div>
  );
}
