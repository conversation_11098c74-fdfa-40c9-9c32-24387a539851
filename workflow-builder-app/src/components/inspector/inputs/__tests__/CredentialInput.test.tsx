/**
 * Tests for CredentialInput component
 * Following TDD methodology
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CredentialInput } from '../CredentialInput';
import { fetchCredentials } from '../../../../lib/api';

// Mock the API functions
jest.mock('../../../../lib/api');
const mockFetchCredentials = fetchCredentials as jest.MockedFunction<typeof fetchCredentials>;

// Mock CredentialSelector to avoid Radix UI test issues
jest.mock('../../../credentials/CredentialSelector', () => {
  return function MockCredentialSelector({ 
    value, 
    onSelect, 
    placeholder, 
    disabled 
  }: any) {
    return (
      <div data-testid="credential-selector">
        <select
          value={value || ''}
          onChange={(e) => onSelect(e.target.value)}
          disabled={disabled}
          data-placeholder={placeholder}
        >
          <option value="">{placeholder}</option>
          <option value="cred-1">OpenAI API Key</option>
          <option value="cred-2">Anthropic Key</option>
        </select>
      </div>
    );
  };
});

describe('CredentialInput', () => {
  const mockInputDef = {
    name: 'api_key',
    display_name: 'API Key',
    input_type: 'credential' as const,
    required: true,
    info: 'API key for the service',
  };

  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockFetchCredentials.mockResolvedValue({ credentials: [] });
  });

  describe('Basic Rendering', () => {
    it('should render with toggle switch', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={null}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      expect(screen.getByText('Use credential from secure storage')).toBeInTheDocument();
      expect(screen.getByRole('switch')).toBeInTheDocument();
    });

    it('should start in direct input mode by default', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={null}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      const toggle = screen.getByRole('switch');
      expect(toggle).not.toBeChecked();
      expect(screen.getByPlaceholderText('Enter API Key...')).toBeInTheDocument();
    });

    it('should show credential selector when toggle is enabled', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: true }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      const toggle = screen.getByRole('switch');
      expect(toggle).toBeChecked();
      expect(screen.getByTestId('credential-selector')).toBeInTheDocument();
    });
  });

  describe('Direct Input Mode', () => {
    it('should handle direct value input', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: false, value: 'test-key' }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      const input = screen.getByDisplayValue('test-key');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'password');
    });

    it('should call onChange when direct value changes', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: false, value: '' }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      const input = screen.getByPlaceholderText('Enter API Key...');
      fireEvent.change(input, { target: { value: 'new-key' } });

      expect(mockOnChange).toHaveBeenCalledWith('api_key', {
        use_credential_id: false,
        value: 'new-key',
      });
    });

    it('should show security warning in direct input mode', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: false }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      expect(screen.getByText(/Entering credentials directly is less secure/)).toBeInTheDocument();
    });
  });

  describe('Credential Selection Mode', () => {
    it('should handle credential selection', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: true }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      const selector = screen.getByTestId('credential-selector').querySelector('select');
      fireEvent.change(selector!, { target: { value: 'cred-1' } });

      expect(mockOnChange).toHaveBeenCalledWith('api_key', {
        use_credential_id: true,
        credential_id: 'cred-1',
      });
    });

    it('should show selected credential confirmation', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: true, credential_id: 'cred-1' }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      expect(screen.getByText(/Credential selected. The secure value will be used/)).toBeInTheDocument();
    });

    it('should show help text for credential mode', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: true }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      expect(screen.getByText(/Credentials are stored securely/)).toBeInTheDocument();
      expect(screen.getByText(/Credential Manager/)).toBeInTheDocument();
    });
  });

  describe('Mode Switching', () => {
    it('should switch from direct input to credential selection', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: false, value: 'test-key' }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      const toggle = screen.getByRole('switch');
      fireEvent.click(toggle);

      expect(mockOnChange).toHaveBeenCalledWith('api_key', {
        use_credential_id: true,
        value: '', // Should clear direct value
      });
    });

    it('should switch from credential selection to direct input', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: true, credential_id: 'cred-1' }}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      const toggle = screen.getByRole('switch');
      fireEvent.click(toggle);

      expect(mockOnChange).toHaveBeenCalledWith('api_key', {
        use_credential_id: false,
        credential_id: '', // Should clear credential ID
      });
    });
  });

  describe('Disabled State', () => {
    it('should disable all inputs when disabled', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: false }}
          onChange={mockOnChange}
          isDisabled={true}
          nodeId="test-node"
        />
      );

      const toggle = screen.getByRole('switch');
      const input = screen.getByPlaceholderText('Enter API Key...');

      expect(toggle).toBeDisabled();
      expect(input).toBeDisabled();
    });

    it('should disable credential selector when disabled', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: true }}
          onChange={mockOnChange}
          isDisabled={true}
          nodeId="test-node"
        />
      );

      const selector = screen.getByTestId('credential-selector').querySelector('select');
      expect(selector).toBeDisabled();
    });
  });

  describe('Info Display', () => {
    it('should display input definition info', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={null}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      expect(screen.getByText('API key for the service')).toBeInTheDocument();
    });

    it('should work without info field', () => {
      const inputDefWithoutInfo = { ...mockInputDef };
      delete (inputDefWithoutInfo as any).info;

      render(
        <CredentialInput
          inputDef={inputDefWithoutInfo}
          value={null}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      // Should render without errors
      expect(screen.getByText('Use credential from secure storage')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle null value gracefully', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={null}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      // Should default to direct input mode
      const toggle = screen.getByRole('switch');
      expect(toggle).not.toBeChecked();
    });

    it('should handle undefined value gracefully', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={undefined}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      // Should default to direct input mode
      const toggle = screen.getByRole('switch');
      expect(toggle).not.toBeChecked();
    });

    it('should handle partial value objects', () => {
      render(
        <CredentialInput
          inputDef={mockInputDef}
          value={{ use_credential_id: true } as any}
          onChange={mockOnChange}
          nodeId="test-node"
        />
      );

      // Should work with missing fields
      expect(screen.getByTestId('credential-selector')).toBeInTheDocument();
    });
  });
});
