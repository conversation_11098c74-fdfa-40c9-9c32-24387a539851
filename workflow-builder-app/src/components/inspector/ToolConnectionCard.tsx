/**
 * Tool Connection Card component for displaying connected tool information
 */

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

export interface ToolConnection {
  nodeId: string;
  handleId: string;
  componentType: string;
  label: string;
}

interface ToolConnectionCardProps {
  toolConnection: ToolConnection;
  onRemove?: (handleId: string) => void;
  isLoading?: boolean;
  hasError?: boolean;
  isDisabled?: boolean;
}

/**
 * Gets the appropriate icon for a component type
 */
function getComponentIcon(componentType: string): string {
  const iconMap: Record<string, string> = {
    DataProcessor: "🔄",
    TextAnalyzer: "📝",
    MCPMarketplace: "🔌",
    FileReader: "📁",
    FileWriter: "💾",
    ApiRequest: "🌐",
    Conditional: "🔀",
    Loop: "🔁",
    AgenticAI: "🤖",
  };

  return iconMap[componentType] || "🔧"; // Default tool icon
}

/**
 * Checks if a component is an MCP marketplace component
 */
function isMCPComponent(componentType: string): boolean {
  return componentType === "MCPMarketplace";
}

/**
 * Tool Connection Card component
 */
export function ToolConnectionCard({
  toolConnection,
  onRemove,
  isLoading = false,
  hasError = false,
  isDisabled = false,
}: ToolConnectionCardProps) {
  const { nodeId, handleId, componentType, label } = toolConnection;
  const icon = getComponentIcon(componentType);
  const isMCP = isMCPComponent(componentType);

  const handleRemoveClick = () => {
    if (!isDisabled && !isLoading && onRemove) {
      onRemove(handleId);
    }
  };

  return (
    <article
      role="article"
      className={cn(
        "flex items-start gap-3 rounded-lg border border-gray-200 bg-white p-3 shadow-sm transition-all hover:shadow-md dark:border-gray-700 dark:bg-gray-800",
        isDisabled && "opacity-50",
        hasError && "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950"
      )}
    >
      {/* Component Icon */}
      <div className="flex-shrink-0">
        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-400">
          <span className="text-sm">{icon}</span>
        </div>
      </div>

      {/* Tool Information */}
      <div className="flex-grow min-w-0">
        <div className="flex items-start justify-between gap-2">
          <div className="min-w-0 flex-grow">
            {/* Tool Label */}
            <h4 className="truncate text-sm font-medium text-gray-900 dark:text-gray-100">
              {label}
            </h4>
            
            {/* Component Type and Handle ID */}
            <div className="mt-1 flex items-center gap-2">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {componentType}
              </span>
              <span className="font-mono text-xs text-gray-400 dark:text-gray-500">
                {handleId}
              </span>
            </div>

            {/* Badges */}
            <div className="mt-2 flex items-center gap-2">
              {/* MCP Badge */}
              {isMCP && (
                <Badge variant="secondary" className="text-xs">
                  MCP
                </Badge>
              )}

              {/* Connection Status */}
              <div className="flex items-center gap-1">
                <div
                  data-testid="connection-status"
                  className={cn(
                    "h-2 w-2 rounded-full",
                    hasError ? "bg-red-500" : "bg-green-500"
                  )}
                  aria-label="Tool connection status"
                />
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {hasError ? "Error" : "Connected"}
                </span>
              </div>
            </div>
          </div>

          {/* Remove Button - only show if onRemove is provided */}
          {onRemove && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRemoveClick}
              disabled={isDisabled || isLoading}
              className="h-6 w-6 p-0 text-gray-400 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-400"
              aria-label="Remove tool connection"
              tabIndex={0}
            >
              {isLoading ? (
                <Loader2
                  className="h-3 w-3 animate-spin"
                  data-testid="loading-spinner"
                />
              ) : (
                <X className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </div>
    </article>
  );
}
