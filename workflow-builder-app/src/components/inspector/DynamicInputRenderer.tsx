import React from "react";
import { InputDefinition } from "@/types";
import { DynamicInputTypeMapper, JsonSchemaProperty } from "@/lib/dynamicInputTypeMapper";

// Import all available input components
import {
  StringInput,
  NumberInput,
  BooleanInput,
  ObjectInput,
  ArrayInput,
  SelectInput,
  HandleInput,
  CredentialInput,
} from "./inputs";
import { DynamicInput } from "@/components/ui/dynamic-input";
import { ValidationWrapper } from "./ValidationWrapper";
import { Input } from "@/components/ui/input-credential";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { ConnectedIndicator } from "../ui/connected-indicator";
import { cn } from "@/lib/utils";

interface DynamicInputRendererProps {
  inputDef: InputDefinition;
  value: any;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  connectionInfo?: any;
  nodeId?: string;
}

/**
 * Component registry mapping component names to actual React components
 */
const COMPONENT_REGISTRY = {
  StringInput,
  NumberInput,
  BooleanInput,
  ObjectInput,
  ArrayInput,
  SelectInput,
  HandleInput,
  CredentialInput,
  DynamicInput,
} as const;

/**
 * Dynamic input renderer that can handle any input type using the mapping system
 */
export function DynamicInputRenderer({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  connectionInfo,
  nodeId,
}: DynamicInputRendererProps) {
  // Try to get mapping for the input type
  const mapping = React.useMemo(() => {
    try {
      // Create a schema object from the input definition
      const schema: JsonSchemaProperty = {
        type: getBaseType(inputDef.input_type),
        format: getFormat(inputDef.input_type),
        enum: inputDef.options?.map(opt => typeof opt === 'string' ? opt : opt.value),
        minimum: inputDef.min_value,
        maximum: inputDef.max_value,
        minLength: inputDef.min_length,
        maxLength: inputDef.max_length,
        pattern: inputDef.pattern,
        properties: inputDef.properties,
        description: inputDef.info || undefined,
        title: inputDef.display_name,
      };

      const result = DynamicInputTypeMapper.getMapping(schema);

      return result;
    } catch (error) {
      console.warn(`Failed to get mapping for input type ${inputDef.input_type}:`, error);

      return DynamicInputTypeMapper.getMapping({ type: 'string' });
    }
  }, [inputDef]);

  // Get the component to render
  const ComponentToRender = COMPONENT_REGISTRY[mapping.component as keyof typeof COMPONENT_REGISTRY];



  if (!ComponentToRender) {
    console.warn(`Component ${mapping.component} not found in registry. Falling back to StringInput.`);
    return (
      <StringInput
        inputDef={{
          ...inputDef,
          input_type: 'string',
          info: `${inputDef.info || ''} (Note: Component '${mapping.component}' not available, using text input)`
        }}
        value={value}
        onChange={onChange}
        isDisabled={isDisabled}
        isConnected={isConnected}
        nodeId={nodeId}
      />
    );
  }

  // Create enhanced input definition with mapping properties
  const enhancedInputDef: InputDefinition = {
    ...inputDef,
    input_type: mapping.inputType,
    // Apply validation from mapping
    min_value: mapping.validation?.min ?? inputDef.min_value,
    max_value: mapping.validation?.max ?? inputDef.max_value,
    min_length: mapping.validation?.minLength ?? inputDef.min_length,
    max_length: mapping.validation?.maxLength ?? inputDef.max_length,
    pattern: mapping.validation?.pattern ?? inputDef.pattern,
    // Apply options from mapping (for dropdowns)
    options: mapping.props?.options ?? inputDef.options,
  };

  // Special handling for specific component types
  if (mapping.component === 'HandleInput') {
    return (
      <HandleInput
        inputDef={enhancedInputDef}
        value={value}
        onChange={onChange}
        isDisabled={isDisabled}
        isConnected={isConnected}
        connectionInfo={connectionInfo}
        nodeId={nodeId}
      />
    );
  }

  if (mapping.component === 'DynamicInput') {
    return (
      <DynamicInput
        inputDef={enhancedInputDef}
        currentValue={value}
        onChange={(name, value) => onChange(name, value)}
        isDisabled={isDisabled}
        isConnected={isConnected}
        minInputs={inputDef.min_handles || 0}
        maxInputs={inputDef.max_handles || 10}
        defaultInputs={inputDef.default_handles || 1}
      />
    );
  }

  // For all other components, use standard props
  const commonProps = {
    inputDef: enhancedInputDef,
    value,
    onChange,
    isDisabled,
    isConnected,
    nodeId,
  };

  return <ComponentToRender {...(commonProps as any)} />;
}

/**
 * Extract base type from input_type string
 */
function getBaseType(inputType: string): string {
  // Handle compound types like "string:uri", "string:email", etc.
  if (inputType.includes(':')) {
    return inputType.split(':')[0];
  }
  
  // Map common variations to standard types
  const typeMap: Record<string, string> = {
    'int': 'integer',
    'float': 'number',
    'bool': 'boolean',
    'dict': 'object',
    'json': 'object',
    'list': 'array',
    'url': 'string',
    'email': 'string',
    'password': 'string',
    'multiline': 'string',
    'code': 'string',
    'dropdown': 'string',
    'select': 'string',
  };
  
  return typeMap[inputType] || inputType;
}

/**
 * Extract format from input_type string
 */
function getFormat(inputType: string): string | undefined {
  // Handle compound types like "string:uri", "string:email", etc.
  if (inputType.includes(':')) {
    return inputType.split(':')[1];
  }
  
  // Map input types to formats
  const formatMap: Record<string, string> = {
    'url': 'uri',
    'email': 'email',
    'password': 'password',
    'date': 'date',
    'time': 'time',
    'datetime': 'datetime',
  };
  
  return formatMap[inputType];
}

/**
 * Fallback renderer for completely unknown input types
 * SAFETY: Guaranteed to work for any input type
 */
export function FallbackInputRenderer({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: DynamicInputRendererProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;

  console.warn(`[DYNAMIC INPUT SYSTEM] Using fallback renderer for input type: ${inputDef.input_type}`);

  // Determine the best fallback input type based on the original type
  const getFallbackInputType = (inputType: string): string => {
    const lowerType = inputType.toLowerCase();

    if (lowerType.includes('number') || lowerType.includes('int') || lowerType.includes('float')) {
      return 'number';
    }
    if (lowerType.includes('bool') || lowerType.includes('toggle') || lowerType.includes('switch')) {
      return 'checkbox';
    }
    if (lowerType.includes('email')) {
      return 'email';
    }
    if (lowerType.includes('url') || lowerType.includes('link')) {
      return 'url';
    }
    if (lowerType.includes('password') || lowerType.includes('secret')) {
      return 'password';
    }
    if (lowerType.includes('date')) {
      return 'date';
    }
    if (lowerType.includes('time')) {
      return 'time';
    }
    if (lowerType.includes('color')) {
      return 'color';
    }
    if (lowerType.includes('file')) {
      return 'file';
    }

    // Default to text
    return 'text';
  };

  const fallbackType = getFallbackInputType(inputDef.input_type);

  // Handle boolean/checkbox types specially
  if (fallbackType === 'checkbox') {
    return (
      <ValidationWrapper inputDef={inputDef} value={value}>
        <div className="relative">
          <div className="flex items-center space-x-2 mt-1">
            <input
              id={inputId}
              type="checkbox"
              checked={!!value}
              onChange={(e) => onChange(inputDef.name, e.target.checked)}
              disabled={isDisabled}
              className={cn("h-4 w-4", isDisabled && "opacity-50")}
            />
            <label htmlFor={inputId} className="text-sm">
              {inputDef.display_name}
            </label>
          </div>
          {isDisabled && isConnected && <ConnectedIndicator />}
          <div className="mt-1 text-xs text-yellow-600">
            ⚠️ Input type '{inputDef.input_type}' mapped to checkbox
          </div>
        </div>
      </ValidationWrapper>
    );
  }

  // Handle textarea for long text types
  if (inputDef.input_type.includes('multiline') || inputDef.input_type.includes('textarea') || inputDef.input_type.includes('long')) {
    return (
      <ValidationWrapper inputDef={inputDef} value={value}>
        <div className="relative">
          <Textarea
            id={inputId}
            value={value ?? ""}
            onChange={(e) => onChange(inputDef.name, e.target.value)}
            placeholder={`${inputDef.display_name} (${inputDef.input_type})`}
            className={cn(
              "bg-background/50 mt-1 text-xs",
              isDisabled && "opacity-50"
            )}
            disabled={isDisabled}
            rows={3}
          />
          {isDisabled && isConnected && <ConnectedIndicator />}
          <div className="mt-1 text-xs text-yellow-600">
            ⚠️ Input type '{inputDef.input_type}' mapped to textarea
          </div>
        </div>
      </ValidationWrapper>
    );
  }

  // Default input fallback
  return (
    <ValidationWrapper inputDef={inputDef} value={value}>
      <div className="relative">
        <Input
          id={inputId}
          type={fallbackType}
          value={value ?? ""}
          onChange={(e) => {
            const newValue = fallbackType === 'number' ?
              (e.target.value === '' ? '' : Number(e.target.value)) :
              e.target.value;
            onChange(inputDef.name, newValue);
          }}
          placeholder={`${inputDef.display_name} (${inputDef.input_type})`}
          className={cn(
            "bg-background/50 mt-1 h-8 text-xs",
            isDisabled && "opacity-50"
          )}
          disabled={isDisabled}
          min={inputDef.min_value}
          max={inputDef.max_value}
          minLength={inputDef.min_length}
          maxLength={inputDef.max_length}
          pattern={inputDef.pattern}
          required={inputDef.required}
        />
        {isDisabled && isConnected && <ConnectedIndicator />}
        <div className="mt-1 text-xs text-yellow-600">
          ⚠️ Input type '{inputDef.input_type}' mapped to {fallbackType} input
        </div>
      </div>
    </ValidationWrapper>
  );
}

/**
 * Enhanced input renderer that combines dynamic mapping with fallback support
 * SAFETY: Multiple layers of error handling to prevent breaking existing functionality
 */
export function EnhancedInputRenderer(props: DynamicInputRendererProps) {
  try {
    return <DynamicInputRenderer {...props} />;
  } catch (error) {
    console.error(`[DYNAMIC INPUT SYSTEM] Error rendering input ${props.inputDef.name}:`, error);

    // Log error for monitoring
    if (typeof window !== 'undefined') {
      try {
        const errorLog = {
          timestamp: new Date().toISOString(),
          inputName: props.inputDef.name,
          inputType: props.inputDef.input_type,
          nodeId: props.nodeId,
          error: String(error),
          stack: error instanceof Error ? error.stack : undefined
        };

        // Store in localStorage for debugging (with size limit)
        const existingLogs = JSON.parse(localStorage.getItem('dynamicInputErrors') || '[]');
        existingLogs.push(errorLog);

        // Keep only last 50 errors to prevent storage bloat
        if (existingLogs.length > 50) {
          existingLogs.splice(0, existingLogs.length - 50);
        }

        localStorage.setItem('dynamicInputErrors', JSON.stringify(existingLogs));
      } catch (logError) {
        console.warn('Failed to log dynamic input error:', logError);
      }
    }

    // SAFETY: Always fall back to a working renderer
    return <FallbackInputRenderer {...props} />;
  }
}
