import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { NodeSettingsPanel } from '../NodeSettingsPanel';

// Mock UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }) => (
    React.createElement('button', { onClick, ...props }, children)
  ),
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant, ...props }) => (
    React.createElement('span', { className: `badge ${variant}`, ...props }, children)
  ),
}));

jest.mock('@/components/ui/scroll-area', () => ({
  ScrollArea: ({ children, ...props }) => React.createElement('div', props, children),
}));

jest.mock('@/components/ui/label', () => ({
  Label: ({ children, ...props }) => React.createElement('label', props, children),
}));

jest.mock('@/components/ui/input', () => ({
  Input: (props) => React.createElement('input', props),
}));

jest.mock('@/components/ui/separator', () => ({
  Separator: () => React.createElement('hr'),
}));

// Mock other inspector components
jest.mock('../ApiNodeSettings', () => ({
  ApiNodeSettings: () => React.createElement('div', {}, 'API Node Settings'),
}));

jest.mock('../DefaultNodeSettings', () => ({
  DefaultNodeSettings: () => React.createElement('div', {}, 'Default Node Settings'),
}));

describe('NodeSettingsPanel Simple Test', () => {
  const createMockAgenticAINode = () => ({
    id: 'agentic-ai-1',
    type: 'workflow',
    position: { x: 0, y: 0 },
    data: {
      label: 'AI Agent Executor',
      definition: {
        name: 'AgenticAI',
        display_name: 'AI Agent Executor',
        description: 'Executes an AI agent with tools',
        category: 'AI',
        icon: 'Bot',
        inputs: [
          {
            name: 'workflow_components',
            display_name: 'Workflow Components (Tools)',
            input_type: 'dynamic_handle',
            required: false,
            is_handle: true,
            input_types: ['Any'],
            info: 'Connect workflow components as tools',
            min_handles: 0,
            max_handles: 10,
            default_handles: 0,
            base_name: 'tool',
            base_display_name: 'Tool'
          }
        ],
        outputs: []
      },
      type: 'AgenticAI',
      originalType: 'AgenticAI',
      config: { num_handles: 0 }
    }
  });

  const mockProps = {
    onLabelChange: jest.fn(),
    onConfigChange: jest.fn(),
    isInputConnected: jest.fn(() => false),
    shouldDisableInput: jest.fn(() => false),
    getConnectionInfo: jest.fn(() => ({ isConnected: false })),
    onNodeDataChange: jest.fn(),
    edges: [],
    nodes: [],
    onAddToolSlot: jest.fn(),
    onRemoveToolSlot: jest.fn()
  };

  test('renders tool management section for AgenticAI', () => {
    const agenticAINode = createMockAgenticAINode();
    
    render(
      React.createElement(NodeSettingsPanel, {
        node: agenticAINode,
        ...mockProps
      })
    );
    
    // Should render the tool management section
    expect(screen.getByText('🔧 Agent Tools')).toBeInTheDocument();
    expect(screen.getByText('Connected Components')).toBeInTheDocument();
    expect(screen.getByText('+ Add Tool Slot')).toBeInTheDocument();
  });

  test('renders basic node label input', () => {
    const agenticAINode = createMockAgenticAINode();
    
    render(
      React.createElement(NodeSettingsPanel, {
        node: agenticAINode,
        ...mockProps
      })
    );
    
    // Should render node label
    expect(screen.getByText('Node Label')).toBeInTheDocument();
    expect(screen.getByDisplayValue('AI Agent Executor')).toBeInTheDocument();
  });
});
