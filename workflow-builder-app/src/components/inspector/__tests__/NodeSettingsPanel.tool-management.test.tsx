import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import { NodeSettingsPanel } from '../NodeSettingsPanel';

// Mock UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }) => (
    React.createElement('button', { onClick, ...props }, children)
  ),
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant, ...props }) => (
    React.createElement('span', { className: `badge ${variant}`, ...props }, children)
  ),
}));

describe('NodeSettingsPanel Tool Management', () => {
  const createMockAgenticAINode = () => ({
    id: 'agentic-ai-1',
    type: 'workflow',
    position: { x: 0, y: 0 },
    data: {
      label: 'AI Agent Executor',
      definition: {
        name: 'AgenticAI',
        display_name: 'AI Agent Executor',
        description: 'Executes an AI agent with tools',
        category: 'AI',
        icon: 'Bot',
        inputs: [
          {
            name: 'workflow_components',
            display_name: 'Workflow Components (Tools)',
            input_type: 'dynamic_handle',
            required: false,
            is_handle: true,
            input_types: ['Any'],
            info: 'Connect workflow components as tools',
            min_handles: 0,
            max_handles: 10,
            default_handles: 0,
            base_name: 'tool',
            base_display_name: 'Tool'
          }
        ],
        outputs: []
      },
      type: 'AgenticAI',
      originalType: 'AgenticAI',
      config: { num_handles: 0 }
    }
  });

  const mockOnNodeDataChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders tool management section for AgenticAI', () => {
    const agenticAINode = createMockAgenticAINode();
    render(
      React.createElement(NodeSettingsPanel, {
        node: agenticAINode,
        onLabelChange: jest.fn(),
        onConfigChange: jest.fn(),
        isInputConnected: jest.fn(() => false),
        shouldDisableInput: jest.fn(() => false),
        getConnectionInfo: jest.fn(() => ({ isConnected: false })),
        edges: [],
        nodes: [],
        onAddToolSlot: jest.fn(),
        onRemoveToolSlot: jest.fn()
      })
    );

    // Should render the tool management section
    expect(screen.queryByText(/Agent Tools/)).toBeInTheDocument();
  });

  test('tool management performance requirement', () => {
    const startTime = performance.now();

    const agenticAINode = createMockAgenticAINode();
    render(
      React.createElement(NodeSettingsPanel, {
        node: agenticAINode,
        onLabelChange: jest.fn(),
        onConfigChange: jest.fn(),
        isInputConnected: jest.fn(() => false),
        shouldDisableInput: jest.fn(() => false),
        getConnectionInfo: jest.fn(() => ({ isConnected: false })),
        edges: [],
        nodes: [],
        onAddToolSlot: jest.fn(),
        onRemoveToolSlot: jest.fn()
      })
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render in less than 100ms
    expect(renderTime).toBeLessThan(100);
  });
});
