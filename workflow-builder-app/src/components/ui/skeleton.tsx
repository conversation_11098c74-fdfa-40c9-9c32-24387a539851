import * as React from "react"
import { cn } from "@/lib/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-gray-200 dark:bg-gray-700 dark:bg-gray-800",
        className
      )}
      {...props}
    />
  )
}

function SkeletonText({
  className,
  lines = 1,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & { lines?: number }) {
  return (
    <div className={cn("space-y-2", className)} {...props}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          className={cn(
            "h-4",
            i === lines - 1 && lines > 1 ? "w-3/4" : "w-full"
          )}
        />
      ))}
    </div>
  )
}

function SkeletonCard({
  className,
  showIcon = true,
  showTitle = true,
  showDescription = true,
  descriptionLines = 2,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  showIcon?: boolean
  showTitle?: boolean
  showDescription?: boolean
  descriptionLines?: number
}) {
  return (
    <div
      className={cn(
        "rounded-xl bg-gray-100 dark:bg-gray-800 dark:bg-[#1A1A1A] p-4 space-y-3 border border-transparent dark:border-gray-800",
        className
      )}
      {...props}
    >
      <div className="flex items-start gap-2.5">
        {showIcon && (
          <div className="p-1 rounded bg-[#F4F4F5] dark:bg-[#2E2E2E] flex-shrink-0 mt-0.5">
            <Skeleton className="h-4 w-4" />
          </div>
        )}
        {showTitle && (
          <Skeleton className="h-4 flex-1" />
        )}
        <Skeleton className="h-4 w-4 flex-shrink-0" />
      </div>
      {showDescription && (
        <div className="space-y-1">
          {Array.from({ length: descriptionLines }).map((_, i) => (
            <Skeleton
              key={i}
              className={cn(
                "h-3",
                i === descriptionLines - 1 ? "w-2/3" : "w-full"
              )}
            />
          ))}
        </div>
      )}
    </div>
  )
}

function SkeletonMCPGroup({
  className,
  toolCount = 3,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  toolCount?: number
}) {
  return (
    <div
      className={cn(
        "rounded-lg bg-gray-100 dark:bg-gray-800 dark:bg-black p-3 space-y-3",
        className
      )}
      {...props}
    >
      {/* MCP Group Header */}
      <div className="flex items-center gap-2.5 p-2 border-b border-gray-200/60 dark:border-gray-700/60 pb-2">
        <Skeleton className="h-8 w-8 rounded-md bg-gray-300 dark:bg-gray-600" />
        <Skeleton className="h-4 w-32 bg-gray-300 dark:bg-gray-600" />
        <Skeleton className="h-3 w-3 ml-auto bg-gray-300 dark:bg-gray-600" />
      </div>
      
      {/* MCP Tools */}
      <div className="space-y-3">
        {Array.from({ length: toolCount }).map((_, i) => (
          <SkeletonCard
            key={i}
            className="bg-white dark:bg-[#1A1A1A] border border-transparent dark:border-gray-800"
            descriptionLines={Math.random() > 0.5 ? 1 : 2}
          />
        ))}
      </div>
    </div>
  )
}

function SkeletonCategoryHeader({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "flex items-center gap-2 p-4",
        className
      )}
      {...props}
    >
      <div className="p-1.5 rounded-md bg-gray-200 dark:bg-gray-700">
        <Skeleton className="h-6 w-6" />
      </div>
      <Skeleton className="h-5 w-24" />
      <Skeleton className="h-4 w-4 ml-auto" />
    </div>
  )
}

function SkeletonSidebarContent({
  className,
  categoryCount = 3,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  categoryCount?: number
}) {
  return (
    <div className={cn("space-y-1 px-4 py-4", className)} {...props}>
      {Array.from({ length: categoryCount }).map((_, i) => (
        <div
          key={i}
          className="overflow-hidden rounded-lg bg-gray-50 dark:bg-gray-900"
        >
          <SkeletonCategoryHeader />
          <div className="px-3 pb-4">
            <div className="space-y-4 pt-3">
              <SkeletonMCPGroup toolCount={Math.floor(Math.random() * 3) + 2} />
              {Math.random() > 0.5 && (
                <SkeletonMCPGroup toolCount={Math.floor(Math.random() * 2) + 1} />
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

function SkeletonSidebar({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <aside
      className={cn(
        "flex h-full w-80 flex-col border-r border-gray-200 bg-white dark:border-gray-800 dark:bg-[#000000]",
        className
      )}
      {...props}
    >
      {/* Header with search bar skeleton */}
      <div className="border-b border-gray-200 dark:border-gray-800 p-3">
        <div className="flex items-center justify-between">
          <div className="flex-1 relative">
            <Skeleton className="h-10 w-full rounded-lg bg-gray-200 dark:bg-gray-700" />
            <Skeleton className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 rounded bg-gray-300 dark:bg-gray-600" />
            <Skeleton className="absolute right-2 top-1/2 h-5 w-6 -translate-y-1/2 rounded bg-gray-300 dark:bg-gray-600" />
          </div>
          <Skeleton className="h-8 w-8 rounded-md bg-gray-300 dark:bg-gray-600 ml-2" />
        </div>
      </div>

      {/* Categories skeleton */}
      <div className="flex-1 overflow-hidden">
        <div className="space-y-1 p-4">
          {/* AI Category - Collapsed */}
          <div className="overflow-hidden rounded-lg bg-gray-50 dark:bg-[#2E2E2E]">
            <div className="flex items-center justify-between p-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5 rounded bg-gray-300 dark:bg-gray-600" />
                <Skeleton className="h-4 w-16 bg-gray-300 dark:bg-gray-600" />
              </div>
              <Skeleton className="h-4 w-4 rounded bg-gray-300 dark:bg-gray-600" />
            </div>
          </div>

          {/* MCP Marketplace Category - Collapsed */}
          <div className="overflow-hidden rounded-lg bg-gray-50 dark:bg-[#2E2E2E]">
            <div className="flex items-center justify-between p-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5 rounded bg-gray-300 dark:bg-gray-600" />
                <Skeleton className="h-4 w-24 bg-gray-300 dark:bg-gray-600" />
              </div>
              <Skeleton className="h-4 w-4 rounded bg-gray-300 dark:bg-gray-600" />
            </div>
          </div>

          {/* Agents Category - Collapsed */}
          <div className="overflow-hidden rounded-lg bg-gray-50 dark:bg-[#2E2E2E]">
            <div className="flex items-center justify-between p-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5 rounded bg-gray-300 dark:bg-gray-600" />
                <Skeleton className="h-4 w-16 bg-gray-300 dark:bg-gray-600" />
              </div>
              <Skeleton className="h-4 w-4 rounded bg-gray-300 dark:bg-gray-600" />
            </div>
          </div>

          {/* Data Category - Collapsed */}
          <div className="overflow-hidden rounded-lg bg-gray-50 dark:bg-[#2E2E2E]">
            <div className="flex items-center justify-between p-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5 rounded bg-gray-300 dark:bg-gray-600" />
                <Skeleton className="h-4 w-12 bg-gray-300 dark:bg-gray-600" />
              </div>
              <Skeleton className="h-4 w-4 rounded bg-gray-300 dark:bg-gray-600" />
            </div>
          </div>

          {/* Additional MCP Categories - Collapsed */}
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="overflow-hidden rounded-lg bg-gray-50 dark:bg-[#2E2E2E]">
              <div className="flex items-center justify-between p-3">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-5 rounded bg-gray-300 dark:bg-gray-600" />
                  <Skeleton className="h-4 w-20 bg-gray-300 dark:bg-gray-600" />
                </div>
                <Skeleton className="h-4 w-4 rounded bg-gray-300 dark:bg-gray-600" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* User account section skeleton */}
      <div className="border-t border-gray-200 dark:border-gray-800 p-4">
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-xl bg-gray-300 dark:bg-gray-600" />
          <Skeleton className="h-4 flex-1 bg-gray-300 dark:bg-gray-600" />
          <Skeleton className="h-6 w-6 rounded bg-gray-300 dark:bg-gray-600" />
        </div>
      </div>
    </aside>
  )
}

export { 
  Skeleton, 
  SkeletonText, 
  SkeletonCard, 
  SkeletonMCPGroup, 
  SkeletonCategoryHeader,
  SkeletonSidebarContent,
  SkeletonSidebar
}