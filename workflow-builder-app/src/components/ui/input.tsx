import * as React from "react";

import { cn } from "@/lib/utils";

type InputProps = React.ComponentProps<"textarea"> & {
  type?: string;
};

function Input({ className, type, ...props }: InputProps) {
  return (
    <textarea
      rows={1}
      data-slot="input"
      className={cn(
        "overflow-hidden resize-none break-words whitespace-pre-wrap file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent px-3 py-2.5 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        "leading-tight items-center justify-center",
        className,
      )}
      {...props}
    ></textarea>
  );
}

export { Input };

