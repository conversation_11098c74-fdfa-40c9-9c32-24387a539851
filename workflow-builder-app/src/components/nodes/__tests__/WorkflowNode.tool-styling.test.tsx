/**
 * Tests for WorkflowNode tool styling functionality
 */

import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { Edge, Node, ReactFlowProvider, NodeProps } from "reactflow";
import WorkflowNode from "../WorkflowNode";
import { WorkflowNodeData } from "@/types";

// Mock the tool connection utilities and hooks
jest.mock("@/utils/toolConnectionUtils", () => ({
  generateToolStyleClasses: jest.fn(),
  isToolHandle: jest.fn(),
}));

jest.mock("@/hooks/useToolConnections", () => ({
  useAgenticAIToolConnections: jest.fn(),
  useIsConnectedAsTool: jest.fn(),
}));

import { generateToolStyleClasses, isToolHandle } from "@/utils/toolConnectionUtils";
import { useAgenticAIToolConnections, useIsConnectedAsTool } from "@/hooks/useToolConnections";

const mockGenerateToolStyleClasses = generateToolStyleClasses as jest.MockedFunction<
  typeof generateToolStyleClasses
>;
const mockIsToolHandle = isToolHandle as jest.MockedFunction<typeof isToolHandle>;
const mockUseAgenticAIToolConnections = useAgenticAIToolConnections as jest.MockedFunction<
  typeof useAgenticAIToolConnections
>;
const mockUseIsConnectedAsTool = useIsConnectedAsTool as jest.MockedFunction<
  typeof useIsConnectedAsTool
>;

// Test wrapper component with React Flow provider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ReactFlowProvider>
    {children}
  </ReactFlowProvider>
);

// Helper function to render WorkflowNode with providers
const renderWorkflowNode = (data: WorkflowNodeData, props: Partial<NodeProps<WorkflowNodeData>> = {}) => {
  return render(
    <TestWrapper>
      <WorkflowNode
        data={data}
        isConnectable={true}
        selected={false}
        id="test-node-id"
        {...props}
      />
    </TestWrapper>
  );
};

// Mock data helpers
const createMockAgenticAINode = (hasTools: boolean = false, toolCount: number = 0): Node<WorkflowNodeData> => ({
  id: "agentic-ai-1",
  type: "custom",
  position: { x: 0, y: 0 },
  data: {
    label: "AI Agent",
    type: "component",
    originalType: "AgenticAI",
    definition: {
      name: "AgenticAI",
      display_name: "AI Agent Executor",
      description: "Execute AI agent with tools",
      category: "AI",
      icon: "brain",
      beta: false,
      inputs: [
        {
          name: "query",
          display_name: "Query",
          input_type: "string",
          is_handle: true,
          required: true,
        },
        ...(hasTools ? [
          {
            name: "tool_1",
            display_name: "Tool 1",
            input_type: "dynamic_handle",
            is_handle: true,
            required: false,
          },
          {
            name: "tool_2",
            display_name: "Tool 2",
            input_type: "dynamic_handle",
            is_handle: true,
            required: false,
          },
        ] : []),
      ],
      outputs: [
        {
          name: "response",
          display_name: "Response",
          output_type: "string",
        },
      ],
      is_valid: true,
      path: "components.ai.agentic_ai",
    },
    toolConnectionState: hasTools ? {
      hasConnectedTools: true,
      connectedToolCount: toolCount,
      toolConnections: [],
      availableToolSlots: [],
    } : undefined,
  },
});

const createMockComponentConnectedAsTool = (): Node<WorkflowNodeData> => ({
  id: "select-data-1",
  type: "custom",
  position: { x: 0, y: 0 },
  data: {
    label: "Select Data",
    type: "component",
    originalType: "SelectData",
    definition: {
      name: "SelectData",
      display_name: "Select Data Component",
      description: "Select data from input",
      category: "Data",
      icon: "database",
      beta: false,
      inputs: [
        {
          name: "data",
          display_name: "Data",
          input_type: "string",
          is_handle: true,
          required: true,
        },
      ],
      outputs: [
        {
          name: "output_data",
          display_name: "Output Data",
          output_type: "string",
        },
      ],
      is_valid: true,
      path: "components.data.select_data",
    },
    isConnectedAsTool: true,
  },
});

describe("WorkflowNode Tool Styling", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    mockGenerateToolStyleClasses.mockReturnValue({
      agenticAIWithTools: "",
      connectedAsTool: "",
      toolCountBadge: "",
    });
    mockIsToolHandle.mockReturnValue(false);
    mockUseAgenticAIToolConnections.mockReturnValue({
      hasConnectedTools: false,
      connectedToolCount: 0,
      toolConnections: [],
      availableToolSlots: [],
    });
    mockUseIsConnectedAsTool.mockReturnValue(false);
  });

  describe("AgenticAI Node with Tool Connections", () => {
    test("shows tool connection styling when tools are connected", () => {
      const mockNode = createMockAgenticAINode(true, 3);
      
      mockUseAgenticAIToolConnections.mockReturnValue({
        hasConnectedTools: true,
        connectedToolCount: 3,
        toolConnections: [],
        availableToolSlots: [],
      });
      
      mockGenerateToolStyleClasses.mockReturnValue({
        agenticAIWithTools: "agentic-ai-has-tools",
        connectedAsTool: "",
        toolCountBadge: "tool-count-badge",
      });

      renderWorkflowNode(mockNode.data);

      const nodeElement = screen.getByTestId("agentic-ai-node");
      expect(nodeElement).toHaveClass("agentic-ai-has-tools");
    });

    test("displays tool count badge when tools are connected", () => {
      const mockNode = createMockAgenticAINode(true, 3);

      mockUseAgenticAIToolConnections.mockReturnValue({
        hasConnectedTools: true,
        connectedToolCount: 3,
        toolConnections: [],
        availableToolSlots: [],
      });

      mockGenerateToolStyleClasses.mockReturnValue({
        agenticAIWithTools: "agentic-ai-has-tools",
        connectedAsTool: "",
        toolCountBadge: "tool-count-badge",
      });

      renderWorkflowNode(mockNode.data);

      const badge = screen.getByTestId("tool-count-badge");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveTextContent("3");
      expect(badge).toHaveClass("tool-count-badge");
    });

    test("does not show tool styling when no tools are connected", () => {
      const mockNode = createMockAgenticAINode(false, 0);
      
      mockUseAgenticAIToolConnections.mockReturnValue({
        hasConnectedTools: false,
        connectedToolCount: 0,
        toolConnections: [],
        availableToolSlots: [],
      });

      renderWorkflowNode(mockNode.data);

      const nodeElement = screen.getByTestId("agentic-ai-node");
      expect(nodeElement).not.toHaveClass("agentic-ai-has-tools");
      expect(screen.queryByTestId("tool-count-badge")).not.toBeInTheDocument();
    });
  });

  describe("Tool Handle Styling", () => {
    test("tool handles display wrench icon and orange styling", () => {
      const mockNode = createMockAgenticAINode(true, 2);

      mockIsToolHandle.mockImplementation((handleName) =>
        handleName === "tool_1" || handleName === "tool_2"
      );

      renderWorkflowNode(mockNode.data);

      const toolHandle1 = screen.getByTestId("handle-tool_1");
      const toolHandle2 = screen.getByTestId("handle-tool_2");
      
      expect(toolHandle1).toHaveClass("tool-handle");
      expect(toolHandle2).toHaveClass("tool-handle");
      
      // Check for wrench icon (this would be in CSS ::before pseudo-element)
      expect(toolHandle1).toHaveAttribute("data-tool-handle", "true");
      expect(toolHandle2).toHaveAttribute("data-tool-handle", "true");
    });

    test("regular handles maintain standard styling", () => {
      const mockNode = createMockAgenticAINode(true, 1);

      renderWorkflowNode(mockNode.data);

      const regularHandle = screen.getByTestId("handle-query");
      expect(regularHandle).toHaveClass("regular-handle");
      expect(regularHandle).not.toHaveClass("tool-handle");
    });
  });

  describe("Components Connected as Tools", () => {
    test("shows connected-as-tool styling for components connected to tool handles", () => {
      const mockNode = createMockComponentConnectedAsTool();

      mockUseIsConnectedAsTool.mockReturnValue(true);
      mockGenerateToolStyleClasses.mockReturnValue({
        agenticAIWithTools: "",
        connectedAsTool: "connected-as-tool",
        toolCountBadge: "",
      });

      renderWorkflowNode(mockNode.data);

      const nodeElement = screen.getByTestId("workflow-node");
      expect(nodeElement).toHaveClass("connected-as-tool");
    });

    test("does not show connected-as-tool styling for regular connections", () => {
      const mockNode = createMockComponentConnectedAsTool();
      mockNode.data.isConnectedAsTool = false;

      mockUseIsConnectedAsTool.mockReturnValue(false);

      renderWorkflowNode(mockNode.data);

      const nodeElement = screen.getByTestId("workflow-node");
      expect(nodeElement).not.toHaveClass("connected-as-tool");
    });
  });

  describe("Accessibility and Responsive Design", () => {
    test("tool styling maintains accessibility compliance", () => {
      const mockNode = createMockAgenticAINode(true, 2);

      mockUseAgenticAIToolConnections.mockReturnValue({
        hasConnectedTools: true,
        connectedToolCount: 2,
        toolConnections: [],
        availableToolSlots: [],
      });

      renderWorkflowNode(mockNode.data);

      const toolCountBadge = screen.getByTestId("tool-count-badge");
      expect(toolCountBadge).toHaveAttribute("aria-label", "2 tools connected");
    });

    test("tool handles have proper focus states", () => {
      const mockNode = createMockAgenticAINode(true, 1);

      mockIsToolHandle.mockImplementation((handleName) => handleName === "tool_1");

      renderWorkflowNode(mockNode.data);

      const toolHandle = screen.getByTestId("handle-tool_1");
      expect(toolHandle).toHaveAttribute("tabIndex", "0");
    });
  });
});
