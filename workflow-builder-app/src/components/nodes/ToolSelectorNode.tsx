import React, { memo, useState, useMemo } from "react";
import { NodeProps, useReactFlow, Node, Edge } from "reactflow";
import { WorkflowNodeData, ComponentDefinition } from "@/types";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { Search, Grip, Layers, ChevronRight, ArrowLeft, Copy, Trash2 } from "lucide-react";
import { nanoid } from "nanoid";
import Image from "next/image";
import { populateDefaultValues } from "@/utils/inputValueUtils";
import { validateMCPTool } from "@/utils/mcpValidation";
import { AddOutputSchemaDialog } from "@/components/modals/AddOutputSchemaDialog";
import { AddEnvKeysDialog } from "@/components/modals/AddEnvKeysDialog";
import { toast } from "sonner";

interface ToolSelectorNodeData extends WorkflowNodeData {
  selectedTool?: ComponentDefinition;
  availableTools?: ComponentDefinition[];
  isExpanded?: boolean;
  categoryName?: string;
}

const ToolSelectorNode = memo(({ data, isConnectable, selected, id }: NodeProps<ToolSelectorNodeData>) => {
  const { label, definition, selectedTool, availableTools = [], isExpanded = false, categoryName } = data;
  
  console.log(`ToolSelectorNode ${id}: Component mounted with data:`, {
    label,
    categoryName,
    isExpanded,
    availableToolsLength: availableTools.length,
    definition: definition ? { name: definition.name, category: definition.category } : null
  });
  
  const [searchTerm, setSearchTerm] = useState("");
  const [localIsExpanded, setLocalIsExpanded] = useState(isExpanded);
  const [localAvailableTools, setLocalAvailableTools] = useState<ComponentDefinition[]>(availableTools);
  const [hovered, setHovered] = useState(false);
  
  // Dialog states for MCP validation
  const [showOutputSchemaDialog, setShowOutputSchemaDialog] = useState(false);
  const [showEnvKeysDialog, setShowEnvKeysDialog] = useState(false);
  const [selectedMCPTool, setSelectedMCPTool] = useState<{ name: string; definition: ComponentDefinition } | null>(null);

  // Get React Flow instance for updating node data
  const { setNodes, getNodes, setEdges } = useReactFlow();

  // Auto-load tools if this is a category selector that's expanded by default
  React.useEffect(() => {
    console.log(`ToolSelectorNode ${id}: useEffect triggered with categoryName: "${categoryName}", isExpanded: ${isExpanded}, localAvailableTools.length: ${localAvailableTools.length}, availableTools.length: ${availableTools.length}`);
    
    // For category selectors, ensure they are expanded by default
    if (categoryName && !localIsExpanded) {
      console.log(`ToolSelectorNode ${id}: Auto-expanding category selector`);
      setLocalIsExpanded(true);
    }
    
    // Use tools passed as props if available, otherwise keep existing logic for backward compatibility
    if (availableTools.length > 0 && localAvailableTools.length === 0) {
      console.log(`ToolSelectorNode ${id}: Using ${availableTools.length} tools passed from sidebar`);
      setLocalAvailableTools(availableTools);
    } else if (categoryName && (isExpanded || localIsExpanded) && localAvailableTools.length === 0 && availableTools.length === 0) {
      // Only fetch from API if no tools were passed as props (backward compatibility)
      console.log(`ToolSelectorNode ${id}: No tools passed as props, falling back to API call for category: ${categoryName}`);
      
      const loadCategoryTools = async () => {
        try {
          console.log(`ToolSelectorNode ${id}: Loading tools for category: ${categoryName}`);
          
          // Extract MCP name from category name (remove "MCP_" prefix)
          const mcpName = categoryName.replace(/^MCP_/, '');
          console.log(`ToolSelectorNode ${id}: Extracted MCP name: "${mcpName}"`);
          
          // Fetch all MCP components to find tools for this specific MCP server
          const { fetchMCPComponents } = await import("@/lib/api");
          const allMcpComponents = await fetchMCPComponents();
          console.log(`ToolSelectorNode ${id}: Fetched all MCP components:`, allMcpComponents);
          
          // Log all available categories and their component counts
          Object.entries(allMcpComponents).forEach(([category, components]) => {
            const componentCount = Object.keys(components).length;
            console.log(`ToolSelectorNode ${id}: Category "${category}": ${componentCount} components`);
          });
          
          // Extract all tools from all MCP components
          const allTools: ComponentDefinition[] = [];
          Object.entries(allMcpComponents).forEach(([category, categoryComponents]) => {
            Object.values(categoryComponents).forEach((tool: any) => {
              allTools.push(tool);
            });
          });
          
          console.log(`ToolSelectorNode ${id}: Total MCP tools found: ${allTools.length}`);
          
          // Log all available MCP server names (extracted from display_name)
          const availableMcpServers = new Set<string>();
          allTools.forEach(tool => {
            if (tool.display_name && tool.display_name.includes(' - ')) {
              const serverName = tool.display_name.split(' - ')[0];
              availableMcpServers.add(serverName);
            }
          });
          console.log(`ToolSelectorNode ${id}: Available MCP servers:`, Array.from(availableMcpServers));
          
          // Filter for tools that belong to this specific MCP server
          const mcpTools = allTools.filter((tool: ComponentDefinition) => {
            const isMcpTool = tool.display_name && tool.display_name.startsWith(`${mcpName} - `);
            if (isMcpTool) {
              console.log(`ToolSelectorNode ${id}: Found MCP tool: ${tool.display_name}`);
            }
            return isMcpTool;
          });
          
          console.log(`ToolSelectorNode ${id}: Found ${mcpTools.length} MCP tools for "${mcpName}"`);
          setLocalAvailableTools(mcpTools);
        } catch (error) {
          console.error(`ToolSelectorNode ${id}: Error loading tools:`, error);
        }
      };
      
      loadCategoryTools();
    }
  }, [categoryName, isExpanded, localIsExpanded, localAvailableTools.length, availableTools.length, id]);

  // Filter tools based on search term - must be before any early returns
  const filteredTools = useMemo(() => {
    const toolsToFilter = localAvailableTools.length > 0 ? localAvailableTools : availableTools;
    if (!searchTerm.trim()) return toolsToFilter;
    
    const searchLower = searchTerm.toLowerCase();
    return toolsToFilter.filter(tool =>
      tool.display_name.toLowerCase().includes(searchLower) ||
      tool.description.toLowerCase().includes(searchLower) ||
      tool.category.toLowerCase().includes(searchLower)
    );
  }, [localAvailableTools, availableTools, searchTerm]);

  // Debug logging for tool counts
  console.log(`ToolSelectorNode ${id}: Final tool counts - localAvailableTools: ${localAvailableTools.length}, availableTools: ${availableTools.length}, filteredTools: ${filteredTools.length}, isExpanded: ${localIsExpanded}, categoryName: "${categoryName}"`);

  // If a tool is selected, render it as a regular workflow node
  if (selectedTool) {
    return (
      <TooltipProvider>
        <div className="relative">
          <Card
            className={`workflow-node relative w-52 overflow-visible transition-all duration-200 ${
              selected ? "ring-primary scale-105 shadow-lg ring-2" : "shadow-md hover:shadow-lg"
            }`}
            style={{
              minHeight: "90px",
              height: "auto",
              zIndex: 10,
            }}
          >
            <CardHeader className="flex flex-row items-center gap-1.5 space-y-0 px-2 py-2">
              <div className="bg-primary/10 rounded-md p-1">
                <Layers className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 overflow-hidden">
                <CardTitle className="truncate text-xs leading-tight font-medium">
                  {selectedTool.display_name}
                </CardTitle>
                <CardDescription className="truncate text-[9px] leading-tight">
                  {selectedTool.category}
                </CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleBackToSelector()}
                title="Back to tool selector"
              >
                <ArrowLeft className="h-3 w-3" />
              </Button>
            </CardHeader>
            <CardContent className="px-2 pb-2">
              <p className="text-xs text-muted-foreground line-clamp-2">
                {selectedTool.description}
              </p>
            </CardContent>
          </Card>
        </div>
      </TooltipProvider>
    );
  }

  // Handle tool selection - transform into regular workflow node
  const handleToolSelect = (tool: ComponentDefinition) => {
    console.log(`ToolSelectorNode ${id}: Transforming into WorkflowNode with tool: ${tool.display_name}`);
    
    // Validate MCP tool before allowing selection
    const validation = validateMCPTool(tool);
    if (!validation.isValid) {
      if (validation.requiresOutputSchema) {
        // Set the selected MCP tool for the dialog
        setSelectedMCPTool({
          name: validation.toolName,
          definition: tool,
        });
        // Show the output schema dialog
        setShowOutputSchemaDialog(true);
        toast.info(
          `Please define an output schema for ${validation.toolName} before adding it to the canvas.`
        );
        return;
      } else if (validation.requiresEnvKeys) {
        // Set the selected MCP tool for the dialog
        setSelectedMCPTool({
          name: validation.toolName,
          definition: tool,
        });
        // Show the env keys dialog
        setShowEnvKeysDialog(true);
        toast.info(
          `Please provide environment keys for ${validation.toolName} before adding it to the canvas.`
        );
        return;
      }
    }
    
    // Determine the correct node type following the same pattern as drag-and-drop
    let nodeType = "component";
    const originalNodeType = tool.name;
    
    if (originalNodeType.includes("MCP_")) {
      nodeType = "mcp";
    } else if (originalNodeType === "AgenticAI" || originalNodeType.startsWith("agent-")) {
      const isEmployeeAgent = originalNodeType.startsWith("agent-") || tool.agent_info?.is_bench_employee === true;
      nodeType = isEmployeeAgent ? "employee" : "agent";
    } else if (originalNodeType === "LoopNode") {
      nodeType = "loop";
    }
    
    // Generate a new ID following the same pattern as drag-and-drop
    const newNodeId = `${originalNodeType}-${+new Date()}`;
    
    // Create a temporary node to use with populateDefaultValues
    const tempNode: Node<WorkflowNodeData> = {
      id: newNodeId,
      type: "WorkflowNode",
      position: { x: 0, y: 0 }, // Position will be preserved from the original node
      data: {
        label: tool.display_name,
        type: nodeType, // Use the correctly determined node type
        originalType: originalNodeType, // Keep the original component name
        definition: tool,
        config: {}, // Will be populated with defaults
        oauthConnectionState: undefined,
      },
    };
    
    // Populate default values from input definitions
    const configWithDefaults = populateDefaultValues(tempNode);
    
    // Transform this node into a regular WorkflowNode
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? {
              ...node,
              id: newNodeId, // Generate new ID following drag-and-drop pattern
              type: "WorkflowNode", // Change node type to WorkflowNode
              data: {
                label: tool.display_name,
                type: nodeType, // Use the correctly determined node type
                originalType: originalNodeType, // Keep the original component name
                definition: tool,
                config: configWithDefaults, // Use populated default values
                oauthConnectionState: undefined,
              },
            }
          : node
      )
    );
  };

  // Handle back to selector
  const handleBackToSelector = () => {
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? {
              ...node,
              data: {
                ...node.data,
                selectedTool: undefined,
                isExpanded: localIsExpanded,
              },
            }
          : node
      )
    );
  };

  // Duplicate node handler
  const handleDuplicateNode = (e: React.MouseEvent) => {
    e.stopPropagation();
    const nodes = getNodes();
    const nodeToCopy = nodes.find((n) => n.id === id);
    if (!nodeToCopy) return;
    const newId = nanoid();
    const offset = 40;
    const newNode = {
      ...nodeToCopy,
      id: newId,
      position: {
        x: nodeToCopy.position.x + offset,
        y: nodeToCopy.position.y + offset,
      },
      data: { ...nodeToCopy.data, label: `${nodeToCopy.data.label || "Tool Selector"} (Copy)` },
      selected: false,
    };
    setNodes((nds) => [...nds, newNode]);
  };

  // Delete node handler
  const handleDeleteNode = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Remove both the node and all connected edges
    setNodes((nds) => nds.filter((n) => n.id !== id));
    setEdges((eds) => eds.filter((e) => e.source !== id && e.target !== id));
  };

  // Handle expand/collapse
  const handleToggleExpand = async () => {
    const newExpanded = !localIsExpanded;
    setLocalIsExpanded(newExpanded);
    
    console.log(`ToolSelectorNode ${id}: Expanding to ${newExpanded}, current tools: ${localAvailableTools.length}, availableTools: ${availableTools.length}`);
    
    // If expanding and we don't have tools yet, use tools from props or fetch them
    if (newExpanded && localAvailableTools.length === 0) {
      if (availableTools.length > 0) {
        // Use tools passed as props
        console.log(`ToolSelectorNode ${id}: Using ${availableTools.length} tools passed from sidebar`);
        setLocalAvailableTools(availableTools);
      } else {
        // Fallback to API call for backward compatibility
        try {
          console.log(`ToolSelectorNode ${id}: Fetching available tools for category: ${categoryName || 'all'}...`);
          // Import the API function to fetch components
          const { fetchComponents } = await import("@/lib/api");
          const components = await fetchComponents();
          
          // Extract tools based on category or all tools
          const allTools: ComponentDefinition[] = [];
          Object.entries(components).forEach(([category, categoryComponents]: [string, any]) => {
            // If we have a specific category, only load tools from that category
            if (categoryName && category.toLowerCase() !== categoryName.toLowerCase()) {
              return;
            }
            
            Object.values(categoryComponents).forEach((tool: any) => {
              if (tool.type === "MCP" || tool.type === "component") {
                allTools.push(tool);
              }
            });
          });
          
          console.log(`ToolSelectorNode ${id}: Found ${allTools.length} available tools for category: ${categoryName || 'all'}`);
          setLocalAvailableTools(allTools);
        } catch (error) {
          console.error(`ToolSelectorNode ${id}: Error fetching available tools:`, error);
        }
      }
    }
    
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === id
          ? {
              ...node,
              data: {
                ...node.data,
                isExpanded: newExpanded,
              },
            }
          : node
      )
    );
  };

  // Calculate dynamic height
  const baseHeight = localIsExpanded ? 400 : 90;
  const dynamicHeight = baseHeight;

  // Handle output schema submission
  const handleOutputSchemaSubmit = async (schema: object) => {
    if (selectedMCPTool && selectedMCPTool.definition.mcp_info) {
      try {
        // Show loading toast with an ID so we can dismiss it later
        const toastId = toast.loading(`Saving output schema for ${selectedMCPTool.name}...`);

        // Get the MCP server ID and tool name from the definition
        const mcpInfo = selectedMCPTool.definition.mcp_info;
        const mcpId = mcpInfo.server_id;
        const toolName = mcpInfo.tool_name;

        // Import the API function
        const { updateMCPToolOutputSchema } = await import("@/lib/api");

        // Call the API to update the output schema on the server
        const result = await updateMCPToolOutputSchema({
          mcp_id: mcpId,
          tool_name: toolName,
          output_schema_json: schema,
        });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Update the output schema in the definition
          selectedMCPTool.definition.mcp_info.output_schema = schema;

          // Show success message
          toast.success(`Output schema added for ${selectedMCPTool.name}`);

          // Reset state
          setSelectedMCPTool(null);
          setShowOutputSchemaDialog(false);

          // Now try to add the tool to the canvas
          handleToolSelect(selectedMCPTool.definition);
        } else {
          // Show error message
          toast.error(`Failed to save output schema: ${result.error || "Unknown error"}`);
        }
      } catch (error) {
        console.error("Error saving output schema:", error);
        // Dismiss any loading toasts that might be active
        toast.dismiss();
        toast.error(
          `Error saving output schema: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }
    }
  };

  // Handle environment keys submission
  const handleEnvKeysSubmit = async (envKeyValues: Array<{ key: string; value: string }>) => {
    if (selectedMCPTool) {
      try {
        // Show loading toast
        const toastId = toast.loading(`Saving environment keys for ${selectedMCPTool.name}...`);

        // Import the API function
        const { updateMCPEnvironmentKeys } = await import("@/lib/api");

        // Call the API to update the environment keys on the server
        const result = await updateMCPEnvironmentKeys({
          mcp_id: selectedMCPTool.definition.mcp_info?.server_id || "",
          env_key_values: envKeyValues,
        });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Update the env_credential_status in the definition
          selectedMCPTool.definition.env_credential_status = "provided";

          // Show success message
          toast.success(`Environment keys added for ${selectedMCPTool.name}`);

          // Reset state
          setSelectedMCPTool(null);
          setShowEnvKeysDialog(false);

          // Now try to add the tool to the canvas
          handleToolSelect(selectedMCPTool.definition);
        } else {
          // Show error message
          toast.error(`Failed to save environment keys: ${result.error || "Unknown error"}`);
        }
      } catch (error) {
        console.error("Error saving environment keys:", error);
        // Dismiss any loading toasts that might be active
        toast.dismiss();
        toast.error(
          `Error saving environment keys: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }
    }
  };

  return (
    <TooltipProvider>
      <div 
        className="relative w-full flex justify-center items-center"
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        {/* Hover overlay for duplicate/delete */}
        {hovered && (
          <div
            className="absolute left-1/2 top-0 z-50 flex -translate-x-1/2 -translate-y-1/2 gap-2 rounded-lg bg-white/60 dark:bg-zinc-900/60 backdrop-blur-md shadow-xl border border-white/30 dark:border-zinc-700/40 px-2 py-0.5 animate-fade-in"
            style={{ pointerEvents: 'auto', transition: 'box-shadow 0.2s, background 0.2s' }}
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={handleDuplicateNode}
                    className="group flex items-center justify-center rounded-full p-1 text-primary hover:bg-primary/10 hover:text-primary-700 focus:outline-none transition-all duration-150"
                    title="Duplicate Node"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>Duplicate</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={handleDeleteNode}
                    className="group flex items-center justify-center rounded-full p-1 text-red-500 hover:bg-red-100 hover:text-red-700 focus:outline-none transition-all duration-150"
                    title="Delete Node"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>Delete</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
        <Card
          className={`workflow-node relative w-80 overflow-visible transition-all duration-300 ${
            selected ? "ring-primary scale-105 shadow-xl ring-2" : "shadow-lg hover:shadow-xl"
          }`}
          style={{
            minHeight: `${dynamicHeight}px`,
            height: "auto",
            zIndex: 10,
            margin: "0 auto", // Center the card horizontally
          }}
          onClick={(e) => {
            // Prevent the click from bubbling up to the node level
            e.stopPropagation();
          }}
        >
          {/* Header */}
          <CardHeader className="flex flex-row items-center gap-3 space-y-0 px-4 py-2.5 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-b border-blue-100 dark:border-blue-800/30">
            <div className="bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg p-1.5 shadow-sm">
              <Layers className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1 overflow-hidden">
              <CardTitle className="truncate text-sm leading-tight font-semibold text-gray-900 dark:text-gray-100">
                {label || "Tool Selector"}
              </CardTitle>
              <CardDescription className="truncate text-xs leading-tight text-blue-600 dark:text-blue-400 font-medium">
                {categoryName ? `${localAvailableTools.length > 0 ? localAvailableTools.length : availableTools.length} tools from ${categoryName}` : `${localAvailableTools.length > 0 ? localAvailableTools.length : availableTools.length} tools available`}
                {availableTools.length > 0 && " (from sidebar)"}
              </CardDescription>
            </div>
                          <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-blue-100 dark:hover:bg-blue-800/30 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleExpand();
                }}
                title={localIsExpanded ? "Collapse" : "Expand"}
              >
                <ChevronRight className={`h-3 w-3 transition-transform duration-200 ${localIsExpanded ? 'rotate-90' : ''}`} />
              </Button>
          </CardHeader>

          {localIsExpanded && (
            <CardContent 
              className="px-4 pb-3 pt-2.5"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Search input */}
              <div 
                className="relative mb-3 mx-0"
                onClick={(e) => e.stopPropagation()}
              >
                <Search className="absolute left-3 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search tools..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onClick={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  className="w-full pl-9 pr-3 py-2 text-xs bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>

              {/* Tools list */}
              <CustomScrollArea 
                className="h-64 custom-scrollbar"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                <div className="space-y-2">
                  {filteredTools.length === 0 ? (
                    <div 
                      className="text-center py-8 text-sm text-gray-500 dark:text-gray-400"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="mb-2">
                        <Search className="h-8 w-8 mx-auto text-gray-300 dark:text-gray-600" />
                      </div>
                      {searchTerm ? "No tools match your search" : "No tools available"}
                    </div>
                  ) : (
                    filteredTools.map((tool) => (
                      <div
                        key={tool.name}
                        className="group cursor-pointer rounded-lg bg-white dark:bg-gray-800/50 p-2.5 hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-gray-100 dark:border-gray-700 hover:border-blue-200 dark:hover:border-blue-700 transition-all duration-200 hover:shadow-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToolSelect(tool);
                        }}
                      >
                        <div className="flex items-start gap-2.5 mb-1.5">
                          <div className="h-6 w-6 rounded-md bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-800/30 dark:to-indigo-800/30 flex items-center justify-center flex-shrink-0 shadow-sm">
                            <Layers className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1.5 mb-1">
                              <span className="text-xs font-semibold text-gray-900 dark:text-gray-100 break-words group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                                {tool.display_name}
                              </span>
                            </div>
                            <p className="text-[10px] text-gray-600 dark:text-gray-400 line-clamp-2 leading-tight break-words">
                              {tool.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CustomScrollArea>
            </CardContent>
          )}


        </Card>
        
        {/* Output Schema Dialog */}
        {selectedMCPTool && (
          <AddOutputSchemaDialog
            open={showOutputSchemaDialog}
            onOpenChange={setShowOutputSchemaDialog}
            toolName={selectedMCPTool.name}
            onSubmit={handleOutputSchemaSubmit}
          />
        )}

        {/* Environment Keys Dialog */}
        {selectedMCPTool && (
          <AddEnvKeysDialog
            open={showEnvKeysDialog}
            onOpenChange={setShowEnvKeysDialog}
            toolName={selectedMCPTool.name}
            envKeys={selectedMCPTool.definition.env_keys || []}
            onSubmit={handleEnvKeysSubmit}
          />
        )}
      </div>
    </TooltipProvider>
  );
});

ToolSelectorNode.displayName = "ToolSelectorNode";

export default ToolSelectorNode; 