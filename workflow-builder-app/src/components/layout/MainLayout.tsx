"use client";

import React, { useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { ParentSidebar } from "./ParentSidebar";
import { Button } from "@/components/ui/button";
import { useTheme } from "next-themes";
import { Plus, Moon, Sun, ChevronDown, FileEdit, File, FileUp, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { createEmptyWorkflow } from "@/app/(features)/workflows/api";
import { toast } from "sonner";

interface MainLayoutProps {
  children: React.ReactNode;
}

// Define the page configurations
const pageConfigs: Record<string, {
  title: string;
  buttonText: string;
  buttonAction: (() => void) | null;
  hasDropdown: boolean;
}> = {
  "/": {
    title: "Dashboard",
    buttonText: "Create new workflow",
    buttonAction: null, // Will be handled by dropdown
    hasDropdown: true
  },
      "/global-variable": {
    title: "Global variable",
    buttonText: "Create new global variable",
    buttonAction: () => {
      // Dispatch custom event to open global variable dialog
      window.dispatchEvent(new CustomEvent('openGlobalVariableDialog'));
    },
    hasDropdown: false
  },
  "/credentials": {
    title: "Credentials",
    buttonText: "Create new credential",
    buttonAction: () => {
      // Handle create credential action
      console.log("Create new credential");
    },
    hasDropdown: false
  },
      "/projects": {
    title: "Project",
    buttonText: "Create new project",
    buttonAction: () => {
      // Handle create project action
      console.log("Create new project");
    },
    hasDropdown: false
  },
  "/settings/triggers": {
    title: "Triggers and scheduler",
    buttonText: "Create new trigger",
    buttonAction: () => {
      // Handle create trigger action
      console.log("Create new trigger");
    },
    hasDropdown: false
  },
  "/settings/marketplace": {
            title: "Explore Workflows",
    buttonText: "Browse marketplace",
    buttonAction: () => {
      // Handle marketplace action
      console.log("Browse marketplace");
    },
    hasDropdown: false
  }
};

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isCreatingWorkflow, setIsCreatingWorkflow] = useState(false);

  // Handle hydration
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Theme toggle handler
  const handleToggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);
  };

  // Determine if dark mode is active
  const isDarkMode = theme === "dark";

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Get the current page configuration
  const getCurrentPageConfig = () => {
    // Find the matching route
    for (const [route, config] of Object.entries(pageConfigs)) {
      if (pathname === route || pathname.startsWith(route + "/")) {
        return config;
      }
    }
    // Default to dashboard if no match found
    return pageConfigs["/"];
  };

  const currentConfig = getCurrentPageConfig();

  // Handle creating a new workflow
  const handleCreateWorkflow = async () => {
    try {
      setIsCreatingWorkflow(true);
      const newWorkflow = await createEmptyWorkflow();
      console.log("Created new workflow:", newWorkflow);
      // Redirect to canvas page with the new workflow ID
      router.push(`/workflows/${newWorkflow.workflow_id}/edit`);
    } catch (err: any) {
      console.error("Failed to create workflow:", err);
      
      // Check if it's a duplicate name error
      if (err.message && err.message.includes("already exists")) {
        toast.error(err.message);
      } else {
        toast.error("Failed to create a new workflow. Please try again.");
      }
      setIsCreatingWorkflow(false);
    }
  };

  // Handle creating workflow from template (placeholder)
  const handleCreateFromTemplate = () => {
    // TODO: Implement template selection functionality
    console.log("Create from template clicked - functionality to be implemented");
    toast.info("Template functionality coming soon!");
  };

  // Handle importing workflow file (placeholder)
  const handleImportFile = () => {
    // TODO: Implement file import functionality
    console.log("Import file clicked - functionality to be implemented");
    toast.info("File import functionality coming soon!");
  };

  // Render the action button based on configuration
  const renderActionButton = () => {
    if (currentConfig.hasDropdown) {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              disabled={isCreatingWorkflow}
              className="bg-brand-primary hover:bg-brand-primary/90 text-white flex items-center gap-2"
            >
              {isCreatingWorkflow ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  {currentConfig.buttonText}
                </>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem
              onClick={handleCreateWorkflow}
              disabled={isCreatingWorkflow}
              className="flex items-center gap-2 py-2"
            >
              <FileEdit className="h-4 w-4" />
              <span>Create from blank</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleCreateFromTemplate}
              disabled={isCreatingWorkflow}
              className="flex items-center gap-2 py-2"
            >
              <File className="h-4 w-4" />
              <span>Create from template</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleImportFile}
              disabled={isCreatingWorkflow}
              className="flex items-center gap-2 py-2"
            >
              <FileUp className="h-4 w-4" />
              <span>Import file</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
         } else {
       return (
         <Button
           onClick={currentConfig.buttonAction || undefined}
           className="bg-brand-primary hover:bg-brand-primary/90 text-white flex items-center gap-2"
         >
           <Plus className="h-4 w-4" />
           {currentConfig.buttonText}
         </Button>
       );
     }
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Parent Sidebar */}
      <ParentSidebar
        collapsed={sidebarCollapsed}
        onToggleCollapse={handleToggleSidebar}
      />
      
      {/* Main Content Area */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Dynamic Header */}
        <div className="bg-card/30 p-3 shadow-md border-b border-gray-200 dark:border-gray-800 px-4 md:px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white font-[Satoshi]">
                {currentConfig.title}
              </h1>
            </div>
            <div className="flex items-center gap-3">
              {/* Theme Toggle */}
              {mounted && (
                <button
                  type="button"
                  className={`relative inline-flex h-10 w-16 items-center rounded-lg transition-colors duration-200 ${
                    isDarkMode ? 'bg-[#3F3F46]' : 'bg-[#E5E7EB]'
                  }`}
                  onClick={handleToggleTheme}
                  title="Toggle theme"
                >
                  {/* Toggle Thumb - Square/Rectangular */}
                  <div
                    className={`absolute h-8 w-7 transform rounded-sm shadow-md transition-transform duration-200 ${
                      isDarkMode ? 'bg-[#1E1E1E]' : 'bg-[#FFFFFF]'
                    }`}
                    style={{
                      left: isDarkMode ? '32px' : '4px'
                    }}
                  />
                  
                  {/* Sun Icon - Inside thumb when light mode, in background when dark mode */}
                  <div className={`absolute flex h-8 w-7 items-center justify-center transition-transform duration-200`}
                       style={{
                         left: '4px'
                       }}>
                    <Sun className={`h-4 w-4 transition-colors duration-200 ${
                      isDarkMode ? 'text-white' : 'text-black'
                    }`} />
                  </div>
                  
                  {/* Moon Icon - Inside thumb when dark mode, in background when light mode */}
                  <div className={`absolute flex h-8 w-7 items-center justify-center transition-transform duration-200`}
                       style={{
                         left: '32px'
                       }}>
                    <Moon className={`h-4 w-4 transition-colors duration-200 ${
                      isDarkMode ? 'text-white' : 'text-black'
                    }`} />
                  </div>
                </button>
              )}

              {/* Dynamic Action Button */}
              {renderActionButton()}
            </div>
          </div>
        </div>

        {/* Page Content */}
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </main>
    </div>
  );
}; 