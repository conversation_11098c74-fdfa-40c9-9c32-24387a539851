import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react";
import { ComponentsApiResponse, ComponentDefinition } from "@/types";
import { Agent } from "@/types/agents";
import { MCP_CATEGORIES, MCP_CATEGORY_LABELS, MCPCategory } from "@/types/mcp";
import { WorkflowListItem } from "@/lib/workflowApi";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { AddOutputSchemaDialog } from "@/components/modals/AddOutputSchemaDialog";
import { AddEnvKeysDialog } from "@/components/modals/AddEnvKeysDialog";
import { SkeletonMCPGroup, SkeletonCard, SkeletonSidebarContent } from "@/components/ui/skeleton";
import { toast } from "sonner";
import {
  Search,
  FileText,
  Database,
  Code,
  Workflow,
  Cog,
  Cpu,
  ArrowRightLeft,
  LogIn,
  Grip,
  Brain,
  Store,
  Bell,
  MessageCircle,
  Share2,
  CloudUpload,
  Settings,
  User,
  Users,
  Layers,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { fetchAgents } from "@/lib/agentApi";
import { MARKETPLACE_URL } from "@/lib/apiConfig";
import Image from "next/image";
import { validateMCPTool } from "@/utils/mcpValidation";

// Helper function to generate workflow inputs from start_nodes
const generateWorkflowInputs = (startNodes: any[]) => {
  if (!startNodes || startNodes.length === 0) {
    // Fallback to generic input if no start_nodes
    return [
      {
        name: "input",
        display_name: "Input",
        info: "Input data for the workflow",
        input_type: "object",
        required: true,
        is_handle: true,
        is_list: false,
        real_time_refresh: false,
        advanced: false,
        value: null,
        options: null,
        visibility_rules: null,
        visibility_logic: "OR" as const,
        requirement_rules: null,
        requirement_logic: "OR" as const
      }
    ];
  }

  // Convert start_nodes to input definitions
  return startNodes.map((startNode, index) => ({
    name: startNode.field || `input_${index}`,
    display_name: startNode.field ? startNode.field.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) : `Input ${index + 1}`,
    info: `Input field: ${startNode.field || `input_${index}`}`,
    input_type: startNode.type === "string" ? "string" : startNode.type === "number" ? "number" : "string",
    required: true,
    is_handle: true,
    is_list: false,
    real_time_refresh: false,
    advanced: false,
    value: null,
    options: null,
    visibility_rules: null,
    visibility_logic: "OR" as const,
    requirement_rules: null,
    requirement_logic: "OR" as const,
    // Store the transition_id for workflow execution
    transition_id: startNode.transition_id
  }));
};

// Stylized MCP Logo Component
interface MCPLogoProps {
  mcpName: string;
  mcpComponents: any[];
  className?: string;
}

const MCPLogo: React.FC<MCPLogoProps> = ({ mcpName, mcpComponents, className = "" }) => {
  const componentWithLogo = mcpComponents.find(comp => comp.logo);

  if (componentWithLogo?.logo) {
    return (
      <div className={`relative h-8 w-8 flex-shrink-0 ${className}`}>
        <div className="h-8 w-8 overflow-hidden rounded-md shadow-sm ring-1 ring-black/5 dark:ring-white/10">
          <Image
            src={componentWithLogo.logo}
            alt={`${mcpName} logo`}
            width={32}
            height={32}
            className="mcp-logo-sidebar h-full w-full object-cover"
          onError={(e) => {
            // Fallback to Cloud icon with enhanced styling
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div class="h-8 w-8 rounded-md bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-800/30 dark:to-indigo-800/30 flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10">
                  <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                  </svg>
                </div>
              `;
            }
          }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`h-8 w-8 rounded-md bg-black/5 dark:bg-white/10  flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10 ${className}`}>
      <Layers className="h-5 w-5 text-brand-primary dark:text-brand-secondary" />
    </div>
  );
};

// Stylized Agent Logo Component
interface AgentLogoProps {
  agent: {
    id: string;
    name: string;
    avatar?: string;
  };
}

const AgentLogo: React.FC<AgentLogoProps> = ({ agent }) => {
  if (agent.avatar) {
    return (
      <div className="relative h-8 w-8 flex-shrink-0">
        <Image
          src={agent.avatar}
          alt={`${agent.name} avatar`}
          width={32}
          height={32}
          className="rounded-full object-cover shadow-sm ring-1 ring-black/5 dark:ring-white/10"
          onError={(e) => {
            // Fallback to User icon with enhanced styling
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div class="h-8 w-8 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-800/30 dark:to-pink-800/30 flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10">
                  <svg class="h-5 w-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
              `;
            }
          }}
        />
      </div>
    );
  }

  return (
    <div className="h-8 w-8 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-800/30 dark:to-pink-800/30 flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10">
      <User className="h-5 w-5 text-purple-600 dark:text-purple-400" />
    </div>
  );
};

interface SidebarProps {
  components: ComponentsApiResponse;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  mcpCategories?: string[];
  loadedMcpCategories?: Set<string>;
  onLoadMCPCategory?: (category: string) => Promise<void>;
  onUpdateComponents?: (category: string, newComponents: any) => void;
  workflowComponents?: WorkflowListItem[];
  isLoadingWorkflows?: boolean;
  mcpCategoryMetadata?: Record<string, {
    total: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  }>;
  // New prop to control initial expanded state
  initialExpandedCategories?: string[];
}

// Helper function to make a node draggable
const onDragStart = (
  event: React.DragEvent,
  nodeType: string,
  definition: ComponentDefinition,
  setShowOutputSchemaDialog: React.Dispatch<React.SetStateAction<boolean>>,
  setSelectedMCPTool: React.Dispatch<
    React.SetStateAction<{ name: string; definition: ComponentDefinition } | null>
  >,
  setShowEnvKeysDialog?: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  // Add dragging class to clean up drag appearance
  const target = event.currentTarget as HTMLElement;
  target.classList.add('dragging');
  
  // Create a perfect clone of the dragged element to preserve exact appearance
  const dragImage = target.cloneNode(true) as HTMLElement;
  
  // Preserve all original styling and appearance
  dragImage.style.position = 'absolute';
  dragImage.style.top = '-1000px';
  dragImage.style.left = '-1000px';
  dragImage.style.pointerEvents = 'none';
  dragImage.style.zIndex = '9999';
  dragImage.style.width = target.offsetWidth + 'px';
  dragImage.style.height = target.offsetHeight + 'px';
  
  // Preserve the exact visual appearance without any modifications
  dragImage.style.transform = 'none';
  dragImage.style.opacity = '0.9';
  
  // Only hide scrollbars without affecting other styling
  const scrollableElements = dragImage.querySelectorAll('.custom-scrollbar, [style*="overflow"]');
  scrollableElements.forEach(el => {
    const element = el as HTMLElement;
    element.style.scrollbarWidth = 'none';
    (element.style as any).msOverflowStyle = 'none';
    // Add webkit scrollbar hiding
    const style = document.createElement('style');
    style.textContent = `
      .drag-ghost *::-webkit-scrollbar { display: none !important; }
    `;
    dragImage.appendChild(style);
  });
  
  // Ensure the drag image maintains all computed styles
  const computedStyle = window.getComputedStyle(target);
  dragImage.style.backgroundColor = computedStyle.backgroundColor;
  dragImage.style.border = computedStyle.border;
  dragImage.style.borderRadius = computedStyle.borderRadius;
  dragImage.style.boxShadow = computedStyle.boxShadow;
  dragImage.style.padding = computedStyle.padding;
  dragImage.style.margin = '0';
  
  // Add the drag-ghost class for additional styling
  dragImage.classList.add('drag-ghost');
  dragImage.classList.remove('dragging');
  
  // Append to body temporarily for drag image - this must be synchronous
  document.body.appendChild(dragImage);
  
  // Set the custom drag image with proper offset - this triggers immediately
  event.dataTransfer.setDragImage(dragImage, target.offsetWidth / 2, target.offsetHeight / 2);
  
  // Clean up classes and drag image after drag ends
  const cleanup = () => {
    target.classList.remove('dragging');
    if (document.body.contains(dragImage)) {
      document.body.removeChild(dragImage);
    }
    document.removeEventListener('dragend', cleanup);
    document.removeEventListener('drop', cleanup);
  };
  
  // Use requestAnimationFrame for immediate cleanup after the drag image is captured
  requestAnimationFrame(() => {
    if (document.body.contains(dragImage)) {
      document.body.removeChild(dragImage);
    }
  });
  
  document.addEventListener('dragend', cleanup);
  document.addEventListener('drop', cleanup);

  // Validate MCP tool before allowing drag
  const validation = validateMCPTool(definition);
  if (!validation.isValid) {
    event.preventDefault();
    
    if (validation.requiresOutputSchema) {
      console.log(`Preventing drag for MCP tool without output schema: ${definition.display_name}`);

      // Set the selected MCP tool for the dialog
      setSelectedMCPTool({
        name: validation.toolName,
        definition,
      });
      // Show the output schema dialog
      setShowOutputSchemaDialog(true);
      toast.info(
        `Please define an output schema for ${validation.toolName} before adding it to the canvas.`
      );
      return;
    } else if (validation.requiresEnvKeys) {
      console.log(`Preventing drag for MCP tool with env keys: ${definition.display_name}`);

      // Set the selected MCP tool for the dialog
      setSelectedMCPTool({
        name: validation.toolName,
        definition,
      });
      // Show the env keys dialog if the setter is provided
      if (setShowEnvKeysDialog) {
        setShowEnvKeysDialog(true);
      }
      toast.info(
        `Please provide environment keys for ${validation.toolName} before adding it to the canvas.`
      );
      return;
    }
  }

  // If validation passes, proceed with drag
  const nodeData = JSON.stringify({ nodeType, definition });
  event.dataTransfer.setData("application/reactflow", nodeData);
  event.dataTransfer.effectAllowed = "move";
};

// Helper function to get appropriate icon based on category
const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
    case "io":
      return LogIn;
    case "data":
      return Database;
    case "processing":
      return Cpu;
    case "api":
      return ArrowRightLeft;
    case "control flow":
      return Workflow;
    case "text":
      return FileText;
    case "code":
      return Code;
    case "ai":
      return Brain; // AI/LLM uses Brain icon
    case "mcp":
      return Cpu; // MCP components use CPU icon
    case "marketplace":
    case "mcp marketplace":
    case "tools":
      return Store; // Tools components use Store icon
    case "agents":
      return Users; // Agents use Users icon
    case "workflows":
      return Workflow; // Workflows use Workflow icon
    case "notifications alerts":
      return Bell; // Notifications & Alerts components use Bell icon
    case "communication":
      return MessageCircle; // Communication components use MessageCircle icon
    case "social media":
      return Share2; // Social Media components use Share2 icon
    case "database":
      return Database; // Database components use Database icon
    case "cloud storage":
      return CloudUpload; // Cloud Storage components use CloudUpload icon
    case "devops system":
      return Settings; // DevOps System components use Settings icon
    case "file handling":
      return FileText; // File Handling components use FileText icon
    default:
      return Cog;
  }
};

// Helper function to get user-friendly category display name
const getCategoryDisplayName = (category: string) => {
  switch (category.toLowerCase()) {
    case "io":
      return "Input/Output";
    case "ai":
      return "AI/LLM";
    case "mcp":
      return "MCP Marketplace";
    case "mcp marketplace":
      return "Tools";
    case "tools":
      return "Tools";
    case "agents":
      return "Agents";
    case "workflows":
      return "Workflows";
    case "notifications alerts":
      return "Notifications & Alerts";
    case "communication":
      return "Communication";
    case "social media":
      return "Social Media";
    case "database":
      return "Database";
    case "cloud storage":
      return "Cloud Storage";
    case "devops system":
      return "DevOps System";
    case "file handling":
      return "File Handling";
    default:
      return category.charAt(0).toUpperCase() + category.slice(1);
  }
};

/**
 * Sidebar Component
 * 
 * This component displays the workflow components sidebar with the following features:
 * - Persistent expanded state (survives theme changes and component updates)
 * - MCP component grouping and pagination
 * - Search functionality with auto-expansion
 * - Drag and drop support for components
 * - Local storage persistence for user preferences
 * 
 * FIXED ISSUES:
 * - Dropdowns no longer close when MCP components are loaded via pagination
 * - Dropdowns no longer close when theme is changed
 * - Expanded state is preserved across page refreshes
 */
export const Sidebar = React.memo(function Sidebar({
  components,
  collapsed = false,
  onToggleCollapse,
  mcpCategories = [],
  loadedMcpCategories = new Set(),
  onLoadMCPCategory,
  onUpdateComponents,
  workflowComponents = [],
  isLoadingWorkflows = false,
  mcpCategoryMetadata = {},
  initialExpandedCategories = [],
}: SidebarProps) {
  // Removed console.log statements to improve performance during renders
  const categories = Object.keys(components).sort();

  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  
  // Initialize expanded categories from initial props
  const [expandedCategories, setExpandedCategories] = useState<string[]>(initialExpandedCategories);

  // State to track expanded MCP server groups
  const [expandedMCPGroups, setExpandedMCPGroups] = useState<Record<string, string[]>>({});

  // State to track pagination for categories (showing MCP servers in each category)
  const [categoryPagination, setCategoryPagination] = useState<Record<string, {
    visibleCount: number;
    isLoading: boolean;
    hasMore: boolean;
    page: number;
    totalLoaded: number;
    totalCount: number; // Add total count from API metadata
  }>>({});

  // State for output schema dialog
  const [showOutputSchemaDialog, setShowOutputSchemaDialog] = useState(false);
  const [selectedMCPTool, setSelectedMCPTool] = useState<{
    name: string;
    definition: ComponentDefinition;
  } | null>(null);

  // State for env keys dialog
  const [showEnvKeysDialog, setShowEnvKeysDialog] = useState(false);

  // State for agents
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoadingAgents, setIsLoadingAgents] = useState(false);

  // We'll only load agents when the components prop changes, ensuring everything loads together
  useEffect(() => {
    if (Object.keys(components).length > 0) {
      async function loadAgents() {
        setIsLoadingAgents(true);
        try {
          // Using the dedicated agentApi for better error handling
          const result = await fetchAgents({
            page: 1,
            page_size: 50, // Adjust as needed
          });
          setAgents(result.data);
          console.log("Loaded agents together with components:", result.data);
        } catch (error) {
          console.error("Error loading agents:", error);
          toast.error("Failed to load agents");
        } finally {
          setIsLoadingAgents(false);
        }
      }
      
      // Only load agents after components are available
      loadAgents();
    }
  }, [components]);

  // Initialize expanded categories only on first load, not on every component update
  useEffect(() => {
    if (Object.keys(components).length > 0 && expandedCategories.length === 0) {
      // Only set initial expanded categories if none are currently expanded
      // This prevents resetting when components are updated or theme changes
      setExpandedCategories(initialExpandedCategories);
    }
  }, [components, initialExpandedCategories, expandedCategories.length]);

  // Preserve expanded MCP groups state - don't reset on component updates
  // Only reset when search term changes (handled in handleSearchChange)

  // Filter components based on search term
  const filteredComponents = useCallback(() => {
    // Start with a base set of components, but filter out legacy categories that have enum equivalents
    const filtered: ComponentsApiResponse = {};
    
    // First, add all non-MCP categories from components
    Object.entries(components).forEach(([category, categoryComponents]) => {
      // Check if this category has an enum equivalent
      const hasEnumEquivalent = MCP_CATEGORIES.some(enumKey => {
        const enumLabel = MCP_CATEGORY_LABELS[enumKey];
        return enumLabel === category || enumKey === category;
      });
      
      // If it doesn't have an enum equivalent, or if it's the proper enum label, add it
      if (!hasEnumEquivalent || Object.values(MCP_CATEGORY_LABELS).includes(category)) {
        filtered[category] = categoryComponents;
      }
    });
    
    // Add placeholder categories for all MCP categories from enum
    MCP_CATEGORIES.forEach(category => {
      const categoryKey = MCP_CATEGORY_LABELS[category];
      if (!filtered[categoryKey]) {
        filtered[categoryKey] = {};
      }
    });
    
    // Also add any additional mcpCategories passed as props (for backward compatibility)
    // But skip if we already have the enum version
    mcpCategories.forEach(category => {
      const isAlreadyAddedAsEnum = Object.values(MCP_CATEGORY_LABELS).includes(category);
      const hasEnumEquivalent = MCP_CATEGORIES.some(enumKey => {
        const enumLabel = MCP_CATEGORY_LABELS[enumKey];
        return enumLabel === category || enumKey === category;
      });
      
      if (!filtered[category] && !isAlreadyAddedAsEnum && !hasEnumEquivalent) {
        filtered[category] = {};
      }
    });
    
    // Add the Agents category if we have agents
    if (agents.length > 0) {
      filtered["Agents"] = {};
    }
    
    // Add the Workflows category if we have workflow components
    if (workflowComponents.length > 0) {
      filtered["Workflows"] = {};
    }
    

    
    // If we're searching, we need to filter components and agents
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      
      // Create a new filtered object
      const searchFiltered: ComponentsApiResponse = {};
      
      // Filter regular components, but skip legacy categories that have enum equivalents
      Object.entries(components).forEach(([category, categoryComponents]) => {
        // Check if this category has an enum equivalent
        const hasEnumEquivalent = MCP_CATEGORIES.some(enumKey => {
          const enumLabel = MCP_CATEGORY_LABELS[enumKey];
          return enumLabel === category || enumKey === category;
        });
        
        // If it has an enum equivalent and it's not the proper enum label, skip it
        if (hasEnumEquivalent && !Object.values(MCP_CATEGORY_LABELS).includes(category)) {
          return;
        }
        
        const matchingComponents = Object.values(categoryComponents).filter(
          (comp) =>
            comp.display_name.toLowerCase().includes(searchLower) ||
            comp.description.toLowerCase().includes(searchLower),
        );

        if (matchingComponents.length > 0) {
          searchFiltered[category] = Object.fromEntries(
            matchingComponents.map((comp) => [comp.name, comp]),
          );
        }
      });
      
      // Add MCP categories that match the search (even if not loaded yet)
      MCP_CATEGORIES.forEach(category => {
        const categoryKey = MCP_CATEGORY_LABELS[category];
        if (categoryKey.toLowerCase().includes(searchLower) || category.toLowerCase().includes(searchLower)) {
          if (!searchFiltered[categoryKey]) {
            searchFiltered[categoryKey] = components[categoryKey] || {};
          }
        }
      });
      
      // Also check additional mcpCategories passed as props (for backward compatibility)
      // But skip if we already have the enum version
      mcpCategories.forEach(category => {
        const isAlreadyAddedAsEnum = Object.values(MCP_CATEGORY_LABELS).includes(category);
        const hasEnumEquivalent = MCP_CATEGORIES.some(enumKey => {
          const enumLabel = MCP_CATEGORY_LABELS[enumKey];
          return enumLabel === category || enumKey === category;
        });
        
        if (category.toLowerCase().includes(searchLower) && !isAlreadyAddedAsEnum && !hasEnumEquivalent) {
          if (!searchFiltered[category]) {
            searchFiltered[category] = components[category] || {};
          }
        }
      });
      
      // Filter agents and add them to a dedicated category only if there are matches
      const filteredAgents = agents.filter(
        (agent) =>
          agent.name.toLowerCase().includes(searchLower) ||
          agent.description.toLowerCase().includes(searchLower) ||
          (agent.category && agent.category.toLowerCase().includes(searchLower)),
      );

      // Only add agents category if there are matching agents
      if (filteredAgents.length > 0) {
        searchFiltered["Agents"] = {};
      }
      
      // Filter workflow components and add them to a dedicated category only if there are matches
      const filteredWorkflows = workflowComponents.filter(
        (workflow) =>
          workflow.name.toLowerCase().includes(searchLower) ||
          (workflow.description && workflow.description.toLowerCase().includes(searchLower)),
      );

      // Only add workflows category if there are matching workflows
      if (filteredWorkflows.length > 0) {
        searchFiltered["Workflows"] = {};
      }
      
      return searchFiltered;
    }
    
    return filtered;
  }, [components, searchTerm, agents, mcpCategories, workflowComponents]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const previousValue = searchTerm;
    setSearchTerm(value);

    // Show loading state briefly for search
    if (value.trim()) {
      setIsSearching(true);
      setTimeout(() => {
        setIsSearching(false);
      }, 300);
    } else {
      setIsSearching(false);
    }

    // Auto-expand all categories when searching
    if (value.trim()) {
      const filteredCats = Object.keys(filteredComponents());
      setExpandedCategories(filteredCats);

      // Also expand all MCP groups in the filtered categories
      const newExpandedGroups: Record<string, string[]> = {};
      filteredCats.forEach((category) => {
        const categoryComponents = filteredComponents()[category];
        if (categoryComponents) {
          // Find all MCP groups in this category
          const mcpGroups = new Set<string>();
          Object.values(categoryComponents).forEach((comp) => {
            if (comp.type === "MCP" && comp.display_name.includes(" - ")) {
              const mcpName = comp.display_name.split(" - ")[0];
              mcpGroups.add(mcpName);
            }
          });

          if (mcpGroups.size > 0) {
            newExpandedGroups[category] = Array.from(mcpGroups);
          }
        }
      });

      setExpandedMCPGroups(newExpandedGroups);
    } else if (previousValue.trim() && !value.trim()) {
      // When clearing search, restore initial expanded categories but preserve user interactions
      // Only reset if we had a search term before and now we don't
      setExpandedCategories(initialExpandedCategories);
      // Clear expanded MCP groups when search is cleared
      setExpandedMCPGroups({});
    }
  };

  // Toggle category expansion
  const handleCategoryToggle = async (category: string) => {
    const isExpanding = !expandedCategories.includes(category);
    
    // Update expanded categories state
    setExpandedCategories((prev) =>
      prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category],
    );
    
    // Check if this is an MCP category from our enum
    const isMCPEnumCategory = Object.values(MCP_CATEGORY_LABELS).includes(category);
    
    // If expanding an MCP category that hasn't been loaded yet, load it
    // For enum categories, we need to find the corresponding enum key
    let categoryToLoad = category;
    if (isMCPEnumCategory) {
      // Find the enum key that corresponds to this label
      const enumKey = Object.keys(MCP_CATEGORY_LABELS).find(
        key => MCP_CATEGORY_LABELS[key as MCPCategory] === category
      );
      if (enumKey) {
        categoryToLoad = enumKey;
      }
    }
    
    if (isExpanding && (mcpCategories.includes(category) || isMCPEnumCategory) && !loadedMcpCategories.has(categoryToLoad) && onLoadMCPCategory) {
      try {
        await onLoadMCPCategory(categoryToLoad);
      } catch (error) {
        console.error(`Failed to load MCP category ${categoryToLoad}:`, error);
        // Optionally show a toast error message
      }
    }
  };

  // Toggle MCP group expansion
  const handleMCPGroupToggle = (category: string, mcpName: string) => {
    setExpandedMCPGroups((prev) => {
      const categoryGroups = prev[category] || [];
      const newCategoryGroups = categoryGroups.includes(mcpName)
        ? categoryGroups.filter((name) => name !== mcpName)
        : [...categoryGroups, mcpName];

      return {
        ...prev,
        [category]: newCategoryGroups,
      };
    });

  };

  // Initialize pagination state for a category
  const initializePaginationForCategory = (category: string, totalMcpServers: number) => {
    if (!categoryPagination[category]) {
      // Check if we have metadata for this category
      const metadata = mcpCategoryMetadata[category];
      const totalCount = metadata ? metadata.total : totalMcpServers;
      
      setCategoryPagination((prev) => ({
        ...prev,
        [category]: {
          visibleCount: 10,
          isLoading: false,
          hasMore: totalCount > 10, // Only has more if total count is greater than initial load
          page: 1,
          totalLoaded: 0,
          totalCount: totalCount, // Use the total count from metadata
        },
      }));
    }
  };

  // Get pagination state for a category
  const getCategoryPaginationState = (category: string) => {
    // Check if we have metadata for this category
    const metadata = mcpCategoryMetadata[category];
    const totalCount = metadata ? metadata.total : 0;
    
    return categoryPagination[category] || {
      visibleCount: 10,
      isLoading: false,
      hasMore: totalCount > 10, // Only has more if total count is greater than initial load
      page: 1,
      totalLoaded: 0,
      totalCount: totalCount, // Use the total count from metadata
    };
  };

  // Handle load more functionality for category
  const handleCategoryLoadMore = async (category: string, totalMcpServers: number) => {
    const currentState = getCategoryPaginationState(category);
    
    // Set loading state
    setCategoryPagination((prev) => ({
      ...prev,
      [category]: {
        ...currentState,
        isLoading: true,
      },
    }));

    try {
      // Use the new paginated API to fetch the next page
      const nextPage = currentState.page + 1;
      console.log(`Loading more MCP components for category "${category}" - page ${nextPage}`);
      
      // Import the new API function
      const { fetchMCPComponentsByCategoryWithMetadata } = await import("@/lib/api");
      
      // Find the original category name from the display label
      // Reverse lookup from MCP_CATEGORY_LABELS to get the original category name
      let originalCategory = category;
      for (const [key, label] of Object.entries(MCP_CATEGORY_LABELS)) {
        if (label === category) {
          originalCategory = key;
          break;
        }
      }
      
      // Fetch the next page of components using the original category name
      const response = await fetchMCPComponentsByCategoryWithMetadata(originalCategory, nextPage, 10);
      
      console.log(`API Response for category "${originalCategory}":`, response);
      
      if (response.data && Object.keys(response.data).length > 0) {
        // Get the display label for this category
        const categoryDisplayLabel = MCP_CATEGORY_LABELS[originalCategory as MCPCategory] || originalCategory;
        
        // Try to find components in the response data
        // The API might return components under the original category name or display label
        let newComponents = {};
        
        // First try the display label
        if (response.data[categoryDisplayLabel]) {
          newComponents = response.data[categoryDisplayLabel];
          console.log(`Found components under display label "${categoryDisplayLabel}":`, newComponents);
        }
        // Then try the original category name
        else if (response.data[originalCategory]) {
          newComponents = response.data[originalCategory];
          console.log(`Found components under original category "${originalCategory}":`, newComponents);
        }
        // Finally, try any category in the response
        else {
          const firstCategory = Object.keys(response.data)[0];
          if (firstCategory) {
            newComponents = response.data[firstCategory];
            console.log(`Found components under first available category "${firstCategory}":`, newComponents);
          }
        }
        
        const existingComponents = components[categoryDisplayLabel] || {};
        
        // Filter out components that already exist to only count new ones
        const trulyNewComponents: Record<string, any> = {};
        Object.entries(newComponents).forEach(([componentName, componentData]) => {
          if (!existingComponents[componentName]) {
            trulyNewComponents[componentName] = componentData;
          }
        });
        
        // Combine existing and new components
        const combinedComponents = {
          ...existingComponents,
          ...trulyNewComponents
        };
        
        console.log(`Combined components for "${categoryDisplayLabel}":`, {
          existing: Object.keys(existingComponents).length,
          new: Object.keys(newComponents).length,
          trulyNew: Object.keys(trulyNewComponents).length,
          total: Object.keys(combinedComponents).length
        });
        
        // Update the components data using the callback
        if (onUpdateComponents) {
          onUpdateComponents(categoryDisplayLabel, combinedComponents);
        }
        
        console.log(`Loaded ${Object.keys(trulyNewComponents).length} new components for category "${category}"`);
        
        // Update pagination state based on metadata
        const metadata = response.metadata;
        const hasMore = metadata.hasNextPage;
        
        setCategoryPagination((prev) => ({
          ...prev,
          [category]: {
            ...currentState,
            isLoading: false,
            hasMore: hasMore,
            page: nextPage,
            totalLoaded: currentState.totalLoaded + Object.keys(trulyNewComponents).length,
            totalCount: metadata.total, // Use 'total' from metadata
          },
        }));
        
        // Remove success toast - no longer show success message
        // toast.success(`Loaded ${Object.keys(trulyNewComponents).length} more MCP components`);
      } else {
        // No more components to load
        setCategoryPagination((prev) => ({
          ...prev,
          [category]: {
            ...currentState,
            isLoading: false,
            hasMore: false,
          },
        }));
        
        // Remove info toast - no longer show "no more components" message
        // toast.info("No more MCP components to load");
      }
    } catch (error) {
      console.error('Error loading more MCP servers:', error);
      // Reset loading state on error
      setCategoryPagination((prev) => ({
        ...prev,
        [category]: {
          ...currentState,
          isLoading: false,
        },
      }));
      // Remove error toast - no longer show error message
      // toast.error('Failed to load more MCP servers');
    }
  };

  // Memoize the filtered data to prevent recalculation on every render
  const filteredData = useMemo(() => filteredComponents(), [filteredComponents]);

  // Memoize the visible categories to prevent recalculation on every render
  const visibleCategories = useMemo(() => {
    // Get categories from filtered data
    const categories = Object.keys(filteredData);
    
    // Filter out the "io" category and then sort the remaining categories
    return categories
      .filter((category) => category.toLowerCase() !== "io") // Comment out IO category
      .sort((a, b) => {
        // Priority order: AI first, then MCP Marketplace, then Agents, then Data, then new MCP categories, then alphabetical
        // IO category is filtered out
        const aLower = a.toLowerCase();
        const bLower = b.toLowerCase();

        if (aLower === "ai") return -1;
        if (bLower === "ai") return 1;
        if (aLower === "mcp marketplace" || aLower === "tools") return -1;
        if (bLower === "mcp marketplace" || bLower === "tools") return 1;
        if (aLower === "agents") return -1;
        if (bLower === "agents") return 1;
        if (aLower === "workflows") return -1;
        if (bLower === "workflows") return 1;
        if (aLower === "mcp") return -1;
        if (bLower === "mcp") return 1;
        if (aLower === "data") return -1;
        if (bLower === "data") return 1;

        // Group MCP categories together after the priority categories
        const mcpCategoryLabels = Object.values(MCP_CATEGORY_LABELS).map(label => label.toLowerCase());
        const aIsMcp = mcpCategoryLabels.includes(aLower);
        const bIsMcp = mcpCategoryLabels.includes(bLower);

        if (aIsMcp && !bIsMcp) return -1;
        if (!aIsMcp && bIsMcp) return 1;
        if (aIsMcp && bIsMcp) {
          // Sort MCP categories in a specific order
          return mcpCategories.indexOf(aLower) - mcpCategories.indexOf(bLower);
        }

        return a.localeCompare(b);
      });
  }, [filteredData, agents, workflowComponents, mcpCategories]);

  // Handle output schema submission
  const handleOutputSchemaSubmit = async (schema: object) => {
    if (selectedMCPTool && selectedMCPTool.definition.mcp_info) {
      try {
        // Show loading toast with an ID so we can dismiss it later
        const toastId = toast.loading(`Saving output schema for ${selectedMCPTool.name}...`);

        // Get the MCP server ID and tool name from the definition
        const mcpInfo = selectedMCPTool.definition.mcp_info;
        const mcpId = mcpInfo.server_id;
        const toolName = mcpInfo.tool_name;

        // Import the API function
        const { updateMCPToolOutputSchema } = await import("@/lib/api");

        // Call the API to update the output schema on the server
        const result = await updateMCPToolOutputSchema({
          mcp_id: mcpId,
          tool_name: toolName,
          output_schema_json: schema,
        });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Update the output schema in the definition
          selectedMCPTool.definition.mcp_info.output_schema = schema;

          // Show success message
          toast.success(`Output schema added for ${selectedMCPTool.name}`);

          // Reset state
          setSelectedMCPTool(null);
          setShowOutputSchemaDialog(false);
        } else {
          // Show error message
          toast.error(`Failed to save output schema: ${result.error || "Unknown error"}`);
        }
      } catch (error) {
        console.error("Error saving output schema:", error);
        // Dismiss any loading toasts that might be active
        toast.dismiss();
        toast.error(
          `Error saving output schema: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }
    }
  };

  // Handle environment keys submission
  const handleEnvKeysSubmit = async (envKeyValues: Array<{ key: string; value: string }>) => {
    if (selectedMCPTool && selectedMCPTool.definition.mcp_info) {
      try {
        // Show loading toast with an ID so we can dismiss it later
        const toastId = toast.loading(`Saving environment keys for ${selectedMCPTool.name}...`);

        // Get the MCP server ID from the definition
        const mcpInfo = selectedMCPTool.definition.mcp_info;
        const mcpId = mcpInfo.server_id;

        // Import the API function
        const { updateMCPEnvironmentKeys } = await import("@/lib/api");

        // Call the API to update the environment keys on the server
        const result = await updateMCPEnvironmentKeys({
          mcp_id: mcpId,
          env_key_values: envKeyValues,
        });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Show success message
          toast.success(`Environment keys added for ${selectedMCPTool.name}`);

          // Reset state
          setSelectedMCPTool(null);
          setShowEnvKeysDialog(false);
        } else {
          // Show error message
          toast.error(`Failed to save environment keys: ${result.error || "Unknown error"}`);
        }
      } catch (error) {
        console.error("Error saving environment keys:", error);
        // Dismiss any loading toasts that might be active
        toast.dismiss();
        toast.error(
          `Error saving environment keys: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }
    }
  };

  return (
    <aside
      className={`bg-white dark:bg-[#000000] border-r border-gray-200 dark:border-gray-800 relative flex h-full shrink-0 flex-col overflow-hidden shadow-md transition-all duration-300 ${
        collapsed ? "w-16" : "w-80"
      }`}
    >
      {/* Header with Search box */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-800">
        {!collapsed && (
          <div className="flex-1 relative">
            <div className="relative flex items-center h-10 rounded-lg bg-[#F9FAFB] border border-[#E5E7EB] dark:bg-[#18181B] dark:border-[#3F3F46] transition-colors">
              <div className="absolute left-3 top-1/2 -translate-y-1/2 flex items-center justify-center">
                <Search className="h-4 w-4 text-gray-500 dark:text-[#A1A1AA]" />
              </div>
              <input
                type="text"
                placeholder="Search components"
                value={searchTerm}
                onChange={handleSearchChange}
                className="flex-1 pl-10 pr-8 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-[#A1A1AA] font-[Satoshi] text-sm focus:outline-none"
                aria-label="Search components"
              />
            </div>
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center justify-center">
              <kbd className="px-1.5 py-0.5 text-xs font-medium text-gray-500 dark:text-[#A1A1AA] bg-gray-100 border border-gray-300 dark:bg-[#232329] dark:border-[#35363C] rounded">/</kbd>
            </div>
          </div>
        )}
        <button
          onClick={onToggleCollapse}
          className="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors ml-2"
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {collapsed ? (
            <>
              <Image
                src="/panel-left-close_light.png"
                alt="Expand sidebar"
                width={24}
                height={24}
                className="block dark:hidden"
              />
              <Image
                src="/panel-left-close_dark.png"
                alt="Expand sidebar"
                width={24}
                height={24}
                className="hidden dark:block"
              />
            </>
          ) : (
            <>
              <Image
                src="/panel-left-close_light.png"
                alt="Collapse sidebar"
                width={24}
                height={24}
                className="block dark:hidden"
              />
              <Image
                src="/panel-left-close_dark.png"
                alt="Collapse sidebar"
                width={24}
                height={24}
                className="hidden dark:block"
              />
            </>
          )}
        </button>
      </div>

      <CustomScrollArea className="custom-scrollbar relative z-10 flex-grow bg-white dark:bg-[#000000] scrollbar-thin scrollbar-thumb-[#E5E7EB] dark:scrollbar-thumb-[#2E2E2E] scrollbar-track-transparent">
        {!collapsed ? (
          <>
            {isSearching ? (
              <SkeletonSidebarContent categoryCount={2} />
            ) : (
              <>
                <Accordion
                  type="multiple"
                  className="w-full space-y-0 px-2 py-1"
                  value={expandedCategories}
                >
                  {visibleCategories.map((category) => {
                    const CategoryIcon = getCategoryIcon(category);
                    
                    // Check if this is an MCP category
                    const isEnumMCPCategory = Object.values(MCP_CATEGORY_LABELS).includes(category);
                    const isMcpCategory = mcpCategories.includes(category) || isEnumMCPCategory;
                    
                    // Disable drag & drop for main category headers (Tools, MCP, etc.)
                    // Only individual items within these categories should be draggable
                    const isDraggableCategory = false; // No main categories should be draggable
                    
                    return (
                      <AccordionItem
                        value={category}
                        key={category}
                        className={`overflow-hidden rounded-lg hover:bg-[#E5E7EB] dark:hover:bg-[#2E2E2E] ${
                          expandedCategories.includes(category)
                            ? 'bg-[#E5E7EB] dark:bg-[#2E2E2E] mb-2'
                            : 'hover:bg-[#E5E7EB] dark:hover:bg-[#2E2E2E]'
                        }`}
                        style={expandedCategories.includes(category) ? {
                          boxShadow: '0px 2px 12px 0px #0303030F'
                        } : {}}
                      >
                        <AccordionTrigger
                          className="font-[Satoshi] px-2 py-2 text-base font-semibold transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-700 hover:no-underline text-black dark:text-white"
                          onClick={() => handleCategoryToggle(category)}
                        >
                          <div 
                            className={`flex w-full items-center gap-2 ${isDraggableCategory ? 'cursor-grab' : 'cursor-default'}`}
                            draggable={isDraggableCategory}
                            onDragStart={isDraggableCategory ? (event) => {
                              // Create a category definition for dragging
                              const categoryDefinition: ComponentDefinition = {
                                name: `Category_${category}`,
                                display_name: getCategoryDisplayName(category),
                                description: `All tools from ${getCategoryDisplayName(category)} category`,
                                category: category,
                                icon: "Layers",
                                beta: false,
                                inputs: [
                                  {
                                    name: "input",
                                    display_name: "Input",
                                    input_types: ["Any"],
                                    is_handle: true,
                                    input_type: "handle",
                                    info: "Input data for the selected tool",
                                    required: false,
                                    is_list: false,
                                    real_time_refresh: false,
                                    advanced: false,
                                    value: null,
                                    options: null,
                                    visibility_rules: null,
                                    visibility_logic: "OR" as const,
                                    requirement_rules: null,
                                    requirement_logic: "OR" as const
                                  }
                                ],
                                outputs: [
                                  {
                                    name: "output",
                                    display_name: "Output",
                                    output_type: "Any"
                                  }
                                ],
                                is_valid: true,
                                path: `category.${category.toLowerCase()}`,
                                type: "CategorySelector"
                              };
                              
                              onDragStart(
                                event,
                                "CategorySelector",
                                categoryDefinition,
                                setShowOutputSchemaDialog,
                                setSelectedMCPTool,
                                setShowEnvKeysDialog,
                              );
                            } : undefined}
                          >
                            <div className={`p-1.5 rounded-md transition-all duration-200 ${
                              expandedCategories.includes(category)
                                ? 'bg-white dark:bg-[#1E1E1E]'
                                : 'group-hover:bg-white dark:group-hover:bg-[#1E1E1E]'
                            }`}>
                              <CategoryIcon className="h-6 w-6 transition-colors duration-200 text-black dark:text-white" />
                            </div>
                            <span className="text-black dark:text-white font-[Satoshi] flex-1">
                              {getCategoryDisplayName(category)}
                            </span>
                            {isDraggableCategory && <Grip className="h-4 w-4 text-gray-400 dark:text-gray-500" />}
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="accordion-content-animation px-1 pb-2">
                          <div className="max-h-[280px] space-y-2 overflow-y-auto pt-1 px-1">
                        {(() => {
                            // Check if this is an MCP category from our enum
                            const isEnumMCPCategory = Object.values(MCP_CATEGORY_LABELS).includes(category);
                            
                            // For enum categories, we need to check the enum key, not the display label
                            let categoryToCheck = category;
                            if (isEnumMCPCategory) {
                              // Find the enum key that corresponds to this label
                              const enumKey = Object.keys(MCP_CATEGORY_LABELS).find(
                                key => MCP_CATEGORY_LABELS[key as MCPCategory] === category
                              );
                              if (enumKey) {
                                categoryToCheck = enumKey;
                              }
                            }
                            
                            // Handle loading state for MCP categories with skeleton view
                            if ((mcpCategories.includes(category) || isEnumMCPCategory) && !loadedMcpCategories.has(categoryToCheck)) {
                              return (
                                <div className="space-y-2">
                                  <SkeletonMCPGroup toolCount={3} />
                                  <SkeletonMCPGroup toolCount={2} />
                                  <SkeletonMCPGroup toolCount={4} />
                                </div>
                              );
                            }
                            
                            // Handle special case for Agents category
                            if (category === "Agents") {
                            return (
                              <div className="max-h-[250px] overflow-y-auto custom-scrollbar">
                                <div className="space-y-2">
                                  {isLoadingAgents ? (
                                    <div className="space-y-2">
                                      {[1, 2, 3, 4].map((index) => (
                                        <SkeletonCard
                                          key={index}
                                          className="bg-white dark:bg-[#1A1A1A] border border-transparent dark:border-gray-800"
                                          descriptionLines={Math.random() > 0.5 ? 1 : 2}
                                        />
                                      ))}
                                    </div>
                                  ) : agents.length === 0 ? (
                                    <div className="text-center p-4 text-sm text-gray-500 dark:text-gray-400 font-[Satoshi]">
                                      No agents found
                                    </div>
                                  ) : (
                                    agents
                                    .filter(agent => {
                                      // Apply search filter if there's a search term
                                      if (!searchTerm) return true;
                                      const searchLower = searchTerm.toLowerCase();
                                      return (
                                        agent.name.toLowerCase().includes(searchLower) ||
                                        agent.description.toLowerCase().includes(searchLower) ||
                                        (agent.category && agent.category.toLowerCase().includes(searchLower))
                                      );
                                    })
                                    .map((agent) => (
                                      <div
                                        key={agent.id}
                                        className="relative cursor-grab rounded-lg bg-white dark:bg-[#1A1A1A] p-2.5 text-sm border border-transparent dark:border-gray-800"
                                        onDragStart={(event) =>
                                          onDragStart(
                                            event,
                                            `agent-${agent.id}`,
                                            {
                                              name: `agent-${agent.id}`,
                                              display_name: agent.name,
                                              description: agent.description,
                                              category: "Agents",
                                              icon: "Users",
                                              beta: false,
                                              path: `agent.${agent.id}`,
                                              inputs: [
                                                {
                                                  name: "input",
                                                  display_name: "Input",
                                                  info: "Input message to send to the agent",
                                                  input_type: "string",
                                                  required: true,
                                                  is_handle: true
                                                }
                                              ],
                                              outputs: [
                                                {
                                                  name: "response",
                                                  display_name: "Response",
                                                  output_type: "string"
                                                }
                                              ],
                                              is_valid: true,
                                              type: "Agent",
                                              agent_info: agent, // Store the full agent data
                                            },
                                            setShowOutputSchemaDialog,
                                            setSelectedMCPTool,
                                            setShowEnvKeysDialog,
                                          )
                                        }
                                        draggable
                                      >
                                        <div className="flex items-center gap-2 mb-1">
                                          <div className="p-1 rounded bg-[#F4F4F5] dark:bg-[#2E2E2E] flex-shrink-0">
                                            <Users className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                          </div>
                                          <span className="text-sm font-medium text-black dark:text-white font-[Satoshi] flex-1 break-words leading-tight">
                                            {agent.name}
                                          </span>
                                          <div className="ml-auto flex-shrink-0">
                                            <Grip className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                                          </div>
                                        </div>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 leading-tight line-clamp-2 font-[Satoshi]">
                                          {agent.description}
                                        </p>
                                      </div>
                                    ))
                              )}
                            </div>
                          </div>
                        );
                          }

                          // Handle special case for Workflows category
                          if (category === "Workflows") {
                            return (
                              <div className="max-h-[250px] overflow-y-auto custom-scrollbar">
                                <div className="space-y-2">
                                  {isLoadingWorkflows ? (
                                    <div className="space-y-2">
                                      {[1, 2, 3].map((index) => (
                                        <SkeletonCard
                                          key={index}
                                          className="bg-white dark:bg-[#1A1A1A] border border-transparent dark:border-gray-800"
                                          descriptionLines={Math.random() > 0.6 ? 1 : 2}
                                        />
                                      ))}
                                    </div>
                                  ) : workflowComponents.length === 0 ? (
                                    <div className="text-center p-4 text-sm text-gray-500 dark:text-gray-400 font-[Satoshi]">
                                      No workflows found
                                    </div>
                                  ) : (
                                    workflowComponents
                                    .filter(workflow => {
                                      // Apply search filter if there's a search term
                                      if (!searchTerm) return true;
                                      const searchLower = searchTerm.toLowerCase();
                                      return (
                                        workflow.name.toLowerCase().includes(searchLower) ||
                                        (workflow.description && workflow.description.toLowerCase().includes(searchLower))
                                      );
                                    })
                                    .map((workflow) => (
                                      <div
                                        key={workflow.id}
                                        className="relative cursor-grab rounded-lg bg-white dark:bg-[#1A1A1A] p-2.5 text-sm border border-transparent dark:border-gray-800"
                                        onDragStart={(event) =>
                                          onDragStart(
                                            event,
                                            `workflow-${workflow.id}`,
                                            {
                                              name: `workflow-${workflow.id}`,
                                              display_name: workflow.name,
                                              description: workflow.description,
                                              category: "Workflows",
                                              icon: "Workflow",
                                              beta: false,
                                              path: `workflow.${workflow.id}`,
                                              inputs: generateWorkflowInputs(workflow.start_nodes || []),
                                              outputs: [
                                                {
                                                  name: "execution_status",
                                                  display_name: "Execution Status",
                                                  output_type: "string"
                                                },
                                                {
                                                  name: "workflow_execution_id",
                                                  display_name: "Execution ID",
                                                  output_type: "string"
                                                },
                                                {
                                                  name: "message",
                                                  display_name: "Message",
                                                  output_type: "string"
                                                }
                                              ],
                                              is_valid: true,
                                              type: "Workflow",
                                              workflow_info: workflow, // Store the full workflow data
                                            } as ComponentDefinition,
                                            setShowOutputSchemaDialog,
                                            setSelectedMCPTool,
                                            setShowEnvKeysDialog,
                                          )
                                        }
                                        draggable
                                      >
                                        <div className="flex items-center gap-2 mb-1">
                                          <div className="p-1 rounded bg-[#F4F4F5] dark:bg-[#2E2E2E] flex-shrink-0">
                                            <Workflow className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                          </div>
                                          <span className="text-sm font-medium text-black dark:text-white font-[Satoshi] flex-1 break-words leading-tight">
                                            {workflow.name}
                                          </span>
                                          <div className="ml-auto flex-shrink-0">
                                            <Grip className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                                          </div>
                                        </div>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 leading-tight line-clamp-2 font-[Satoshi]">
                                          {workflow.description}
                                        </p>
                                      </div>
                                    ))
                                )}
                              </div>
                            </div>
                          );
                          }
                          
                          // Check if this is an MCP category from our enum and show "No MCP Added" message if empty
                          const isMCPEnumCategory = Object.values(MCP_CATEGORY_LABELS).includes(category);
                          if (isMCPEnumCategory) {
                            const components = Object.values(filteredData[category] || {});
                            if (components.length === 0) {
                              return (
                                <div className="text-center p-4 text-sm text-gray-500 dark:text-gray-400">
                                  <div className="mb-2">
                                    <Store className="h-6 mx-auto text-gray-400 dark:text-gray-500" />
                                  </div>
                                  <p className="font-medium mb-1 font-[Satoshi]">No MCP Added</p>
                                  <p className="text-xs mb-3 font-[Satoshi]">
                                    Go to marketplace and add MCP in this {category.toLowerCase()}
                                  </p>
                                  <button
                                    onClick={() => window.open(process.env.NEXT_PUBLIC_MARKETPLACE_URL, '_blank')}
                                    className="inline-flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md font-[Satoshi]"
                                  >
                                    <Search className="h-3 w-3" />
                                    Explore more
                                  </button>
                                </div>
                              );
                            }
                          }
                          
                          // Default handling for regular component categories
                          // Group components by MCP server name
                          const groupedByMCP: Record<string, ComponentDefinition[]> = {};

                          // Get all components for this category
                          const components = Object.values(filteredData[category] || {}).sort((a, b) =>
                            a.display_name.localeCompare(b.display_name),
                          );

                          // Group components by MCP server name (first part of display_name before " - ")
                          components.forEach((compDef) => {
                            // Check if this is an MCP component
                            const isMCPComponent = compDef.type === "MCP";

                            // Check if this is an MCP tool (has display_name in format "MCPName - ToolName")
                            const isMCPTool =
                              isMCPComponent && compDef.display_name.includes(" - ");

                            // Check if this is an MCP server (has mcp_info.tool_name === "server")
                            const isMCPServer =
                              isMCPComponent &&
                              compDef.mcp_info &&
                              compDef.mcp_info.tool_name === "server";

                            if (isMCPTool) {
                              // Extract MCP name from display_name (format: "MCPName - ToolName")
                              const mcpName = compDef.display_name.split(" - ")[0];
                              if (!groupedByMCP[mcpName]) {
                                groupedByMCP[mcpName] = [];
                              }
                              groupedByMCP[mcpName].push(compDef);
                            } else if (isMCPServer) {
                              // For MCP server components, use the display_name as the mcpName
                              const mcpName = compDef.display_name;
                              if (!groupedByMCP[mcpName]) {
                                groupedByMCP[mcpName] = [];
                              }
                              groupedByMCP[mcpName].push(compDef);
                            } else {
                              // For non-MCP components, use a special key
                              if (!groupedByMCP["__other"]) {
                                groupedByMCP["__other"] = [];
                              }
                              groupedByMCP["__other"].push(compDef);
                            }
                          });

                          // Initialize expanded MCP groups for this category if not already set
                          if (searchTerm.trim() && !expandedMCPGroups[category]) {
                            // Auto-expand all MCP groups when searching
                            const mcpNames = Object.keys(groupedByMCP).filter(
                              (name) => name !== "__other",
                            );
                            if (mcpNames.length > 0) {
                              setExpandedMCPGroups((prev) => ({
                                ...prev,
                                [category]: mcpNames,
                              }));
                            }
                          }

                          // Get the list of expanded MCP groups for this category
                          const expandedGroups = expandedMCPGroups[category] || [];

                          // Get all MCP servers (excluding __other)
                          const mcpServers = Object.entries(groupedByMCP).filter(([mcpName]) => mcpName !== "__other");
                          
                          // Initialize pagination for this category
                          initializePaginationForCategory(category, mcpServers.length);
                          
                          // Get pagination state for this category
                          const categoryPaginationState = getCategoryPaginationState(category);
                          
                          // Show all loaded MCP servers (no slicing since we load them dynamically)
                          const visibleMcpServers = mcpServers;
                          const hasMoreServers = categoryPaginationState.hasMore;
                          
                          // Check if we have loaded all items (total loaded equals total count from API)
                          const hasLoadedAllItems = categoryPaginationState.totalCount > 0 && 
                            mcpServers.length >= categoryPaginationState.totalCount;
                          
                          const showReadMore = mcpServers.length > 0 && hasMoreServers && !hasLoadedAllItems;

                          return (
                            <>
                              {/* Render MCP groups first */}
                              {visibleMcpServers.map(([mcpName, mcpComponents]) => (
                                  <div key={mcpName} className="mb-2">
                                    {/* Parent container with black background that wraps header and tools */}
                                    <div
                                      className="rounded-lg transition-colors duration-200 bg-[#F3F4F6] dark:bg-black"
                                      style={expandedGroups.includes(mcpName) ? {
                                        boxShadow: '0px 2px 12px 0px #0303030F',
                                        padding: '8px'
                                      } : {
                                        padding: '8px'
                                      }}
                                    >
                                      {/* MCP Server Name as collapsible header */}
                                      <div
                                        className={`group text-black dark:text-white flex cursor-pointer items-center justify-between text-sm font-medium ${
                                          expandedGroups.includes(mcpName)
                                            ? 'p-2 mb-2 rounded-lg'
                                            : 'p-2 rounded-lg'
                                        }`}
                                        onClick={() => handleMCPGroupToggle(category, mcpName)}
                                        draggable
                                        onDragStart={(event) => {
                                          // Create a category definition for dragging the MCP server
                                          const categoryDefinition: ComponentDefinition = {
                                            name: `MCP_${mcpName}`,
                                            display_name: mcpName,
                                            description: `All tools from ${mcpName} MCP server`,
                                            category: category,
                                            icon: "Layers",
                                            beta: false,
                                            inputs: [
                                              {
                                                name: "input",
                                                display_name: "Input",
                                                input_types: ["Any"],
                                                is_handle: true,
                                                input_type: "handle",
                                                info: "Input data for the selected tool",
                                                required: false,
                                                is_list: false,
                                                real_time_refresh: false,
                                                advanced: false,
                                                value: null,
                                                options: null,
                                                visibility_rules: null,
                                                visibility_logic: "OR" as const,
                                                requirement_rules: null,
                                                requirement_logic: "OR" as const
                                              }
                                            ],
                                            outputs: [
                                              {
                                                name: "output",
                                                display_name: "Output",
                                                output_type: "Any"
                                              }
                                            ],
                                            is_valid: true,
                                            path: `mcp.${mcpName.toLowerCase()}`,
                                            type: "CategorySelector"
                                          };
                                          
                                          // Include the available tools in the drag data
                                          const dragData = {
                                            nodeType: "CategorySelector",
                                            definition: categoryDefinition,
                                            availableTools: mcpComponents // Pass the actual tools
                                          };
                                          
                                          // Set the drag data with tools included
                                          event.dataTransfer.setData("application/reactflow", JSON.stringify(dragData));
                                          event.dataTransfer.effectAllowed = "move";
                                        }}
                                      >
                                        <div className="flex items-center gap-2">
                                          {/* Show MCPLogo by default, Grip icon on hover */}
                                          <div className="relative h-8 w-8 flex-shrink-0">
                                            <MCPLogo mcpName={mcpName} mcpComponents={mcpComponents} className="group-hover:opacity-0 transition-opacity duration-200" />
                                            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                              <div className="bg-[#E5E7EB] dark:bg-[#2E2E2E] rounded-md p-1.5">
                                                <Grip className="h-4.5 w-4.5 text-gray-400 dark:text-gray-500 cursor-grab" />
                                              </div>
                                            </div>
                                          </div>
                                          <span className="font-[Satoshi]">{mcpName}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <div className="p-1 rounded-md">
                                            <ChevronRight
                                              className={`h-4 w-4 text-black dark:text-white transition-all duration-200 ${expandedGroups.includes(mcpName) ? 'rotate-90' : ''}`}
                                            />
                                          </div>
                                        </div>
                                      </div>

                                      {/* Components from this MCP - only show if expanded */}
                                      {expandedGroups.includes(mcpName) && (
                                        <div className="max-h-[200px] overflow-y-auto space-y-2 custom-scrollbar">
                                          {mcpComponents.map((compDef: ComponentDefinition) => (
                                            <div
                                              key={compDef.name}
                                              className="relative cursor-grab rounded-md bg-white dark:bg-[#1A1A1A] p-2 text-[13px] font-medium border border-transparent dark:border-gray-800"
                                              onDragStart={(event) =>
                                                onDragStart(
                                                  event,
                                                  compDef.name,
                                                  compDef,
                                                  setShowOutputSchemaDialog,
                                                  setSelectedMCPTool,
                                                  setShowEnvKeysDialog,
                                                )
                                              }
                                              draggable
                                            >
                                              <div className="flex items-center gap-1.5 mb-1">
                                                <div className="p-1 rounded bg-[#F4F4F5] dark:bg-[#2E2E2E] flex-shrink-0">
                                                  <CategoryIcon className="h-3 w-3 text-purple-600 dark:text-purple-400" />
                                                </div>
                                                <span className="text-[13px] font-medium text-black dark:text-white font-[Satoshi] flex-1 break-words leading-tight">
                                                  {compDef.display_name.split(" - ")[1] || compDef.display_name}
                                                </span>
                                                <div className="ml-auto flex-shrink-0">
                                                  <Grip className="h-3 w-3 text-gray-400 dark:text-gray-500" />
                                                </div>
                                              </div>
                                              <p className="text-[11px] font-[Satoshi] leading-tight line-clamp-2 sidebar-desc">
                                                {compDef.description}
                                              </p>
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                ))}

                              {/* Category-level Read More Button */}
                              {showReadMore && (
                                <div className="mt-3">
                                  <button
                                    onClick={() => handleCategoryLoadMore(category, mcpServers.length)}
                                    disabled={categoryPaginationState.isLoading}
                                    className="w-full flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-[Satoshi]"
                                  >
                                    {categoryPaginationState.isLoading ? (
                                      <>
                                        <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                                        Loading...
                                      </>
                                    ) : (
                                      <>
                                        <ChevronRight className="h-4 w-4 rotate-90" />
                                        Read more
                                      </>
                                    )}
                                  </button>
                                </div>
                              )}

                              {/* Render other components */}
                              {groupedByMCP["__other"] && groupedByMCP["__other"].length > 0 && (
                                <div className="max-h-[250px] overflow-y-auto space-y-2 custom-scrollbar">
                                  {groupedByMCP["__other"].map((compDef: ComponentDefinition) => (
                                    <div
                                      key={compDef.name}
                                      className="relative cursor-grab rounded-lg bg-white dark:bg-[#1A1A1A] p-2.5 text-sm border border-transparent dark:border-gray-800"
                                      onDragStart={(event) =>
                                        onDragStart(
                                          event,
                                          compDef.name,
                                          compDef,
                                          setShowOutputSchemaDialog,
                                          setSelectedMCPTool,
                                          setShowEnvKeysDialog,
                                        )
                                      }
                                      draggable
                                    >
                                      <div className="flex items-center gap-2 mb-1">
                                        <div className="p-1 rounded bg-[#F4F4F5] dark:bg-[#2E2E2E] flex-shrink-0">
                                          <CategoryIcon className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                        </div>
                                        <span className="text-sm font-medium text-black dark:text-white font-[Satoshi] flex-1">
                                          {compDef.display_name}
                                        </span>
                                        <div className="ml-auto">
                                          <Grip className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                                        </div>
                                      </div>
                                      <p className="text-xs font-[Satoshi] leading-tight line-clamp-2 sidebar-desc">
                                        {compDef.description}
                                      </p>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </>
                          );
                        })()}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
                </Accordion>

                {visibleCategories.length === 0 && (
                  <div className="border-brand-stroke bg-brand-card-hover text-brand-secondary-font dark:border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font m-4 rounded-lg border p-6 text-center text-sm">
                    <Search className="text-brand-primary/70 dark:text-brand-secondary/70 mx-auto mb-3 h-6 w-6" />
                    <span className="font-[Satoshi]">No components match your search.</span>
                  </div>
                )}
              </>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center gap-3 px-1 py-6">
            {visibleCategories.map((category) => {
              const CategoryIcon = getCategoryIcon(category);
              return (
                <div
                  key={category}
                  className="group mb-1 flex flex-col items-center"
                  title={getCategoryDisplayName(category)}
                >
                  <button
                    className={`flex h-10 w-10 items-center justify-center rounded-full shadow-sm ${
                      category.toLowerCase() === "io" || category.toLowerCase() === "data"
                        ? "bg-brand-primary/10 text-brand-primary dark:bg-brand-primary/20 dark:text-brand-secondary"
                        : "bg-brand-card-hover text-brand-primary-font dark:bg-brand-card dark:text-brand-white-text"
                    } transition-all hover:scale-110 hover:shadow-md`}
                    onClick={async () => {
                      if (onToggleCollapse) onToggleCollapse();
                      setTimeout(async () => await handleCategoryToggle(category), 300);
                    }}
                    aria-label={`Open ${getCategoryDisplayName(category)} category`}
                  >
                    <CategoryIcon className="h-5 w-5" />
                  </button>
                  <span className="text-brand-secondary-font mt-1 text-[10px] font-medium opacity-80 font-[Satoshi]">
                    {getCategoryDisplayName(category).substring(0, 3)}
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </CustomScrollArea>

      {/* My Account Section */}
      {!collapsed && (
        <div className="bg-[#F4F4F5] dark:bg-[#2E2E2E] px-4 py-4">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
              <User className="h-6 text-gray-700 dark:text-white" />
            </div>
            <span className="text-base font-bold text-gray-900 dark:text-white font-[Satoshi] flex-1 text-left">My Account</span>
            <ChevronRight className="h-6 w-6 text-gray-900 dark:text-white" />
          </div>
        </div>
      )}

      {/* Output Schema Dialog */}
      {selectedMCPTool && (
        <AddOutputSchemaDialog
          open={showOutputSchemaDialog}
          onOpenChange={setShowOutputSchemaDialog}
          toolName={selectedMCPTool.name}
          onSubmit={handleOutputSchemaSubmit}
        />
      )}

      {/* Environment Keys Dialog */}
      {selectedMCPTool && selectedMCPTool.definition.env_keys && (
        <AddEnvKeysDialog
          open={showEnvKeysDialog}
          onOpenChange={setShowEnvKeysDialog}
          toolName={selectedMCPTool.name}
          envKeys={selectedMCPTool.definition.env_keys}
          onSubmit={handleEnvKeysSubmit}
        />
      )}
    </aside>
  );
});

