✅ I want to implement a dynamic "Read More" functionality for MCP items fetched via API.

🔁 Current Behavior:

When any MCP-related item (like Tools or category dropdown) is opened, the app currently calls:
GET /api/v1/mcps?page=1&page_size=10

It loads all data at once, and then shows only 2 items initially.

On clicking "Read More", it reveals 2 more items from the already fetched list.

🔄 New Desired Behavior:

Replace the current static data load with paginated API calls.

On dropdown open (e.g., Tools), it should now call:
GET /api/v1/mcps?page=1&page_size=2

On each "Read More" click, it should call the next page, like:
GET /api/v1/mcps?page=2&page_size=2

Append new items to the list and display them along with previously loaded ones.

Continue this process until all items are fetched (based on total count or empty response).

📂 Category-specific API Behavior:

For category filters like Notifications & Alerts, the API call should be:
GET /api/v1/mcps?page=1&page_size=2&component_category=notifications_alerts

Similarly, this paginated fetch should apply to all categories using their respective component_category query param.

👉 Please implement this dynamic fetch-and-append logic for all MCP tool dropdowns and categories.
note : not make any chages in ui and other functionality only add this functinality 

dont want to call "
GET /api/v1/mcps?page=1&page_size=10"

api structure "{
    "data": [
        {},
        {}
         ],
    "metadata": {
        "total": 8,
        "totalPages": 1,
        "currentPage": 1,
        "pageSize": 10,
        "hasNextPage": false,
        "hasPreviousPage": false
    }
}"