import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import DeletableEdge from '../DeletableEdge';
import { EdgeProps } from 'reactflow';

// Mock the reactflow components
jest.mock('reactflow', () => ({
  getBezierPath: jest.fn(() => ['M 0 0 L 100 100', 50, 50]),
  EdgeLabelRenderer: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useReactFlow: jest.fn(() => ({
    setEdges: jest.fn(),
  })),
}));

// Mock the Trash2 icon
jest.mock('lucide-react', () => ({
  Trash2: () => <div data-testid="trash-icon">🗑️</div>,
}));

describe('DeletableEdge Tool Connection Visual Distinction', () => {
  const createMockEdgeProps = (
    sourceHandle: string = 'output_data',
    targetHandle: string = 'input_data'
  ): EdgeProps => ({
    id: 'edge-1',
    source: 'node-1',
    target: 'node-2',
    sourceHandle,
    targetHandle,
    sourceX: 0,
    sourceY: 0,
    targetX: 100,
    targetY: 100,
    sourcePosition: 'right' as any,
    targetPosition: 'left' as any,
    style: {},
    markerEnd: undefined,
    markerStart: undefined,
    interactionWidth: 20,
    data: {},
    selected: false,
  });

  const createMockToolConnection = (): EdgeProps => 
    createMockEdgeProps('output_data', 'tool_1');

  const createMockRegularConnection = (): EdgeProps => 
    createMockEdgeProps('output_data', 'input_data');

  test('tool connections render with dashed orange styling', () => {
    const toolConnection = createMockToolConnection();
    render(<DeletableEdge {...toolConnection} />);
    
    const edge = screen.getByTestId('edge-tool-connection');
    expect(edge).toHaveStyle('stroke: rgb(245, 158, 11)');
    expect(edge).toHaveStyle('stroke-dasharray: 5,5');
  });

  test('regular connections render with solid blue styling', () => {
    const regularConnection = createMockRegularConnection();
    render(<DeletableEdge {...regularConnection} />);
    
    const edge = screen.getByTestId('edge-regular-connection');
    expect(edge).toHaveStyle('stroke: rgb(59, 130, 246)');
    expect(edge).toHaveStyle('stroke-dasharray: none');
  });

  test('tool connection detection from handle names', () => {
    // Test the detection logic directly
    const isToolConnection1 = detectToolConnection('tool_1');
    expect(isToolConnection1).toBe(true);
    
    const isToolConnection2 = detectToolConnection('tool_2');
    expect(isToolConnection2).toBe(true);
    
    const isRegularConnection = detectToolConnection('input_data');
    expect(isRegularConnection).toBe(false);
  });

  test('edge classes are applied correctly', () => {
    const toolConnection = createMockToolConnection();
    render(<DeletableEdge {...toolConnection} />);
    
    const edge = screen.getByTestId('edge-tool-connection');
    
    // Should have tool connection class
    expect(edge).toHaveClass('tool-connection');
  });
});

// Helper function that should be implemented in the actual component
function detectToolConnection(targetHandle: string): boolean {
  return targetHandle?.startsWith('tool_') ?? false;
}
