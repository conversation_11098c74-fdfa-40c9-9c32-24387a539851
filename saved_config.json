{"success": true, "message": "Workflow Untitled Workflow 3 retrieved successfully", "workflow": {"id": "16ce1702-c1e1-4825-850c-f8adb3b0311f", "name": "Untitled Workflow 3", "description": "Untitled_Workflow_3", "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/02b35024-ffcc-485a-bbca-e47669788aec.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/de7b479c-aaa7-41dd-92da-6cb64f6b9720.json", "start_nodes": [{"field": "input_1", "type": "string", "transition_id": "transition-CombineTextComponent-1753349654654"}, {"field": "input_data", "type": "multiline", "transition_id": "transition-ConditionalNode-1753355485310"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "user_ids": ["c1454e90-09ac-40f2-bde2-833387d7b645"], "owner_type": "user", "workflow_template_id": null, "template_owner_id": null, "is_imported": false, "version": "1.0.0", "visibility": "private", "category": null, "tags": null, "status": "active", "is_changes_marketplace": false, "is_customizable": true, "auto_version_on_update": false, "created_at": "2025-07-24T09:19:09.620356", "updated_at": "2025-07-24T12:48:55.656846", "available_nodes": [{"name": "ConditionalNode", "display_name": "Switch-Case Router", "type": "component", "transition_id": "transition-ConditionalNode-1753355485310", "label": "Switch-Case Router"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753349654654", "label": "Combine Text"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753349579957", "label": "Merge Data"}], "is_updated": true, "source_version_id": null}}