{"name": "Untitled Workflow 3", "description": "Untitled_Workflow_3", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"ConditionalNode-1753355485310_input_data": {"node_id": "ConditionalNode-1753355485310", "node_name": "Switch-Case Router", "input_name": "input_data", "connected_to_start": true, "required": true, "input_type": "multiline", "options": null}}}}, "width": 208, "height": 141, "selected": false, "dragging": false, "positionAbsolute": {"x": 100, "y": 100}}, {"id": "MergeDataComponent-1753349579957", "type": "WorkflowNode", "position": {"x": 800, "y": 400}, "data": {"label": "Merge Data", "type": "component", "originalType": "MergeDataComponent", "definition": {"name": "MergeDataComponent", "display_name": "Merge Data", "description": "Combines multiple dictionaries or lists.", "category": "Processing", "icon": "Combine", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main data structure to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "merge_strategy", "display_name": "Merge Strategy (Dicts)", "info": "How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Overwrite", "options": ["Overwrite", "Deep Merge", "Error on Conflict", "Aggregate", "Structured Compose"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_1", "display_name": "Output Key 1", "info": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_2", "display_name": "Output Key 2", "info": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_3", "display_name": "Output Key 3", "info": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_4", "display_name": "Output Key 4", "info": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_5", "display_name": "Output Key 5", "info": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_6", "display_name": "Output Key 6", "info": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_7", "display_name": "Output Key 7", "info": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_8", "display_name": "Output Key 8", "info": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_9", "display_name": "Output Key 9", "info": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_10", "display_name": "Output Key 10", "info": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_11", "display_name": "Output Key 11", "info": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Data structure 1 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Data structure 2 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Data structure 3 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Data structure 4 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Data structure 5 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Data structure 6 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Data structure 7 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Data structure 8 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Data structure 9 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Data structure 10 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Merged Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.mergedatacomponent", "interface_issues": []}, "config": {"main_input": {}, "num_additional_inputs": 0, "merge_strategy": "Structured Compose", "output_key_1": "toc_key", "output_key_2": "redis_key", "output_key_3": "", "output_key_4": "", "output_key_5": "", "output_key_6": "", "output_key_7": "", "output_key_8": "", "output_key_9": "", "output_key_10": "", "output_key_11": "", "input_1": {}, "input_2": {}, "input_3": {}, "input_4": {}, "input_5": {}, "input_6": {}, "input_7": {}, "input_8": {}, "input_9": {}, "input_10": {}}}, "width": 208, "height": 194, "selected": false, "dragging": false, "style": {"opacity": 1}, "positionAbsolute": {"x": 800, "y": 400}}, {"id": "CombineTextComponent-1753349654654", "type": "WorkflowNode", "position": {"x": 1020, "y": -280}, "data": {"label": "Combine Text", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"main_input": "", "num_additional_inputs": 1, "separator": "", "input_1": "true", "input_2": "", "input_3": "", "input_4": "", "input_5": "", "input_6": "", "input_7": "", "input_8": "", "input_9": "", "input_10": ""}}, "width": 208, "height": 184, "selected": false, "dragging": false, "style": {"opacity": 1}, "positionAbsolute": {"x": 1020, "y": -280}}, {"id": "ConditionalNode-1753355485310", "type": "WorkflowNode", "position": {"x": 600, "y": 160}, "data": {"label": "Switch-Case Router", "type": "component", "originalType": "ConditionalNode", "definition": {"name": "ConditionalNode", "display_name": "Switch-Case Router", "description": "Evaluates multiple conditions and routes data to matching outputs", "category": "Logic", "icon": "GitBranch", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "Input data that will be routed when conditions match. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "source", "display_name": "Data Source", "info": "Select the data source for condition evaluation: 'node_output' uses connected input data, 'global_context' uses a global variable.", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "variable_name", "display_name": "Variable Name", "info": "Name of the global variable to evaluate conditions against. This variable should be available in the workflow context.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source", "field_value": "global_context", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "condition_1_operator", "display_name": "Condition 1 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_1_expected_value", "display_name": "Condition 1 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_1_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_1_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_conditions", "display_name": "Number of Additional Conditions", "info": "Number of additional conditions beyond the base 1 condition (0-9).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "evaluation_strategy", "display_name": "Evaluation Strategy", "info": "Strategy for handling multiple conditions: 'first_match' stops at the first true condition, 'all_matches' executes all true conditions in parallel.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "all_matches", "options": ["first_match", "all_matches"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_operator", "display_name": "Condition 2 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_expected_value", "display_name": "Condition 2 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}, {"field_name": "condition_2_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_2_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_operator", "display_name": "Condition 3 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_expected_value", "display_name": "Condition 3 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}, {"field_name": "condition_3_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_3_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_operator", "display_name": "Condition 4 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_expected_value", "display_name": "Condition 4 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}, {"field_name": "condition_4_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_4_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_operator", "display_name": "Condition 5 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_expected_value", "display_name": "Condition 5 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}, {"field_name": "condition_5_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_5_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_operator", "display_name": "Condition 6 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_expected_value", "display_name": "Condition 6 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}, {"field_name": "condition_6_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_6_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_operator", "display_name": "Condition 7 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_expected_value", "display_name": "Condition 7 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}, {"field_name": "condition_7_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_7_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_operator", "display_name": "Condition 8 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_expected_value", "display_name": "Condition 8 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}, {"field_name": "condition_8_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_8_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_operator", "display_name": "Condition 9 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_expected_value", "display_name": "Condition 9 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}, {"field_name": "condition_9_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_9_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_operator", "display_name": "Condition 10 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_expected_value", "display_name": "Condition 10 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}, {"field_name": "condition_10_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_10_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "default", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.logic.conditionalnode", "interface_issues": []}, "config": {"input_data": "", "source": "node_output", "variable_name": "", "condition_1_operator": "is_empty", "condition_1_expected_value": "hello", "num_additional_conditions": 0, "evaluation_strategy": "all_matches", "condition_2_operator": "equals", "condition_2_expected_value": "", "condition_3_operator": "equals", "condition_3_expected_value": "", "condition_4_operator": "equals", "condition_4_expected_value": "", "condition_5_operator": "equals", "condition_5_expected_value": "", "condition_6_operator": "equals", "condition_6_expected_value": "", "condition_7_operator": "equals", "condition_7_expected_value": "", "condition_8_operator": "equals", "condition_8_expected_value": "", "condition_9_operator": "equals", "condition_9_expected_value": "", "condition_10_operator": "equals", "condition_10_expected_value": ""}}, "width": 208, "height": 184, "selected": false, "dragging": false, "style": {"opacity": 1}, "positionAbsolute": {"x": 600, "y": 160}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "id": "reactflow__edge-ConditionalNode-1753355485310condition_1-CombineTextComponent-1753349654654main_input", "source": "ConditionalNode-1753355485310", "sourceHandle": "condition_1", "target": "CombineTextComponent-1753349654654", "targetHandle": "main_input", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "id": "reactflow__edge-ConditionalNode-1753355485310default-MergeDataComponent-1753349579957main_input", "source": "ConditionalNode-1753355485310", "sourceHandle": "default", "target": "MergeDataComponent-1753349579957", "targetHandle": "main_input", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "id": "reactflow__edge-start-nodeflow-ConditionalNode-1753355485310input_data", "source": "start-node", "sourceHandle": "flow", "target": "ConditionalNode-1753355485310", "targetHandle": "input_data", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "id": "reactflow__edge-start-nodeflow-CombineTextComponent-1753349654654input_1", "source": "start-node", "sourceHandle": "flow", "target": "CombineTextComponent-1753349654654", "targetHandle": "input_1", "type": "default", "selected": false}]}, "start_node_data": [{"field": "input_data", "type": "multiline", "transition_id": "transition-ConditionalNode-1753355485310"}]}