"""
Test script for the DataComposeComponent in the Workflow Service.

This test verifies that the component follows modern patterns and works correctly
with both the new execute method and legacy build method.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock

from app.components.processing.data_compose import DataComposeComponent
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult


class TestDataComposeComponent:
    """Test cases for the DataComposeComponent."""

    def setup_method(self):
        """Set up test fixtures."""
        self.component = DataComposeComponent()

        # Create a mock workflow context
        self.context = Mock(spec=WorkflowContext)
        self.context.current_node_id = "test_node_1"
        self.context.node_outputs = {}
        self.context.log = Mock()

    def test_component_metadata(self):
        """Test that the component has correct metadata."""
        assert self.component.name == "DataComposeComponent"
        assert self.component.display_name == "Compose Data"
        assert self.component.description == "Composes multiple data structures into a structured dictionary with custom keys."
        assert self.component.category == "Processing"
        assert self.component.icon == "Package"
        assert self.component.beta is False

    def test_component_inputs(self):
        """Test that the component has the correct inputs."""
        inputs = self.component.inputs
        input_names = [inp.name for inp in inputs]
        
        # Should have main_input, num_additional_inputs, output keys, and additional inputs
        assert "main_input" in input_names
        assert "num_additional_inputs" in input_names
        
        # Should have output keys for all possible inputs
        for i in range(1, 12):  # 1 main + 10 additional + 1 extra
            assert f"output_key_{i}" in input_names
            
        # Should have additional input fields
        for i in range(1, 11):  # 10 additional inputs
            assert f"input_{i}" in input_names

    def test_component_outputs(self):
        """Test that the component has the correct outputs."""
        outputs = self.component.outputs
        assert len(outputs) == 1
        assert outputs[0].name == "output_data"
        assert outputs[0].display_name == "Composed Data"
        assert outputs[0].output_type == "dict"

    @pytest.mark.asyncio
    async def test_execute_basic_composition(self):
        """Test execute method with basic data composition."""
        # Mock input data
        main_data = {"name": "Alice", "age": 30}
        additional_data_1 = {"city": "New York", "country": "USA"}
        additional_data_2 = {"job": "Engineer", "salary": 75000}

        self.context.node_outputs = {
            "test_node_1": {
                "main_input": main_data,
                "num_additional_inputs": 2,
                "input_1": additional_data_1,
                "input_2": additional_data_2,
                "output_key_1": "person",
                "output_key_2": "location",
                "output_key_3": "employment"
            }
        }

        result = await self.component.execute(self.context)

        assert isinstance(result, NodeResult)
        assert result.is_success() is True
        
        output_data = result.outputs["output_data"]
        assert isinstance(output_data, dict)
        assert len(output_data) == 3
        assert output_data["person"] == main_data
        assert output_data["location"] == additional_data_1
        assert output_data["employment"] == additional_data_2

    @pytest.mark.asyncio
    async def test_execute_with_default_keys(self):
        """Test execute method with default output keys."""
        # Mock input data
        main_data = {"item": "laptop"}
        additional_data = {"price": 999.99}

        self.context.node_outputs = {
            "test_node_1": {
                "main_input": main_data,
                "num_additional_inputs": 1,
                "input_1": additional_data
                # No custom output keys provided - should use defaults
            }
        }

        result = await self.component.execute(self.context)

        assert result.is_success() is True
        output_data = result.outputs["output_data"]
        assert output_data["data_1"] == main_data  # Default key for main input
        assert output_data["data_2"] == additional_data  # Default key for input_1

    @pytest.mark.asyncio
    async def test_execute_missing_main_input(self):
        """Test execute method with missing main input."""
        self.context.node_outputs = {
            "test_node_1": {
                "num_additional_inputs": 1,
                "input_1": {"some": "data"}
            }
        }

        result = await self.component.execute(self.context)

        assert result.is_error() is True
        assert "Main input is missing" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_with_none_additional_inputs(self):
        """Test execute method with None additional inputs (should be skipped)."""
        main_data = {"main": "data"}

        self.context.node_outputs = {
            "test_node_1": {
                "main_input": main_data,
                "num_additional_inputs": 2,
                "input_1": {"additional": "data"},
                "input_2": None  # This should be skipped
            }
        }

        result = await self.component.execute(self.context)

        assert result.is_success() is True
        output_data = result.outputs["output_data"]
        assert len(output_data) == 2  # Only main_input and input_1
        assert "data_1" in output_data
        assert "data_2" in output_data
        assert "data_3" not in output_data  # input_2 was None, so no data_3

    @pytest.mark.asyncio
    async def test_execute_max_inputs_limit(self):
        """Test execute method respects maximum inputs limit."""
        main_data = {"main": "data"}

        self.context.node_outputs = {
            "test_node_1": {
                "main_input": main_data,
                "num_additional_inputs": 15,  # Exceeds MAX_ADDITIONAL_INPUTS (10)
            }
        }

        result = await self.component.execute(self.context)

        assert result.is_success() is True
        # Should be limited to MAX_ADDITIONAL_INPUTS
        # Only main_input should be present since no additional inputs provided
        output_data = result.outputs["output_data"]
        assert len(output_data) == 1
        assert "data_1" in output_data

    def test_build_method_basic_composition(self):
        """Test legacy build method with basic composition."""
        main_data = {"name": "Bob"}
        additional_data = {"age": 25}

        result = self.component.build(
            main_input=main_data,
            num_additional_inputs=1,
            input_1=additional_data,
            output_key_1="user",
            output_key_2="details"
        )

        assert "output_data" in result
        output_data = result["output_data"]
        assert output_data["user"] == main_data
        assert output_data["details"] == additional_data

    def test_build_method_missing_main_input(self):
        """Test legacy build method with missing main input."""
        result = self.component.build(
            num_additional_inputs=1,
            input_1={"some": "data"}
        )

        assert "error" in result
        assert "Main input is missing" in result["error"]

    def test_get_input_value_method(self):
        """Test the get_input_value helper method."""
        # Mock workflow data structure
        self.context.workflow_data = {
            "nodes": {
                "test_node_1": {
                    "data": {
                        "config": {
                            "test_input": "config_value"
                        }
                    }
                }
            }
        }

        # Test getting value from config
        value = self.component.get_input_value("test_input", self.context, "default")
        assert value == "config_value"

        # Test getting default value
        value = self.component.get_input_value("nonexistent", self.context, "default")
        assert value == "default"

        # Test getting value from node outputs
        self.context.node_outputs = {
            "test_node_1": {
                "output_input": "output_value"
            }
        }
        value = self.component.get_input_value("output_input", self.context, "default")
        assert value == "output_value"


if __name__ == "__main__":
    # Run a simple test
    async def run_test():
        component = DataComposeComponent()
        context = Mock(spec=WorkflowContext)
        context.current_node_id = "test_node"
        context.log = Mock()
        context.node_outputs = {
            "test_node": {
                "main_input": {"name": "Test"},
                "num_additional_inputs": 1,
                "input_1": {"value": 42},
                "output_key_1": "info",
                "output_key_2": "data"
            }
        }
        
        # Execute
        result = await component.execute(context)
        print(f"Status: {result.status}")
        print(f"Outputs: {result.outputs}")
        print(f"Execution time: {result.execution_time}")
    
    asyncio.run(run_test())
