"""
DataComposeComponent - Component for composing multiple data structures into a structured dictionary.

This component creates a new dictionary where each input becomes a separate key-value pair
with custom key names, allowing for structured data composition without merging.
"""

import copy
import time
import logging
from typing import ClassVar, List, Dict, Any

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    IntInput,
    InputVisibilityRule,
    Output,
)
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

logger = logging.getLogger(__name__)


class DataComposeComponent(BaseNode):
    """
    Composes multiple data structures into a structured dictionary.

    This component takes a main input and a configurable number of additional inputs
    and creates a new dictionary where each input becomes a separate key-value pair
    with custom key names. Unlike merge operations, this preserves each input as a
    distinct entry in the output structure.
    """

    name: ClassVar[str] = "DataComposeComponent"
    display_name: ClassVar[str] = "Compose Data"
    description: ClassVar[str] = "Composes multiple data structures into a structured dictionary with custom keys."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Package"

    # Define a maximum number of additional inputs to support
    MAX_ADDITIONAL_INPUTS = 10
    DEFAULT_ADDITIONAL_INPUTS = 2

    # Generate the inputs dynamically
    inputs: ClassVar[List[InputBase]] = [
        # Main input - dual purpose input
        create_dual_purpose_input(
            name="main_input",
            display_name="Main Input",
            input_type="dict",
            required=True,
            info="The main data structure to compose. Can be connected from another node or entered directly.",
            input_types=["dict", "list", "Any"],
        ),
        # Number of additional inputs to show
        IntInput(
            name="num_additional_inputs",
            display_name="Number of Additional Inputs",
            value=DEFAULT_ADDITIONAL_INPUTS,
            info=f"Set the number of additional inputs to show (0-{MAX_ADDITIONAL_INPUTS}).",
        ),
    ]

    # Add dynamic output key inputs
    # We need output keys for: main_input + all additional inputs (up to MAX_ADDITIONAL_INPUTS)
    # Note: Visibility logic is handled entirely in the frontend (inputVisibility.ts)
    for i in range(1, MAX_ADDITIONAL_INPUTS + 2):  # +2 because we need keys for main_input + all additional inputs
        # Add output key input (no visibility rules - frontend handles all visibility logic)
        inputs.append(
            create_dual_purpose_input(
                name=f"output_key_{i}",
                display_name=f"Output Key {i}",
                input_type="string",
                required=False,
                info=f"Custom key name for input {i} (e.g., 'data_{i}').",
                input_types=["string"],
                # No visibility_rules - frontend handles complex visibility logic
            )
        )

    # Add additional input fields dynamically
    for i in range(1, MAX_ADDITIONAL_INPUTS + 1):
        # Create visibility rules - show if num_additional_inputs is >= i
        # Since we don't have a "greater than or equal" operator, we create a rule for each possible value
        visibility_rules = [
            InputVisibilityRule(field_name="num_additional_inputs", field_value=j)
            for j in range(i, MAX_ADDITIONAL_INPUTS + 1)
        ]

        # Add dual purpose input
        inputs.append(
            create_dual_purpose_input(
                name=f"input_{i}",
                display_name=f"Input {i}",
                input_type="dict",
                required=False,
                info=f"Data structure {i} to compose. Can be connected from another node or entered directly.",
                input_types=["dict", "list", "Any"],
                visibility_rules=visibility_rules,
            )
        )

    # Define outputs
    outputs: ClassVar[List[Output]] = [
        Output(
            name="output_data",
            display_name="Composed Data",
            output_type="dict",
        )
    ]

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the workflow context.

        This method first checks if there's a direct value in the node configuration,
        then checks if there's a connected value from another node's output.

        Args:
            input_name: The name of the input to get
            context: The workflow execution context
            default: Default value if input is not found

        Returns:
            The input value or default if not found
        """
        # Get the node ID from context
        node_id = context.current_node_id

        # First check if there's a direct value in the node configuration
        node_config = context.workflow_data.get("nodes", {}).get(node_id, {}).get("data", {}).get("config", {})
        if input_name in node_config:
            value = node_config[input_name]
            if value is not None and value != "":
                return value

        # Check if there's a value in the node outputs
        node_outputs = context.node_outputs.get(node_id, {})
        if input_name in node_outputs:
            return node_outputs[input_name]

        # If not found, return the default
        return default

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the DataComposeComponent.

        This method composes multiple data structures into a structured dictionary
        with custom key names for each input.

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs from context
            main_input = self.get_input_value("main_input", context)
            num_additional_inputs = min(
                int(self.get_input_value("num_additional_inputs", context, self.DEFAULT_ADDITIONAL_INPUTS)),
                self.MAX_ADDITIONAL_INPUTS
            )

            # Log input values for debugging
            logger.debug(f"Main input type: {type(main_input).__name__}")
            logger.debug(f"Number of additional inputs: {num_additional_inputs}")

            # Validate main input
            if main_input is None:
                error_msg = "Main input is missing. Please connect data to compose."
                context.log(f"  {error_msg}")
                return NodeResult.error(error_msg, time.time() - start_time)

            # Create a new dictionary with custom keys
            result = {}

            # Add main input with first key
            output_key_1 = self.get_input_value("output_key_1", context, "data_1")
            result[output_key_1] = copy.deepcopy(main_input)
            context.log(f"  Added main input as '{output_key_1}'")

            # Add additional inputs with their respective keys
            for i in range(1, num_additional_inputs + 1):
                input_name = f"input_{i}"
                input_value = self.get_input_value(input_name, context)

                if input_value is not None:
                    # Get the corresponding output key (output_key_2 for input_1, output_key_3 for input_2, etc.)
                    output_key_name = f"output_key_{i + 1}"
                    output_key_value = self.get_input_value(output_key_name, context, f"data_{i + 1}")

                    result[output_key_value] = copy.deepcopy(input_value)
                    context.log(f"  Added {input_name} as '{output_key_value}'")

            context.log(f"  Data composition completed with keys: {list(result.keys())}")
            return NodeResult.success(
                {"output_data": result},
                time.time() - start_time
            )

        except Exception as e:
            error_msg = f"Error executing {self.name}: {str(e)}"
            context.log(f"  {error_msg}")
            logger.exception(f"Error in {self.name}: {e}")
            return NodeResult.error(error_msg, time.time() - start_time)

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the DataComposeComponent.

        DEPRECATED: This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values

        Returns:
            A dictionary with the component's outputs
        """
        logger.warning(
            f"Using legacy build method for {self.name}. Please update to use execute method."
        )

        try:
            # Get inputs
            main_input = kwargs.get("main_input")
            num_additional_inputs = min(
                int(kwargs.get("num_additional_inputs", self.DEFAULT_ADDITIONAL_INPUTS)),
                self.MAX_ADDITIONAL_INPUTS
            )

            # Validate main input
            if main_input is None:
                return {"error": "Main input is missing. Please connect data to compose."}

            # Create a new dictionary with custom keys
            result = {}

            # Add main input with first key
            output_key_1 = kwargs.get("output_key_1", "data_1")
            result[output_key_1] = copy.deepcopy(main_input)
            print(f"  Added main input as '{output_key_1}'")

            # Add additional inputs with their respective keys
            for i in range(1, num_additional_inputs + 1):
                input_name = f"input_{i}"
                input_value = kwargs.get(input_name)

                if input_value is not None:
                    # Get the corresponding output key (output_key_2 for input_1, output_key_3 for input_2, etc.)
                    output_key_name = f"output_key_{i + 1}"
                    output_key_value = kwargs.get(output_key_name, f"data_{i + 1}")

                    result[output_key_value] = copy.deepcopy(input_value)
                    print(f"  Added {input_name} as '{output_key_value}'")

            print(f"  Data composition completed with keys: {list(result.keys())}")
            return {"output_data": result}

        except Exception as e:
            error_msg = f"Error in {self.name}: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
