// Debug script to reproduce the array type issue
const fs = require('fs');
const path = require('path');

// Load the saved config to get real array schema examples
const savedConfigPath = path.join(__dirname, 'saved_config.json');
const savedConfig = JSON.parse(fs.readFileSync(savedConfigPath, 'utf8'));

// Extract array type examples from the config
function findArrayTypes(obj, path = '') {
  const results = [];
  
  if (typeof obj === 'object' && obj !== null) {
    if (obj.type === 'array') {
      results.push({ path, schema: obj });
    }
    
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        results.push(...findArrayTypes(value, path ? `${path}.${key}` : key));
      }
    }
  }
  
  return results;
}

// Find all array types in the config
const arrayTypes = findArrayTypes(savedConfig);

console.log('🔍 Found array types in config:');
arrayTypes.forEach((item, index) => {
  console.log(`${index + 1}. Path: ${item.path}`);
  console.log(`   Schema:`, JSON.stringify(item.schema, null, 2));
  console.log('');
});

// Test the type mapping logic directly
console.log('\n🧪 Testing type mapping logic:');

// Simulate the mapJsonSchemaTypeToInputType function
function testMapJsonSchemaTypeToInputType(schemaType, schema) {
  console.log(`\n📝 Testing: schemaType="${schemaType}", schema=`, JSON.stringify(schema, null, 2));
  
  // This is what happens in the actual code
  try {
    // The dynamic mapper would be called here
    // Let's simulate what should happen based on the DynamicInputTypeMapper
    
    // Create lookup key
    const key = schema.format ? `${schemaType}:${schema.format}` : schemaType;
    console.log(`   🔑 Lookup key: "${key}"`);
    
    // Check if it's array type
    if (schemaType === 'array') {
      console.log(`   ✅ Should map to: "array" (ArrayInput component)`);
      return 'array';
    }
    
    // Emergency fallback
    const typeMap = {
      string: "string",
      number: "number", 
      integer: "number",
      boolean: "boolean",
      object: "object",
      array: "array", // This should work
    };
    
    const result = typeMap[schemaType] || "string";
    console.log(`   📋 Emergency fallback result: "${result}"`);
    return result;
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return "string";
  }
}

// Test with the actual array examples from config
console.log('\n🎯 Testing with real config examples:');
arrayTypes.slice(0, 3).forEach((item, index) => {
  console.log(`\n--- Example ${index + 1}: ${item.path} ---`);
  const result = testMapJsonSchemaTypeToInputType(item.schema.type, item.schema);
  console.log(`   🎯 Final result: "${result}"`);
});

// Test the getBaseType function from DynamicInputRenderer
console.log('\n🔧 Testing getBaseType function:');
function testGetBaseType(inputType) {
  console.log(`\n📝 Testing getBaseType("${inputType}")`);
  
  // Handle compound types like "string:uri", "string:email", etc.
  if (inputType.includes(':')) {
    const result = inputType.split(':')[0];
    console.log(`   🔑 Compound type detected, base: "${result}"`);
    return result;
  }
  
  // Map common variations to standard types
  const typeMap = {
    'int': 'integer',
    'float': 'number',
    'bool': 'boolean',
    'dict': 'object',
    'json': 'object',
    'list': 'array',
    'url': 'string',
    'email': 'string',
    'password': 'string',
    'multiline': 'string',
    'code': 'string',
    'dropdown': 'string',
    'select': 'string',
  };
  
  const result = typeMap[inputType] || inputType;
  console.log(`   📋 TypeMap result: "${result}"`);
  return result;
}

// Test common cases
['array', 'list', 'string', 'object', 'dropdown'].forEach(type => {
  testGetBaseType(type);
});

console.log('\n🎯 Summary:');
console.log('- Array types found in config: ✅');
console.log('- Type mapping logic should work: ✅');
console.log('- Emergency fallback includes array: ✅');
console.log('- getBaseType handles array correctly: ✅');
console.log('\n💡 The issue might be in:');
console.log('1. The dynamic mapper import/require failing');
console.log('2. Component selection logic in DynamicInputRenderer');
console.log('3. The shouldUseDynamicInputSystem returning false');
console.log('4. Input type being overridden somewhere else');