#!/usr/bin/env python3
"""
Simple test to demonstrate the validation logic fix for conditional component.

This script demonstrates the logical fix without requiring the full environment.
"""

def test_validation_logic():
    """Test the validation logic for conditional operators."""
    
    print("Conditional Component Validation Logic Test")
    print("=" * 50)
    
    # Simulated conditions as they would come from a request
    test_conditions = [
        {
            "name": "is_empty with no expected_value",
            "condition": {
                "operator": "is_empty",
                "next_transition": "empty_branch"
                # No expected_value provided
            },
            "expected_validation": "PASS",
            "reason": "is_empty operator doesn't need expected_value"
        },
        {
            "name": "exists with no expected_value", 
            "condition": {
                "operator": "exists",
                "next_transition": "exists_branch"
                # No expected_value provided
            },
            "expected_validation": "PASS",
            "reason": "exists operator doesn't need expected_value"
        },
        {
            "name": "equals with no expected_value",
            "condition": {
                "operator": "equals",
                "next_transition": "equals_branch"
                # No expected_value provided
            },
            "expected_validation": "FAIL",
            "reason": "equals operator requires expected_value"
        },
        {
            "name": "equals with expected_value",
            "condition": {
                "operator": "equals",
                "expected_value": "test_value",
                "next_transition": "equals_branch"
            },
            "expected_validation": "PASS",
            "reason": "equals operator has required expected_value"
        }
    ]
    
    def validate_condition(condition):
        """Simulate the fixed validation logic."""
        operator = condition.get("operator")
        expected_value = condition.get("expected_value")
        
        # List of operators that don't require expected_value
        no_expected_value_operators = ["exists", "is_empty"]
        
        # List of operators that require expected_value
        expected_value_operators = ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than"]
        
        if operator in no_expected_value_operators:
            # These operators don't require expected_value
            return True, "Valid: operator doesn't require expected_value"
        elif operator in expected_value_operators:
            # These operators require expected_value
            if expected_value is None:
                return False, f"Invalid: operator '{operator}' requires expected_value"
            return True, "Valid: operator has required expected_value"
        else:
            return False, f"Invalid: unknown operator '{operator}'"
    
    def apply_operator_logic(operator, actual_value, expected_value):
        """Simulate the operator application logic."""
        try:
            if operator == "equals":
                return actual_value == expected_value
            elif operator == "not_equals":
                return actual_value != expected_value
            elif operator == "contains":
                return str(expected_value) in str(actual_value) if actual_value is not None else False
            elif operator == "starts_with":
                return str(actual_value).startswith(str(expected_value)) if actual_value is not None else False
            elif operator == "ends_with":
                return str(actual_value).endswith(str(expected_value)) if actual_value is not None else False
            elif operator == "greater_than":
                return float(actual_value) > float(expected_value)
            elif operator == "less_than":
                return float(actual_value) < float(expected_value)
            elif operator == "exists":
                return actual_value is not None
            elif operator == "is_empty":
                return actual_value is None or actual_value == "" or actual_value == [] or actual_value == {}
            else:
                return False
        except Exception:
            return False
    
    # Run tests
    for i, test_case in enumerate(test_conditions, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Condition: {test_case['condition']}")
        print(f"Expected: {test_case['expected_validation']}")
        print(f"Reason: {test_case['reason']}")
        
        is_valid, message = validate_condition(test_case['condition'])
        
        if test_case['expected_validation'] == "PASS" and is_valid:
            print(f"✅ RESULT: PASS - {message}")
        elif test_case['expected_validation'] == "FAIL" and not is_valid:
            print(f"✅ RESULT: FAIL (Expected) - {message}")
        else:
            print(f"❌ RESULT: Unexpected outcome - {message}")
    
    # Test operator logic for is_empty
    print(f"\n{'='*50}")
    print("Testing is_empty Operator Logic")
    print(f"{'='*50}")
    
    test_values = [
        {"value": "", "expected": True, "description": "empty string"},
        {"value": None, "expected": True, "description": "None value"},
        {"value": [], "expected": True, "description": "empty list"},
        {"value": {}, "expected": True, "description": "empty dict"},
        {"value": "not empty", "expected": False, "description": "non-empty string"},
        {"value": [1, 2, 3], "expected": False, "description": "non-empty list"},
        {"value": {"key": "value"}, "expected": False, "description": "non-empty dict"}
    ]
    
    for test_value in test_values:
        result = apply_operator_logic("is_empty", test_value["value"], None)
        status = "✅" if result == test_value["expected"] else "❌"
        print(f"{status} is_empty({test_value['description']}): {result} (expected: {test_value['expected']})")

if __name__ == "__main__":
    test_validation_logic()