/**
 * Debug script for testing conditional component visibility rules
 * This simulates the frontend visibility logic to understand why expected_value
 * fields are showing up when operator is "is_empty"
 */

// Mock the visibility rules evaluation function
function evaluateVisibilityRule(rule, config) {
  console.log(`[DEBUG] Evaluating rule:`, {
    field_name: rule.field_name,
    field_value: rule.field_value,
    operator: rule.operator,
    actual_value: config[rule.field_name]
  });

  const targetValue = config[rule.field_name];
  const operator = rule.operator || "equals";

  if (operator === "not_equals") {
    const result = targetValue !== rule.field_value;
    console.log(`[DEBUG] not_equals result: ${targetValue} !== ${rule.field_value} = ${result}`);
    return result;
  }

  return false;
}

function evaluateVisibilityRules(rules, config, logicOperator = "OR") {
  if (!rules || rules.length === 0) return true;

  const results = rules.map(rule => evaluateVisibilityRule(rule, config));
  console.log(`[DEBUG] Individual rule results:`, results, `Logic: ${logicOperator}`);

  if (logicOperator === "AND") {
    return results.every(r => r);
  } else {
    return results.some(r => r);
  }
}

function shouldShowInput(inputDef, config) {
  if (!inputDef.visibility_rules || inputDef.visibility_rules.length === 0) {
    return true;
  }

  const logicOperator = inputDef.visibility_logic || "OR";
  return evaluateVisibilityRules(inputDef.visibility_rules, config, logicOperator);
}

// Test case 1: is_empty operator should hide expected_value
console.log("\n=== TEST 1: is_empty operator should hide expected_value ===");
const inputDef1 = {
  name: "condition_1_expected_value",
  display_name: "Condition 1 - Expected Value",
  visibility_rules: [
    {
      field_name: "condition_1_operator",
      field_value: "exists",
      operator: "not_equals"
    },
    {
      field_name: "condition_1_operator", 
      field_value: "is_empty",
      operator: "not_equals"
    }
  ],
  visibility_logic: "AND"
};

const config1 = {
  condition_1_operator: "is_empty"
};

const shouldShow1 = shouldShowInput(inputDef1, config1);
console.log(`[RESULT] Should show expected_value with is_empty operator: ${shouldShow1} (expected: false)`);

// Test case 2: equals operator should show expected_value
console.log("\n=== TEST 2: equals operator should show expected_value ===");
const config2 = {
  condition_1_operator: "equals"
};

const shouldShow2 = shouldShowInput(inputDef1, config2);
console.log(`[RESULT] Should show expected_value with equals operator: ${shouldShow2} (expected: true)`);

// Test case 3: exists operator should hide expected_value
console.log("\n=== TEST 3: exists operator should hide expected_value ===");
const config3 = {
  condition_1_operator: "exists"
};

const shouldShow3 = shouldShowInput(inputDef1, config3);
console.log(`[RESULT] Should show expected_value with exists operator: ${shouldShow3} (expected: false)`);

console.log("\n=== SUMMARY ===");
console.log(`Test 1 (is_empty): ${shouldShow1 ? 'FAIL' : 'PASS'}`);
console.log(`Test 2 (equals): ${shouldShow2 ? 'PASS' : 'FAIL'}`);  
console.log(`Test 3 (exists): ${shouldShow3 ? 'FAIL' : 'PASS'}`);