"""
Integration Management Schemas

This module defines Pydantic schemas for integration management operations.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class IntegrationStatus(str, Enum):
    """Integration status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    ERROR = "error"


class IntegrationType(str, Enum):
    """Integration type enumeration."""
    OAUTH = "oauth"
    API_KEY = "api_key"
    WEBHOOK = "webhook"
    CUSTOM = "custom"


class IntegrationBase(BaseModel):
    """Base integration schema."""
    name: str = Field(..., description="Integration name")
    display_name: str = Field(..., description="Display name for the integration")
    description: Optional[str] = Field(None, description="Integration description")
    integration_type: IntegrationType = Field(..., description="Type of integration")
    provider: str = Field(..., description="OAuth provider or service name")
    is_enabled: bool = Field(True, description="Whether the integration is enabled")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Integration configuration")
    supported_scopes: List[str] = Field(default_factory=list, description="Supported OAuth scopes")


class IntegrationCreate(IntegrationBase):
    """Schema for creating a new integration."""
    pass


class IntegrationUpdate(BaseModel):
    """Schema for updating an integration."""
    display_name: Optional[str] = Field(None, description="Display name for the integration")
    description: Optional[str] = Field(None, description="Integration description")
    is_enabled: Optional[bool] = Field(None, description="Whether the integration is enabled")
    configuration: Optional[Dict[str, Any]] = Field(None, description="Integration configuration")
    supported_scopes: Optional[List[str]] = Field(None, description="Supported OAuth scopes")


class IntegrationResponse(IntegrationBase):
    """Schema for integration response."""
    id: str = Field(..., description="Integration ID")
    status: IntegrationStatus = Field(..., description="Integration status")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    created_by: str = Field(..., description="User who created the integration")


class IntegrationListResponse(BaseModel):
    """Schema for integration list response with pagination."""
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Response message")
    integrations: List[IntegrationResponse] = Field(..., description="List of integrations")
    total: int = Field(..., description="Total number of integrations")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")


class IntegrationDeleteResponse(BaseModel):
    """Schema for integration deletion response."""
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Response message")

class UserIntegrationStatus(BaseModel):
    """
    Status of a user's integration connection.
    """
    
    user_id: str = Field(..., example="user_123")
    integration_id: str = Field(..., example="550e8400-e29b-41d4-a716-446655440000")
    integration_name: str = Field(..., example="Google Calendar")
    is_connected: bool = Field(..., example=True)
    last_used_at: Optional[str] = Field(None, example="2023-01-03T12:00:00")
    created_at: str = Field(..., example="2023-01-01T12:00:00")
    scopes: List[str] = Field(default_factory=list, example=["https://www.googleapis.com/auth/calendar"])


class UserIntegrationsListResponse(BaseModel):
    """
    Response model for listing user's connected integrations.
    """
    
    success: bool = Field(..., example=True)
    message: str = Field(..., example="User integrations retrieved successfully")
    integrations: List[UserIntegrationStatus] = Field(..., description="List of user's integrations")


class OAuthCredentialResponse(BaseModel):
    """
    Response model for retrieving stored OAuth credentials.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credentials retrieved successfully")
    access_token: str = Field(..., example="ya29.a0AfH6SMB...")
    token_type: str = Field(default="Bearer", example="Bearer")
    expires_in: int = Field(
        ..., example=3599, description="Duration in seconds for which the access token is valid"
    )
    scope: str = Field(
        ...,
        example="https://www.googleapis.com/auth/calendar",
        description="Scope of the access granted",
    )


class OAuthCredentialDeleteResponse(BaseModel):
    """
    Response model for deleting an OAuth credential.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credential deleted successfully")


class UserIntegrationsListResponse(BaseModel):
    """
    Response model for listing user's connected integrations.
    """
    
    success: bool = Field(..., example=True)
    message: str = Field(..., example="User integrations retrieved successfully")
    integrations: List[UserIntegrationStatus] = Field(..., description="List of user's integrations")
